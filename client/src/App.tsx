import { Switch, Route } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "./pages/not-found";
import Header from "./components/Header";
import Footer from "./components/Footer";
import Home from "./pages/Home";
import Services from "./pages/Services";
import AISolutions from "./pages/AISolutions";
import About from "./pages/About";
import Contact from "./pages/Contact";
import ChatUI from "./components/ChatUI";
import { usePerformanceMonitor } from "./hooks/usePerformanceMonitor";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/services" component={Services} />
      <Route path="/ai-solutions" component={AISolutions} />
      <Route path="/about" component={About} />
      <Route path="/contact" component={Contact} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  // Monitor performance metrics in development
  usePerformanceMonitor();

  return (
    <TooltipProvider>
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="flex-grow">
          <Router />
        </main>
        <Footer />
        <ChatUI /> {/* AI Chatbot for instant service inquiries */}
      </div>
      <Toaster />
    </TooltipProvider>
  );
}

export default App;
