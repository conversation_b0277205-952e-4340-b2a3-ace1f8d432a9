import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { ThemeProvider } from "@/components/ui/theme-provider";
import App from "./App";
import "./index.css";

const container = document.getElementById("root");
if (!container) throw new Error("Could not find root element with id 'root'");
const root = createRoot(container);

root.render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="system" storageKey="mp-theme">
        <App />
      </ThemeProvider>
    </QueryClientProvider>
  </StrictMode>
);