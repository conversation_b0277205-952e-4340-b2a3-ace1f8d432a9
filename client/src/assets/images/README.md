# MP Advance Solutions - Logo Assets

This directory contains the logo assets for MP Advance Solutions.

## Logo Files

### Primary Logo
- `mp-advabce-solutions-logo.svg` - Primary logo in SVG format (recommended for web use)
- `mp-advabce-solutions-logo.png` - Primary logo in PNG format (automatic fallback)
- `mp-advance-logo-white.svg` - White version for dark backgrounds (future use)
- `mp-advance-logo-white.png` - White version in PNG format (future use)

### Logo Variations
- `mp-advance-icon.svg` - Icon-only version (for favicons, small spaces)
- `mp-advance-icon.png` - Icon-only version in PNG format

## Usage Guidelines

### File Formats
- **SVG**: Use for web applications (scalable, smaller file size)
- **PNG**: Use for fallback or when SVG is not supported

### Naming Convention
- Use kebab-case for file names
- Include format in the filename
- Use descriptive names (e.g., `-white`, `-dark`, `-icon`)

### Implementation
Use the Logo component for automatic fallback support:
```jsx
import Logo from '@/components/Logo';

// Basic usage
<Logo />

// With custom sizing
<Logo className="h-8 w-auto" />

// With custom alt text
<Logo className="h-10 w-auto" alt="Company Logo" />

// White logo for dark backgrounds
<LogoWhite className="h-8 w-auto" />
```

Direct import (not recommended - no fallback):
```jsx
import MPAdvanceLogo from '@/assets/images/mp-advabce-solutions-logo.svg';
import MPAdvanceLogoPNG from '@/assets/images/mp-advabce-solutions-logo.png';
```

### Current Usage Locations
- Header component: Main navigation logo
- Footer component: Footer branding
- Favicon: Browser tab icon
- Chat UI: Assistant branding

## File Size Recommendations
- SVG files: Keep under 50KB
- PNG files: Optimize for web (use appropriate compression)
- Icon files: 32x32, 64x64, 128x128, 256x256 px versions

## Brand Colors
- Primary: #your-primary-color
- Secondary: #your-secondary-color
- White: #ffffff
- Dark: #your-dark-color