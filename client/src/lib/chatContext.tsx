import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiRequest } from './queryClient';

// Define the types for our chat messages
export type ChatMessage = {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  category?: string;
};

// Define the type for our chat context
type ChatContextType = {
  messages: ChatMessage[];
  isLoading: boolean;
  isChatOpen: boolean;
  sendMessage: (message: string) => Promise<void>;
  openChat: () => void;
  closeChat: () => void;
  resetChat: () => void;
};

// Create the chat context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Generate a unique ID for messages
const generateId = () => `msg_${Math.random().toString(36).substr(2, 9)}`;

// Initial welcome message
const welcomeMessage: ChatMessage = {
  id: generateId(),
  role: 'assistant',
  content: 'Hi there! I\'m the <span class="company-name">MP Advance Solutions</span> virtual assistant. How can I help with your business technology needs today?',
  timestamp: new Date(),
};

// Props for the ChatProvider component
interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  // State for chat messages
  const [messages, setMessages] = useState<ChatMessage[]>([welcomeMessage]);
  
  // State for loading status during API calls
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  // State for chat window visibility
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);
  
  // Function to send a message to the AI
  const sendMessage = async (message: string) => {
    if (!message.trim()) return;
    
    // Create a new user message
    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content: message,
      timestamp: new Date(),
    };
    
    // Update messages with user message
    setMessages(prev => [...prev, userMessage]);
    
    // Set loading state
    setIsLoading(true);
    
    try {
      // Prepare chat history for API
      const chatHistory = messages
        .filter(msg => msg.role !== 'system') // Exclude system messages
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }));
      
      // Call API endpoint
      const response = await apiRequest<{ response: string; category: string }>('/api/chatbot', {
        method: 'POST',
        data: {
          message,
          chatHistory
        }
      });
      
      // Create assistant message from response
      const assistantMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: response.response,
        timestamp: new Date(),
        category: response.category,
      };
      
      // Update messages with assistant response
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message
      const errorMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: 'Sorry, I encountered an error processing your request. Please try again later or contact our support team.',
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Function to open chat window
  const openChat = () => setIsChatOpen(true);
  
  // Function to close chat window
  const closeChat = () => setIsChatOpen(false);
  
  // Function to reset chat
  const resetChat = () => {
    setMessages([welcomeMessage]);
  };
  
  // Load chat from localStorage on initial render
  useEffect(() => {
    const savedChat = localStorage.getItem('mp_advance_chat');
    if (savedChat) {
      try {
        const parsedChat = JSON.parse(savedChat);
        // Convert string dates back to Date objects
        const processedChat = parsedChat.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        setMessages(processedChat);
      } catch (e) {
        console.error('Error loading chat from storage:', e);
      }
    }
  }, []);
  
  // Save chat to localStorage when messages change
  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem('mp_advance_chat', JSON.stringify(messages));
    }
  }, [messages]);
  
  return (
    <ChatContext.Provider 
      value={{
        messages,
        isLoading,
        isChatOpen,
        sendMessage,
        openChat,
        closeChat,
        resetChat
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use the chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};