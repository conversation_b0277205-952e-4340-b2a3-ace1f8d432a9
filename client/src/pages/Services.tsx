import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ArrowRight, BrainCircuit, ShoppingCart, Megaphone, UserCheck, Settings, Workflow, BookOpen, MonitorSmartphone } from "lucide-react";

export default function Services() {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-slate-50 dark:bg-slate-900 py-16 md:py-24">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl md:text-5xl font-bold mb-6"
            >
              Our Business Solutions
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-lg text-muted-foreground mb-8"
            >
              <span className="company-name">MP Advance Solutions</span> offers comprehensive technology services tailored specifically for retail and wholesale businesses looking to innovate and grow.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Services Navigation */}
      <section className="py-12">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="ai-solutions" className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList className="grid grid-cols-2 md:grid-cols-4 w-full max-w-4xl">
                <TabsTrigger value="ai-solutions">AI Solutions</TabsTrigger>
                <TabsTrigger value="ecommerce">Ecommerce</TabsTrigger>
                <TabsTrigger value="digital-marketing">Digital Marketing</TabsTrigger>
                <TabsTrigger value="leads-generation">Leads Generation</TabsTrigger>
                <TabsTrigger value="business-automation">Business Automation</TabsTrigger>
                <TabsTrigger value="business-process">Process Management</TabsTrigger>
                <TabsTrigger value="order-fulfillment">Order Fulfillment</TabsTrigger>
                <TabsTrigger value="digital-signage">Digital Signage</TabsTrigger>
              </TabsList>
            </div>

            {/* AI Solutions Tab */}
            <TabsContent value="ai-solutions" className="mt-8">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <BrainCircuit className="h-12 w-12 text-blue-500 mb-6" />
                  <h2 className="text-3xl font-bold mb-4">AI Solutions</h2>
                  <p className="text-muted-foreground mb-6">
                    Our AI solutions harness the power of artificial intelligence to help retail and wholesale businesses 
                    make smarter decisions, automate complex processes, and gain a competitive edge.
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-blue-600 dark:text-blue-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Predictive Analytics</h3>
                        <p className="text-muted-foreground text-sm">Forecast trends, anticipate customer behaviors, and make data-driven inventory decisions.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-blue-600 dark:text-blue-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Recommendation Engines</h3>
                        <p className="text-muted-foreground text-sm">Increase cross-selling and upselling with AI-powered product recommendations.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-blue-600 dark:text-blue-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">AI-Powered Chatbots</h3>
                        <p className="text-muted-foreground text-sm">Provide 24/7 customer support and streamline customer interactions at scale.</p>
                      </div>
                    </div>
                  </div>
                  <Link href="/contact">
                    <Button>
                      Get Started with AI
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="relative"
                >
                  <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border">
                    <img 
                      src="https://images.unsplash.com/photo-1626863905121-3b0c0ed7b61c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="AI technology visualization" 
                      className="w-full rounded-lg object-cover"
                      style={{ aspectRatio: "4/3" }}
                    />
                    <div className="mt-6">
                      <h3 className="text-xl font-bold mb-3">How Our AI Solutions Help</h3>
                      <ul className="space-y-2">
                        <li className="flex items-center">
                          <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>35% average increase in conversion rates</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Reduce operational costs by up to 25%</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Improve inventory forecasting accuracy by 40%</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-100/50 dark:bg-blue-900/20 rounded-full -z-10"></div>
                </motion.div>
              </div>
            </TabsContent>

            {/* Ecommerce Tab */}
            <TabsContent value="ecommerce" className="mt-8">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <ShoppingCart className="h-12 w-12 text-green-500 mb-6" />
                  <h2 className="text-3xl font-bold mb-4">Ecommerce Solutions</h2>
                  <p className="text-muted-foreground mb-6">
                    We build powerful ecommerce platforms that help retail and wholesale businesses establish a strong online presence and increase sales.
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-green-600 dark:text-green-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Custom Ecommerce Websites</h3>
                        <p className="text-muted-foreground text-sm">Tailored online stores that reflect your brand identity and business needs.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-green-600 dark:text-green-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Multi-channel Integration</h3>
                        <p className="text-muted-foreground text-sm">Sell across marketplaces, social media, and your own website with unified inventory management.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-green-600 dark:text-green-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">B2B Ecommerce Platforms</h3>
                        <p className="text-muted-foreground text-sm">Specialized solutions for wholesale businesses with custom pricing, bulk ordering, and account management.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-green-600 dark:text-green-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Payment Processing</h3>
                        <p className="text-muted-foreground text-sm">Secure payment gateways and flexible payment options for customers.</p>
                      </div>
                    </div>
                  </div>
                  <Link href="/contact">
                    <Button>
                      Start Selling Online
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="relative"
                >
                  <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border">
                    <img 
                      src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="Ecommerce platform" 
                      className="w-full rounded-lg object-cover"
                      style={{ aspectRatio: "4/3" }}
                    />
                    <div className="mt-6">
                      <h3 className="text-xl font-bold mb-3">Why Choose Our Ecommerce Solutions</h3>
                      <ul className="space-y-2">
                        <li className="flex items-center">
                          <span className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Mobile-optimized shopping experiences</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Seamless integration with existing systems</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Scalable infrastructure that grows with your business</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-green-100/50 dark:bg-green-900/20 rounded-full -z-10"></div>
                </motion.div>
              </div>
            </TabsContent>

            {/* Digital Marketing Tab */}
            <TabsContent value="digital-marketing" className="mt-8">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Megaphone className="h-12 w-12 text-purple-500 mb-6" />
                  <h2 className="text-3xl font-bold mb-4">Digital Marketing</h2>
                  <p className="text-muted-foreground mb-6">
                    Our digital marketing strategies help retail and wholesale businesses reach their target audience, 
                    build brand awareness, and drive conversions across digital channels.
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-purple-600 dark:text-purple-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Search Engine Optimization (SEO)</h3>
                        <p className="text-muted-foreground text-sm">Improve your search engine rankings and drive organic traffic to your website.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-purple-600 dark:text-purple-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Pay-Per-Click Advertising</h3>
                        <p className="text-muted-foreground text-sm">Target potential customers with strategic ad campaigns on search engines and social media.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-purple-600 dark:text-purple-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Social Media Marketing</h3>
                        <p className="text-muted-foreground text-sm">Build and engage your community across social media platforms with strategic content.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-purple-600 dark:text-purple-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Email Marketing</h3>
                        <p className="text-muted-foreground text-sm">Nurture customer relationships and drive repeat business with personalized email campaigns.</p>
                      </div>
                    </div>
                  </div>
                  <Link href="/contact">
                    <Button>
                      Boost Your Digital Presence
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="relative"
                >
                  <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border">
                    <img 
                      src="https://images.unsplash.com/photo-1611926653458-09294b3142bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="Digital marketing strategy" 
                      className="w-full rounded-lg object-cover"
                      style={{ aspectRatio: "4/3" }}
                    />
                    <div className="mt-6">
                      <h3 className="text-xl font-bold mb-3">Our Marketing Approach</h3>
                      <ul className="space-y-2">
                        <li className="flex items-center">
                          <span className="bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Data-driven strategy development</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Comprehensive analytics and reporting</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Continuous optimization for maximum ROI</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-purple-100/50 dark:bg-purple-900/20 rounded-full -z-10"></div>
                </motion.div>
              </div>
            </TabsContent>

            {/* Leads Generation Tab */}
            <TabsContent value="leads-generation" className="mt-8">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <UserCheck className="h-12 w-12 text-orange-500 mb-6" />
                  <h2 className="text-3xl font-bold mb-4">Leads Generation</h2>
                  <p className="text-muted-foreground mb-6">
                    Our lead generation solutions help businesses identify and connect with potential
                    customers, nurturing them through the sales pipeline to generate more revenue.
                  </p>
                  <div className="space-y-4 mb-8">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-orange-600 dark:text-orange-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Lead Capture Systems</h3>
                        <p className="text-muted-foreground text-sm">Strategic lead forms, landing pages, and conversion-optimized content.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-orange-600 dark:text-orange-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Lead Qualification</h3>
                        <p className="text-muted-foreground text-sm">AI-powered scoring and segmentation to identify your most valuable prospects.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-orange-600 dark:text-orange-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">Lead Nurturing Campaigns</h3>
                        <p className="text-muted-foreground text-sm">Automated communication sequences that guide prospects toward purchase decisions.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1">
                        <span className="text-orange-600 dark:text-orange-300 text-sm font-semibold">✓</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">CRM Integration</h3>
                        <p className="text-muted-foreground text-sm">Seamless connection with your CRM system for efficient lead management.</p>
                      </div>
                    </div>
                  </div>
                  <Link href="/contact">
                    <Button>
                      Generate More Leads
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="relative"
                >
                  <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border">
                    <img 
                      src="https://images.unsplash.com/photo-1455849318743-b2233052fcff?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="Lead generation" 
                      className="w-full rounded-lg object-cover"
                      style={{ aspectRatio: "4/3" }}
                    />
                    <div className="mt-6">
                      <h3 className="text-xl font-bold mb-3">Lead Generation Results</h3>
                      <ul className="space-y-2">
                        <li className="flex items-center">
                          <span className="bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Increase qualified leads by up to 70%</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Reduce cost per acquisition by 30-40%</span>
                        </li>
                        <li className="flex items-center">
                          <span className="bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 p-1 rounded-full mr-2">
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                          <span>Shorten sales cycles with targeted nurturing</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-orange-100/50 dark:bg-orange-900/20 rounded-full -z-10"></div>
                </motion.div>
              </div>
            </TabsContent>

            {/* Other tabs would follow the same pattern... */}

          </Tabs>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-primary to-accent text-white py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Accelerate Your Business Growth?</h2>
            <p className="text-white/90 max-w-2xl mx-auto mb-8">
              Contact <span className="company-name">MP Advance Solutions</span> today to discuss how our technology solutions
              can help your retail or wholesale business thrive in the digital age.
            </p>
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="font-medium">
                Schedule a Consultation
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
}