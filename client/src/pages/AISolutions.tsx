import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, BrainCircuit, LineChart, Database, Bot, MessageSquare, Search, Sparkles, ShoppingBag } from "lucide-react";

export default function AISolutions() {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 py-20 md:py-28">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 px-4 py-1.5 rounded-full text-sm font-medium mb-6">
                Artificial Intelligence
              </span>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Transform Your Business with <span className="gradient-heading">AI Solutions</span>
              </h1>
              <p className="text-lg text-muted-foreground mb-8 max-w-xl">
                <span className="company-name">MP Advance Solutions</span> delivers cutting-edge AI technology that helps retail and wholesale
                businesses optimize operations, enhance customer experiences, and drive growth.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/contact">
                  <Button size="lg" className="w-full sm:w-auto">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="#ai-applications">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Explore Applications
                  </Button>
                </Link>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-border p-6 relative z-10">
                <BrainCircuit className="h-16 w-16 text-blue-500 mb-6" />
                <h2 className="text-2xl font-bold mb-4">AI-Powered Innovation</h2>
                <p className="text-muted-foreground mb-6">
                  Our AI solutions combine advanced machine learning, natural language processing, 
                  and computer vision to solve complex business challenges.
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center">
                    <h3 className="font-bold text-lg mb-1">40%</h3>
                    <p className="text-muted-foreground text-sm">Operational Efficiency Boost</p>
                  </div>
                  <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center">
                    <h3 className="font-bold text-lg mb-1">35%</h3>
                    <p className="text-muted-foreground text-sm">Sales Conversion Increase</p>
                  </div>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-200/50 dark:bg-blue-900/20 rounded-full -z-10"></div>
              <div className="absolute top-10 -left-6 w-24 h-24 bg-indigo-200/50 dark:bg-indigo-900/20 rounded-full -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* AI Applications Section */}
      <section id="ai-applications" className="py-20">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">AI Applications for Retail & Wholesale</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our specialized AI solutions are designed to address the specific challenges 
              and opportunities in the retail and wholesale sectors.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* AI Application Card 1 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="h-full">
                <CardContent className="pt-6">
                  <div className="bg-blue-100 dark:bg-blue-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                    <LineChart className="h-6 w-6 text-blue-600 dark:text-blue-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">Predictive Analytics</h3>
                  <p className="text-muted-foreground mb-5">
                    Leverage historical data to forecast trends, customer behaviors, and 
                    inventory needs with remarkable accuracy. Make smarter business decisions 
                    based on data-driven insights.
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Demand forecasting and inventory optimization</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Sales prediction and trend analysis</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span>Customer lifetime value estimation</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* AI Application Card 2 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="h-full">
                <CardContent className="pt-6">
                  <div className="bg-purple-100 dark:bg-purple-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                    <ShoppingBag className="h-6 w-6 text-purple-600 dark:text-purple-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">Personalized Recommendations</h3>
                  <p className="text-muted-foreground mb-5">
                    AI-powered recommendation engines that analyze customer behavior and 
                    preferences to suggest relevant products, increasing cross-selling 
                    and average order values.
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Product recommendation systems</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Personalized shopping experiences</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-purple-500 mr-2">•</span>
                      <span>Dynamic pricing optimization</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* AI Application Card 3 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="h-full">
                <CardContent className="pt-6">
                  <div className="bg-green-100 dark:bg-green-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                    <Bot className="h-6 w-6 text-green-600 dark:text-green-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">Intelligent Chatbots</h3>
                  <p className="text-muted-foreground mb-5">
                    AI-powered conversational agents that provide instant customer support, 
                    process orders, and answer product questions 24/7, enhancing customer 
                    satisfaction while reducing costs.
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">•</span>
                      <span>24/7 customer service automation</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">•</span>
                      <span>Order processing and tracking</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">•</span>
                      <span>Product information and recommendations</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* AI Application Card 4 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="h-full">
                <CardContent className="pt-6">
                  <div className="bg-amber-100 dark:bg-amber-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                    <Search className="h-6 w-6 text-amber-600 dark:text-amber-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">Visual Search & Recognition</h3>
                  <p className="text-muted-foreground mb-5">
                    Computer vision technology that enables customers to search for products 
                    using images, enhancing the shopping experience and making product 
                    discovery intuitive.
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-amber-500 mr-2">•</span>
                      <span>Image-based product search</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-amber-500 mr-2">•</span>
                      <span>Visual similarity recommendations</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-amber-500 mr-2">•</span>
                      <span>Automated product categorization</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* AI Application Card 5 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="h-full">
                <CardContent className="pt-6">
                  <div className="bg-red-100 dark:bg-red-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                    <Database className="h-6 w-6 text-red-600 dark:text-red-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">Inventory Management</h3>
                  <p className="text-muted-foreground mb-5">
                    AI systems that optimize inventory levels across multiple locations, 
                    predict stockouts, and automate reordering to minimize costs while 
                    ensuring product availability.
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-red-500 mr-2">•</span>
                      <span>Smart inventory forecasting</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-red-500 mr-2">•</span>
                      <span>Automated replenishment systems</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-red-500 mr-2">•</span>
                      <span>Multi-location inventory optimization</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            {/* AI Application Card 6 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="h-full">
                <CardContent className="pt-6">
                  <div className="bg-indigo-100 dark:bg-indigo-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                    <MessageSquare className="h-6 w-6 text-indigo-600 dark:text-indigo-300" />
                  </div>
                  <h3 className="text-xl font-bold mb-3">Customer Sentiment Analysis</h3>
                  <p className="text-muted-foreground mb-5">
                    NLP technology that analyzes customer reviews, social media mentions, and 
                    feedback to understand sentiment, identify issues, and improve your 
                    products and services.
                  </p>
                  <ul className="space-y-2 mb-6">
                    <li className="flex items-start">
                      <span className="text-indigo-500 mr-2">•</span>
                      <span>Review and feedback analysis</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-indigo-500 mr-2">•</span>
                      <span>Social media monitoring</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-indigo-500 mr-2">•</span>
                      <span>Brand reputation management</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* How We Work Section */}
      <section className="bg-slate-50 dark:bg-slate-900 py-20">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our AI Implementation Process</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              We follow a proven methodology to ensure successful AI integration that delivers 
              measurable business results for your retail or wholesale operation.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <span className="text-primary font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Discovery & Analysis</h3>
              <p className="text-muted-foreground">
                We begin by deeply understanding your business challenges, data availability, 
                and strategic objectives to identify the most impactful AI applications.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <span className="text-primary font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Solution Design</h3>
              <p className="text-muted-foreground">
                Our experts design a customized AI solution tailored to your specific needs, 
                creating a detailed roadmap for implementation and integration.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <span className="text-primary font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Implementation</h3>
              <p className="text-muted-foreground">
                We develop and deploy your AI solution, ensuring seamless integration with your 
                existing systems and thorough testing to validate performance.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border md:col-span-3 lg:col-span-1"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <span className="text-primary font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Training & Adoption</h3>
              <p className="text-muted-foreground">
                We provide comprehensive training for your team to ensure successful adoption 
                and maximum value from your new AI capabilities.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border md:col-start-2 lg:col-start-2"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <span className="text-primary font-bold text-xl">5</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Continuous Optimization</h3>
              <p className="text-muted-foreground">
                Our ongoing support ensures your AI solution continuously improves, adapting to 
                changing business conditions and leveraging new data.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-20">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <Sparkles className="h-12 w-12 mx-auto mb-6 text-white/80" />
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Business with AI?</h2>
            <p className="text-white/90 max-w-2xl mx-auto mb-8">
              Contact <span className="company-name">MP Advance Solutions</span> today to discuss how our AI solutions can help your
              retail or wholesale business stay competitive and drive growth.
            </p>
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="font-medium">
                Schedule a Consultation
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
}