import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, ExternalLink } from "lucide-react";
import CaseStudyCard from "@/components/CaseStudyCard";

// Sample case studies data
const caseStudies = [
  {
    id: "retail-ai-analytics",
    title: "AI-Powered Analytics for National Retail Chain",
    client: "RetailMax Corp",
    description: "Implemented advanced analytics and predictive AI solutions that increased sales by 28% and optimized inventory management across 200+ stores.",
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    industry: "Retail",
    date: "2023"
  },
  {
    id: "ecommerce-wholesale-platform",
    title: "B2B Ecommerce Platform for Wholesale Distributor",
    client: "Global Distribution Inc.",
    description: "Developed a custom B2B ecommerce platform that streamlined ordering processes, resulting in a 45% increase in online sales and improved client satisfaction.",
    image: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    industry: "Wholesale",
    date: "2023"
  },
  {
    id: "digital-marketing-campaign",
    title: "Integrated Digital Marketing Campaign",
    client: "Fashion Forward",
    description: "Created a comprehensive digital marketing strategy that generated over 10,000 qualified leads and increased conversion rates by 35% for an apparel retailer.",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    industry: "Fashion Retail",
    date: "2022"
  },
  {
    id: "business-process-automation",
    title: "End-to-End Business Process Automation",
    client: "Omega Supply Chain",
    description: "Implemented automation solutions that reduced order processing time by 75% and eliminated manual errors in a major supply chain operation.",
    image: "https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    industry: "Logistics",
    date: "2022"
  },
  {
    id: "digital-signage-network",
    title: "Interactive Digital Signage Network",
    client: "Metro Shopping Centers",
    description: "Deployed an AI-enhanced digital signage network across 15 shopping centers, increasing foot traffic by 22% and boosting tenant sales.",
    image: "https://images.unsplash.com/photo-1543269865-cbf427effbad?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    industry: "Retail Real Estate",
    date: "2022"
  },
  {
    id: "order-fulfillment-system",
    title: "Automated Order Fulfillment System",
    client: "QuickShip Logistics",
    description: "Designed and implemented an automated order fulfillment system that reduced processing times by 65% and improved accuracy to 99.8%.",
    image: "https://images.unsplash.com/photo-1507296327850-904cfd5e3175?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    industry: "Fulfillment Services",
    date: "2021"
  }
];

export default function CaseStudies() {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-slate-50 dark:bg-slate-900 py-16 md:py-24">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-3xl mx-auto">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl md:text-5xl font-bold mb-6"
            >
              Client Success Stories
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-lg text-muted-foreground mb-8"
            >
              Explore how <span className="company-name">MP Advance Solutions</span> has helped businesses across retail, wholesale,
              and related industries transform their operations and achieve remarkable growth.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Filter Section (could be expanded with functional filters) */}
      <section className="py-8 border-b border-border">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center">
              <span className="text-muted-foreground mr-4">Filter by:</span>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" className="rounded-full">All</Button>
                <Button variant="ghost" size="sm" className="rounded-full">AI Solutions</Button>
                <Button variant="ghost" size="sm" className="rounded-full">Ecommerce</Button>
                <Button variant="ghost" size="sm" className="rounded-full">Business Automation</Button>
                <Button variant="ghost" size="sm" className="rounded-full">Digital Signage</Button>
              </div>
            </div>
            <div>
              <select className="border border-border rounded-md p-2 bg-background text-foreground text-sm">
                <option>Most Recent</option>
                <option>Oldest First</option>
                <option>A-Z</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Case Studies Grid */}
      <section className="py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {caseStudies.map((caseStudy, index) => (
              <CaseStudyCard
                key={caseStudy.id}
                id={caseStudy.id}
                title={caseStudy.title}
                client={caseStudy.client}
                description={caseStudy.description}
                image={caseStudy.image}
                industry={caseStudy.industry}
                date={caseStudy.date}
                index={index}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Featured Case Study Section */}
      <section className="bg-slate-50 dark:bg-slate-900 py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <span className="inline-block bg-primary/10 text-primary px-4 py-1.5 rounded-full text-sm font-medium mb-6">
                Featured Case Study
              </span>
              <h2 className="text-3xl font-bold mb-4">
                AI-Driven Inventory Management Transformation
              </h2>
              <p className="text-muted-foreground mb-6">
                Learn how <span className="company-name">MP Advance Solutions</span> helped a leading retail chain implement an AI-powered
                inventory management system that reduced costs by 32% and improved product availability.
              </p>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mr-3 mt-1">
                    <span className="text-primary text-sm font-semibold">✓</span>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Reduced stockouts by 78% through predictive inventory management</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mr-3 mt-1">
                    <span className="text-primary text-sm font-semibold">✓</span>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Decreased excess inventory by 32%, freeing up working capital</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center mr-3 mt-1">
                    <span className="text-primary text-sm font-semibold">✓</span>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Automated replenishment process, saving 20 hours per week per store</p>
                  </div>
                </div>
              </div>
              
              <Link href="/contact">
                <Button>
                  Download Full Case Study
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border">
                <img 
                  src="https://images.unsplash.com/photo-1565375706940-30ce58a3d7b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                  alt="Retail inventory management" 
                  className="w-full rounded-lg object-cover mb-6"
                  style={{ aspectRatio: "16/9" }}
                />
                
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-1">Client</h3>
                    <p className="text-muted-foreground">NationalMart Retail Chain</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Industry</h3>
                    <p className="text-muted-foreground">Retail / Department Stores</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Solutions Implemented</h3>
                    <p className="text-muted-foreground">AI Analytics, Inventory Management, Business Process Automation</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">ROI</h3>
                    <p className="text-muted-foreground">326% ROI within first 12 months</p>
                  </div>
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-accent/10 rounded-full -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Testimonial Section */}
      <section className="py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">What Our Clients Say</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Don't just take our word for it. Here's what our clients have to say about working with <span className="company-name">MP Advance Solutions</span>.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="flex items-center mb-6">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  ))}
                </div>
              </div>
              <blockquote className="text-muted-foreground mb-6 italic">
                "<span className="company-name">MP Advance Solutions</span> transformed our ecommerce operations completely. Their team's expertise in AI and automation helped us increase our online sales by 45% in just three months."
              </blockquote>
              <div>
                <p className="font-semibold">Michael Rodriguez</p>
                <p className="text-muted-foreground text-sm">CTO, Global Distribution Inc.</p>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="flex items-center mb-6">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  ))}
                </div>
              </div>
              <blockquote className="text-muted-foreground mb-6 italic">
                "The digital marketing campaign <span className="company-name">MP Advance Solutions</span> created for us generated more qualified leads than we've ever seen. Their strategic approach and attention to detail is unmatched."
              </blockquote>
              <div>
                <p className="font-semibold">Sarah Johnson</p>
                <p className="text-muted-foreground text-sm">Marketing Director, Fashion Forward</p>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="flex items-center mb-6">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  ))}
                </div>
              </div>
              <blockquote className="text-muted-foreground mb-6 italic">
                "The business automation solutions provided by <span className="company-name">MP Advance Solutions</span> have completely revolutionized our operational efficiency. We've reduced processing times by 75% and eliminated costly errors."
              </blockquote>
              <div>
                <p className="font-semibold">David Chen</p>
                <p className="text-muted-foreground text-sm">Operations Manager, Omega Supply Chain</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-primary to-accent text-white py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Write Your Success Story?</h2>
            <p className="text-white/90 max-w-2xl mx-auto mb-8">
              Contact <span className="company-name">MP Advance Solutions</span> today to discuss how our technology solutions can transform
              your retail or wholesale business and deliver measurable results.
            </p>
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="font-medium">
                Start Your Project
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
}