import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Award, Briefcase, Building, CheckCircle, Clock, Users } from "lucide-react";
import LogoWhite from "@/components/LogoWhite";

export default function About() {
  return (
    <div>
      {/* Hero Section */}
      <section className="bg-slate-50 dark:bg-slate-900 py-16 md:py-24">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="inline-block bg-primary/10 text-primary px-4 py-1.5 rounded-full text-sm font-medium mb-6">
                Our Story
              </span>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Creating Advanced Solutions for Modern Businesses
              </h1>
              <p className="text-lg text-muted-foreground mb-8">
                <LogoWhite className="h-8 w-auto" /> is dedicated to helping retail and wholesale businesses thrive in the digital age with cutting-edge technology solutions.
              </p>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-border p-6 relative z-10">
                <img 
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                  alt="MP Advance Solutions team" 
                  className="w-full rounded-lg object-cover mb-6"
                  style={{ aspectRatio: "16/9" }}
                />
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center">
                    <h3 className="font-bold text-xl mb-1">10+</h3>
                    <p className="text-muted-foreground text-sm">Years of Experience</p>
                  </div>
                  <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center">
                    <h3 className="font-bold text-xl mb-1">500+</h3>
                    <p className="text-muted-foreground text-sm">Projects Delivered</p>
                  </div>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-primary/10 rounded-full -z-10"></div>
              <div className="absolute top-10 -left-6 w-24 h-24 bg-accent/10 rounded-full -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-8 border border-border"
            >
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <Award className="h-6 w-6 text-primary" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
              <p className="text-muted-foreground mb-4">
                To empower retail and wholesale businesses with cutting-edge technology solutions that 
                drive growth, efficiency, and innovation in an increasingly digital marketplace.
              </p>
              <p className="text-muted-foreground">
                We are committed to delivering custom-tailored solutions that address the unique 
                challenges and opportunities faced by our clients, ensuring they remain competitive 
                and successful in their industries.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-8 border border-border"
            >
              <div className="bg-accent/10 w-12 h-12 rounded-full flex items-center justify-center mb-6">
                <CheckCircle className="h-6 w-6 text-accent" />
              </div>
              <h2 className="text-2xl font-bold mb-4">Our Vision</h2>
              <p className="text-muted-foreground mb-4">
                To be the leading provider of innovative business technology solutions, 
                recognized for excellence in AI implementation, ecommerce development, and 
                business process optimization for retail and wholesale sectors.
              </p>
              <p className="text-muted-foreground">
                We aim to create a future where every business, regardless of size, can leverage 
                advanced technology to enhance customer experiences, streamline operations, and 
                achieve sustainable growth.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Company Values Section */}
      <section className="bg-slate-50 dark:bg-slate-900 py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Core Values</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              These principles guide everything we do at <span className="company-name">MP Advance Solutions</span> and define our approach to serving clients.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-blue-100 dark:bg-blue-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                <Building className="h-6 w-6 text-blue-600 dark:text-blue-300" />
              </div>
              <h3 className="text-xl font-bold mb-3">Innovation</h3>
              <p className="text-muted-foreground">
                We constantly explore new technologies and methodologies to provide our clients 
                with cutting-edge solutions that give them a competitive advantage.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-green-100 dark:bg-green-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                <Users className="h-6 w-6 text-green-600 dark:text-green-300" />
              </div>
              <h3 className="text-xl font-bold mb-3">Client Partnership</h3>
              <p className="text-muted-foreground">
                We view ourselves as partners in our clients' success, taking the time to understand 
                their business and working collaboratively to achieve their goals.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-purple-100 dark:bg-purple-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                <Briefcase className="h-6 w-6 text-purple-600 dark:text-purple-300" />
              </div>
              <h3 className="text-xl font-bold mb-3">Excellence</h3>
              <p className="text-muted-foreground">
                We are committed to delivering solutions of the highest quality, with meticulous 
                attention to detail and a focus on exceeding our clients' expectations.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border"
            >
              <div className="bg-amber-100 dark:bg-amber-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                <Clock className="h-6 w-6 text-amber-600 dark:text-amber-300" />
              </div>
              <h3 className="text-xl font-bold mb-3">Adaptability</h3>
              <p className="text-muted-foreground">
                We embrace change and remain flexible, allowing us to respond quickly to evolving 
                technologies and market conditions to benefit our clients.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-white dark:bg-slate-800 rounded-xl p-6 border border-border md:col-span-2 lg:col-span-1"
            >
              <div className="bg-red-100 dark:bg-red-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600 dark:text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Integrity</h3>
              <p className="text-muted-foreground">
                We conduct business with honesty, transparency, and ethical standards, building 
                trust and long-lasting relationships with our clients and partners.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Approach Section */}
      <section className="py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-6">Our Approach</h2>
              <p className="text-muted-foreground mb-6">
                At <span className="company-name">MP Advance Solutions</span>, we take a consultative and collaborative approach to every project,
                ensuring that our solutions are perfectly aligned with our clients' business objectives.
              </p>
              
              <div className="space-y-6 mb-8">
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Understand</h3>
                    <p className="text-muted-foreground">
                      We begin by thoroughly understanding your business, challenges, goals, and market position.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Design</h3>
                    <p className="text-muted-foreground">
                      We create a customized solution design that addresses your specific needs and objectives.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Implement</h3>
                    <p className="text-muted-foreground">
                      We execute the plan with precision, keeping you informed and involved throughout the process.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="mr-4 mt-1">
                    <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold">4</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Support</h3>
                    <p className="text-muted-foreground">
                      We provide ongoing support and optimization to ensure your solution continues to deliver value.
                    </p>
                  </div>
                </div>
              </div>
              
              <Link href="/contact">
                <Button>
                  Work With Us
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border">
                <img 
                  src="https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                  alt="Team collaboration" 
                  className="w-full rounded-lg object-cover mb-6"
                  style={{ aspectRatio: "4/3" }}
                />
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                      <div className="bg-primary h-2.5 rounded-full" style={{ width: '95%' }}></div>
                    </div>
                    <span className="ml-4 text-sm font-medium">Client Satisfaction</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                      <div className="bg-primary h-2.5 rounded-full" style={{ width: '90%' }}></div>
                    </div>
                    <span className="ml-4 text-sm font-medium">Project Delivery</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5">
                      <div className="bg-primary h-2.5 rounded-full" style={{ width: '98%' }}></div>
                    </div>
                    <span className="ml-4 text-sm font-medium">Support Response</span>
                  </div>
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-accent/10 rounded-full -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-primary to-accent text-white py-16">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Business?</h2>
            <p className="text-white/90 max-w-2xl mx-auto mb-8">
              Contact <span className="company-name">MP Advance Solutions</span> today to discuss how our technology solutions can help your
              retail or wholesale business thrive in the digital age.
            </p>
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="font-medium">
                Get in Touch
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
}