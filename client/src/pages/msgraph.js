const { Client } = require('@microsoft/microsoft-graph-client');
const { ConfidentialClientApplication } = require('@azure/msal-node');
require('dotenv').config();

class GraphEmailSender {
    constructor() {
        // Initialize MSAL instance
        this.msalConfig = {
            auth: {
                clientId: process.env.CLIENT_ID,
                clientSecret: process.env.CLIENT_SECRET,
                authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`
            }
        };
        
        this.cca = new ConfidentialClientApplication(this.msalConfig);
        this.graphClient = null;
    }

    async authenticate() {
        try {
            // Get access token using client credentials flow
            const clientCredentialRequest = {
                scopes: ['https://graph.microsoft.com/.default'],
            };

            const response = await this.cca.acquireTokenByClientCredential(clientCredentialRequest);
            const accessToken = response.accessToken;

            // Initialize Graph client with access token
            this.graphClient = Client.init({
                authProvider: (done) => {
                    done(null, accessToken);
                }
            });

            console.log('✅ Successfully authenticated with Microsoft Graph');
            return true;
        } catch (error) {
            console.error('❌ Authentication failed:', error.message);
            return false;
        }
    }

    async sendEmail(emailData) {
        if (!this.graphClient) {
            throw new Error('Not authenticated. Call authenticate() first.');
        }

        const email = {
            message: {
                subject: emailData.subject,
                body: {
                    contentType: 'HTML',
                    content: emailData.body
                },
                toRecipients: emailData.toRecipients.map(email => ({
                    emailAddress: {
                        address: email
                    }
                })),
                ccRecipients: emailData.ccRecipients ? emailData.ccRecipients.map(email => ({
                    emailAddress: {
                        address: email
                    }
                })) : [],
                importance: emailData.importance || 'normal'
            },
            saveToSentItems: true
        };

        try {
            await this.graphClient
                .api(`/users/${process.env.FROM_EMAIL}/sendMail`)
                .post(email);
            
            console.log('✅ Email sent successfully!');
            return true;
        } catch (error) {
            console.error('❌ Failed to send email:', error.message);
            return false;
        }
    }
}

async function main() {
    // Validate environment variables
    const requiredEnvVars = ['CLIENT_ID', 'CLIENT_SECRET', 'TENANT_ID', 'FROM_EMAIL', 'TO_EMAIL'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:', missingVars.join(', '));
        console.log('Please copy .env.example to .env and fill in the required values.');
        process.exit(1);
    }

    const emailSender = new GraphEmailSender();
    
    // Authenticate
    const authSuccess = await emailSender.authenticate();
    if (!authSuccess) {
        process.exit(1);
    }

    // Prepare email data
    const emailData = {
        subject: 'Test Email from Microsoft Graph API',
        body: `
            <h2>Hello from Microsoft Graph!</h2>
            <p>This is a test email sent using the Microsoft Graph Mail API.</p>
            <p><strong>Sent at:</strong> ${new Date().toLocaleString()}</p>
            <p>If you received this email, the integration is working correctly! 🎉</p>
        `,
        toRecipients: [process.env.TO_EMAIL],
        ccRecipients: [], // Optional: add CC recipients
        importance: 'normal' // Options: low, normal, high
    };

    // Send email
    const emailSuccess = await emailSender.sendEmail(emailData);
    
    if (emailSuccess) {
        console.log(`📧 Email sent from ${process.env.FROM_EMAIL} to ${process.env.TO_EMAIL}`);
    } else {
        console.log('❌ Email sending failed');
        process.exit(1);
    }
}

// Run the program
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Application error:', error);
        process.exit(1);
    });
}

module.exports = { GraphEmailSender };
