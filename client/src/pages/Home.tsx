import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowRight, 
  BrainCircuit, 
  ShoppingCart, 
  Megaphone, 
  UserCheck, 
  Settings, 
  Workflow, 
  Truck, 
  MonitorSmartphone,
  ChevronRight 
} from "lucide-react";
import HomeHero from "@/components/HomeHero";
import ServiceCard from "@/components/ServiceCard";

// Service cards data
const services = [
  {
    icon: BrainCircuit,
    iconColor: "text-blue-500",
    title: "AI Solutions",
    description: "Leverage advanced artificial intelligence to optimize operations, enhance customer experiences, and drive business growth.",
    link: "/ai-solutions"
  },
  {
    icon: ShoppingCart,
    iconColor: "text-green-500",
    title: "Ecommerce Solutions",
    description: "Build powerful online stores and marketplaces tailored to your retail or wholesale business needs.",
    link: "/services#ecommerce"
  },
  {
    icon: Megaphone,
    iconColor: "text-purple-500",
    title: "Digital Marketing",
    description: "Attract, engage, and convert customers with strategic digital marketing campaigns and analytics.",
    link: "/services#digital-marketing"
  },
  {
    icon: User<PERSON>he<PERSON>,
    iconColor: "text-orange-500",
    title: "Leads Generation",
    description: "Generate qualified leads and nurture prospects through automated, intelligent funnels.",
    link: "/services#leads-generation"
  },
  {
    icon: Settings,
    iconColor: "text-red-500",
    title: "Business Automation",
    description: "Automate repetitive tasks and streamline operations to increase efficiency and reduce costs.",
    link: "/services#business-automation"
  },
  {
    icon: Workflow,
    iconColor: "text-indigo-500",
    title: "Business Process Management",
    description: "Optimize workflows and business processes to enhance productivity and operational excellence.",
    link: "/services#business-process"
  },
  {
    icon: Truck,
    iconColor: "text-cyan-500",
    title: "Order Fulfillment",
    description: "Streamline your supply chain with efficient order processing, inventory management, and fulfillment solutions.",
    link: "/services#order-fulfillment"
  },
  {
    icon: MonitorSmartphone,
    iconColor: "text-amber-500",
    title: "Digital Signage",
    description: "Engage customers with dynamic digital displays and interactive signage solutions for retail environments.",
    link: "/services#digital-signage"
  }
];

// Case study previews
const caseStudyPreviews = [
  {
    title: "AI-Powered Analytics for Retail Chain",
    category: "Artificial Intelligence",
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    link: "/case-studies/retail-ai-analytics"
  },
  {
    title: "B2B Ecommerce Platform for Wholesale",
    category: "Ecommerce",
    image: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    link: "/case-studies/ecommerce-wholesale-platform"
  },
  {
    title: "Digital Marketing Campaign Results",
    category: "Digital Marketing",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    link: "/case-studies/digital-marketing-campaign"
  }
];

export default function Home() {
  return (
    <div>
      {/* Hero Section */}
      <HomeHero />
      
      {/* Services Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.span 
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-primary font-semibold tracking-wide uppercase"
            >
              Our Services
            </motion.span>
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mt-2 text-3xl md:text-4xl font-bold"
            >
              Business Technology Solutions
            </motion.h2>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="mt-4 text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              <span className="company-name">MP Advance Solutions</span> delivers comprehensive technology services tailored specifically for retail and wholesale businesses.
            </motion.p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <ServiceCard
                key={service.title}
                icon={service.icon}
                iconColor={service.iconColor}
                title={service.title}
                description={service.description}
                link={service.link}
                index={index}
              />
            ))}
          </div>
          
          <div className="mt-12 text-center">
            <Link href="/services">
              <Button size="lg">
                View All Services
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
      
      {/* Why Choose Us Section */}
      <section className="py-20 bg-slate-50 dark:bg-slate-800">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <span className="text-primary font-semibold tracking-wide uppercase">Why Choose Us</span>
              <h2 className="mt-2 text-3xl md:text-4xl font-bold">Expertise in Retail & Wholesale Technology</h2>
              <p className="mt-4 text-lg text-muted-foreground">
                <span className="company-name">MP Advance Solutions</span> brings specialized knowledge and experience to help retail and wholesale businesses navigate the digital landscape with confidence.
              </p>
              
              <div className="mt-8 space-y-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary">
                      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-xl font-bold">Specialized Industry Focus</h3>
                    <p className="mt-2 text-muted-foreground">
                      We exclusively serve retail and wholesale businesses, allowing us to develop deep industry expertise and tailored solutions.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary">
                      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-xl font-bold">End-to-End Solutions</h3>
                    <p className="mt-2 text-muted-foreground">
                      From strategy to implementation and ongoing support, we provide comprehensive services to address all your technology needs.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary">
                      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-xl font-bold">Expert Team</h3>
                    <p className="mt-2 text-muted-foreground">
                      Our team combines technical expertise with business acumen to deliver solutions that drive real results for your company.
                    </p>
                  </div>
                </div>
                
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary">
                      <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-xl font-bold">Measurable Results</h3>
                    <p className="mt-2 text-muted-foreground">
                      We focus on delivering quantifiable business outcomes that help you track and achieve your strategic objectives.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-white dark:bg-slate-900 rounded-xl shadow-lg p-8 border border-border relative z-10">
                <Tabs defaultValue="retail">
                  <TabsList className="grid w-full grid-cols-2 mb-8">
                    <TabsTrigger value="retail">Retail</TabsTrigger>
                    <TabsTrigger value="wholesale">Wholesale</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="retail">
                    <div className="relative aspect-video overflow-hidden rounded-lg mb-6">
                      <img 
                        src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                        alt="Retail technology solutions" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-2xl font-bold mb-4">Retail Solutions</h3>
                    <p className="text-muted-foreground mb-6">
                      Our retail technology solutions drive in-store and online sales, streamline operations, and deliver exceptional customer experiences.
                    </p>
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>Omnichannel shopping experiences</span>
                      </li>
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>AI-powered customer insights</span>
                      </li>
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>In-store digital innovations</span>
                      </li>
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>Smart inventory management</span>
                      </li>
                    </ul>
                    <Link href="/services">
                      <Button>Learn More <ArrowRight className="ml-2 h-4 w-4" /></Button>
                    </Link>
                  </TabsContent>
                  
                  <TabsContent value="wholesale">
                    <div className="relative aspect-video overflow-hidden rounded-lg mb-6">
                      <img 
                        src="https://images.unsplash.com/photo-1553413077-190dd305871c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                        alt="Wholesale technology solutions" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <h3 className="text-2xl font-bold mb-4">Wholesale Solutions</h3>
                    <p className="text-muted-foreground mb-6">
                      Our wholesale solutions optimize your distribution operations, enhance B2B relationships, and drive efficiency across your business.
                    </p>
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>B2B ecommerce platforms</span>
                      </li>
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>Supply chain optimization</span>
                      </li>
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>Order management systems</span>
                      </li>
                      <li className="flex items-start">
                        <ChevronRight className="h-5 w-5 text-primary mt-0.5 mr-2" />
                        <span>Automated fulfillment solutions</span>
                      </li>
                    </ul>
                    <Link href="/services">
                      <Button>Learn More <ArrowRight className="ml-2 h-4 w-4" /></Button>
                    </Link>
                  </TabsContent>
                </Tabs>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -top-6 -right-6 w-36 h-36 bg-primary/10 rounded-full -z-10"></div>
              <div className="absolute -bottom-8 -left-8 w-48 h-48 bg-accent/10 rounded-full -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>
      
      {/* Case Studies Preview Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.span 
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="text-primary font-semibold tracking-wide uppercase"
            >
              Success Stories
            </motion.span>
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mt-2 text-3xl md:text-4xl font-bold"
            >
              Client Case Studies
            </motion.h2>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="mt-4 text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              See how <span className="company-name">MP Advance Solutions</span> has helped retail and wholesale businesses achieve remarkable results.
            </motion.p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {caseStudyPreviews.map((caseStudy, index) => (
              <motion.div
                key={caseStudy.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group relative overflow-hidden rounded-xl border border-border"
              >
                <div className="aspect-[4/3] overflow-hidden">
                  <img 
                    src={caseStudy.image} 
                    alt={caseStudy.title} 
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <span className="text-sm font-semibold text-primary mb-2 block">{caseStudy.category}</span>
                    <h3 className="text-xl font-bold text-white mb-4">{caseStudy.title}</h3>
                    {/* Case study links temporarily disabled */}
                    {/* <Link href={caseStudy.link}>
                      <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                        View Case Study
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link> */}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
          
          {/* Case Studies section temporarily disabled */}
          {/* <div className="mt-12 text-center">
            <Link href="/case-studies">
              <Button size="lg" variant="outline">
                View All Case Studies
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div> */}
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-accent text-white">
        <div className="container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="max-w-3xl mx-auto text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your Business?
            </h2>
            <p className="text-lg text-white/90 mb-8">
              Contact <span className="company-name">MP Advance Solutions</span> today to discuss how our technology solutions
              can help your retail or wholesale business thrive in the digital age.
            </p>
            <Link href="/contact">
              <Button size="lg" variant="secondary">
                Schedule a Consultation
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
}