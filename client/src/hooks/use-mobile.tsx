import { useState, useEffect } from "react";

export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Set initial value
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Add event listener
    window.addEventListener("resize", checkMobile);
    
    // Call check function initially
    checkMobile();
    
    // Remove event listener on cleanup
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return isMobile;
}