import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Link } from "wouter";

interface CaseStudyCardProps {
  id: string;
  title: string;
  client: string;
  description: string;
  image: string;
  industry: string;
  date: string;
  index?: number;
}

const CaseStudyCard = ({
  id,
  title,
  client,
  description,
  image,
  industry,
  date,
  index = 0
}: CaseStudyCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden border border-border h-full flex flex-col"
    >
      <div className="relative">
        <img 
          src={image} 
          alt={title}
          className="w-full h-48 object-cover" 
        />
        <div className="absolute top-3 right-3 flex gap-2">
          <Badge variant="secondary">{industry}</Badge>
          <Badge variant="outline" className="bg-white/80 dark:bg-slate-800/80">{date}</Badge>
        </div>
      </div>
      
      <div className="p-6 flex-grow flex flex-col">
        <h3 className="text-xl font-bold mb-2 line-clamp-2">{title}</h3>
        <p className="text-sm font-medium text-muted-foreground mb-3">Client: {client}</p>
        <p className="text-muted-foreground mb-6 flex-grow line-clamp-3">{description}</p>
        
        <Link href={`/case-studies/${id}`}>
          <Button variant="link" className="p-0 h-auto font-medium mt-auto justify-start">
            View Case Study
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>
    </motion.div>
  );
};

export default CaseStudyCard;