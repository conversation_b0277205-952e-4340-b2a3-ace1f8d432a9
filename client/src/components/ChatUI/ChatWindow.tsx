import React, { useRef, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, RefreshCw, Send, Bot, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from '@/lib/chatContext';
import { formatDate } from '@/lib/utils';

// Function to handle markdown-like formatting
const formatMessage = (message: string): React.ReactNode => {
  // Replace bold text: **text** -> <strong>text</strong>
  let formattedText = message.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Replace italics: *text* -> <em>text</em>
  formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // Replace line breaks with <br />
  formattedText = formattedText.replace(/\n/g, '<br />');
  
  // Return as HTML
  return <span dangerouslySetInnerHTML={{ __html: formattedText }} />;
};

const ChatWindow: React.FC = () => {
  const { messages, isLoading, isChatOpen, sendMessage, closeChat, resetChat } = useChat();
  const [inputValue, setInputValue] = React.useState('');
  const messageEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  // Focus input when chat opens
  useEffect(() => {
    if (isChatOpen) {
      inputRef.current?.focus();
    }
  }, [isChatOpen]);
  
  // Handle sending messages
  const handleSendMessage = () => {
    if (inputValue.trim() && !isLoading) {
      sendMessage(inputValue);
      setInputValue('');
    }
  };
  
  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  if (!isChatOpen) return null;
  
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="fixed bottom-20 right-6 w-96 h-[500px] bg-white dark:bg-slate-900 rounded-xl shadow-lg border border-border flex flex-col z-50"
      >
        {/* Chat Header */}
        <div className="p-3 border-b border-border flex items-center justify-between bg-primary text-white rounded-t-xl">
          <div className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span className="font-semibold">MP Advance AI Assistant</span>
          </div>
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-white hover:bg-white/20"
              onClick={resetChat}
              title="Reset conversation"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon"
              className="h-8 w-8 text-white hover:bg-white/20"
              onClick={closeChat}
              title="Close chat"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Chat Messages */}
        <ScrollArea className="flex-grow p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div 
                key={message.id} 
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`
                  max-w-[80%] p-3 rounded-lg 
                  ${message.role === 'user' 
                    ? 'bg-primary text-white rounded-tr-none' 
                    : 'bg-slate-100 dark:bg-slate-800 rounded-tl-none'
                  }
                `}>
                  <div className="flex items-center space-x-2 mb-1">
                    {message.role === 'user' ? (
                      <>
                        <span className="text-xs opacity-70">You</span>
                        <User className="h-3 w-3 opacity-70" />
                      </>
                    ) : (
                      <>
                        <Bot className="h-3 w-3 opacity-70" />
                        <span className="text-xs opacity-70">MP Advance AI</span>
                      </>
                    )}
                  </div>
                  <div className="text-sm">
                    {formatMessage(message.content)}
                  </div>
                  <div className="text-xs opacity-50 mt-1 text-right">
                    {formatDate(message.timestamp)}
                  </div>
                </div>
              </div>
            ))}
            
            {/* Show typing indicator when loading */}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-slate-100 dark:bg-slate-800 p-3 rounded-lg rounded-tl-none max-w-[80%]">
                  <div className="flex items-center space-x-2">
                    <div className="h-2 w-2 bg-primary/50 rounded-full animate-pulse"></div>
                    <div className="h-2 w-2 bg-primary/50 rounded-full animate-pulse delay-150"></div>
                    <div className="h-2 w-2 bg-primary/50 rounded-full animate-pulse delay-300"></div>
                    <span className="text-xs opacity-50">AI is typing</span>
                  </div>
                </div>
              </div>
            )}
            
            {/* Empty div for auto-scrolling */}
            <div ref={messageEndRef} />
          </div>
        </ScrollArea>
        
        {/* Chat Input */}
        <div className="p-3 border-t border-border">
          <div className="flex space-x-2">
            <Input
              ref={inputRef}
              placeholder="Type your message..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isLoading}
              className="flex-grow"
            />
            <Button 
              onClick={handleSendMessage} 
              disabled={isLoading || !inputValue.trim()}
              size="icon"
              className="flex-shrink-0"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="mt-2 text-xs text-center text-muted-foreground">
            <span className="company-name">MP Advance Solutions</span> AI Assistant
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ChatWindow;