import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquareText, X } from 'lucide-react';
import { motion } from 'framer-motion';
import { useChat } from '@/lib/chatContext';

const ChatButton: React.FC = () => {
  const { isChatOpen, openChat, closeChat } = useChat();
  
  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ type: 'spring', stiffness: 260, damping: 20 }}
      className="fixed bottom-6 right-6 z-50"
    >
      <Button
        onClick={isChatOpen ? closeChat : openChat}
        className="h-14 w-14 rounded-full shadow-lg"
        size="lg"
      >
        {isChatOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <MessageSquareText className="h-6 w-6" />
        )}
        <span className="sr-only">
          {isChatOpen ? 'Close chat' : 'Open chat'}
        </span>
      </Button>
      
      {!isChatOpen && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="absolute top-0 right-16 bg-white dark:bg-slate-800 text-sm px-3 py-1.5 rounded-md shadow-sm border border-border whitespace-nowrap"
        >
          Chat with our AI assistant
          <div className="absolute right-0 top-1/2 -translate-y-1/2 -mr-1.5 w-3 h-3 bg-white dark:bg-slate-800 border-t border-r border-border rotate-45"></div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default ChatButton;