import { useState } from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";
import { Star } from 'lucide-react';
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";

interface Testimonial {
  id: number;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CTO",
    company: "Global Innovations",
    content: "TechNova helped us completely transform our tech infrastructure. Their team's expertise in cloud solutions and AI implementation has given us a competitive edge we didn't think was possible.",
    rating: 5
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Operations Director",
    company: "Frontier Manufacturing",
    content: "The digital transformation roadmap TechNova created for us has revolutionized our production processes. We've seen a 35% increase in efficiency and significant cost savings.",
    rating: 5
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Marketing VP",
    company: "Elevate Retail",
    content: "TechNova's data analytics solutions provided insights that transformed our customer engagement strategy. Their team is professional, responsive, and truly understands business needs.",
    rating: 4
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "CEO",
    company: "Nexus Startups",
    content: "As a startup, finding the right technology partner was crucial. TechNova provided enterprise-level solutions that were scalable and tailored to our specific needs and budget.",
    rating: 5
  }
];

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleCarouselChange = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      className="w-full"
    >
      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        className="w-full"
        onSelect={(api) => handleCarouselChange(api.selectedScrollSnap())}
      >
        <CarouselContent>
          {testimonials.map((testimonial) => (
            <CarouselItem key={testimonial.id} className="md:basis-1/2 lg:basis-1/3 pl-4">
              <Card className="border border-border h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="text-amber-400 flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < testimonial.rating ? 'fill-current' : 'stroke-current fill-transparent'}`}
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground mb-6 text-sm">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                      <span className="text-primary font-medium text-sm">
                        {testimonial.name.charAt(0)}
                        {testimonial.name.split(' ')[1].charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-bold text-sm">{testimonial.name}</h4>
                      <p className="text-muted-foreground text-xs">
                        {testimonial.role}, {testimonial.company}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="flex justify-end gap-2 mt-4 md:mt-8">
          <CarouselPrevious className="relative inset-0 translate-y-0 mr-2" />
          <CarouselNext className="relative inset-0 translate-y-0" />
        </div>
      </Carousel>
      
      <div className="flex justify-center mt-6 space-x-2">
        {testimonials.map((_, index) => (
          <button
            key={index}
            className={`w-2 h-2 rounded-full transition-colors ${
              currentIndex === index ? 'bg-primary' : 'bg-muted'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </motion.div>
  );
};

export default Testimonials;
