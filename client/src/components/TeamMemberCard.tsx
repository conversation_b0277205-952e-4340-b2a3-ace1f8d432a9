import { motion } from "framer-motion";
import { 
  Card, 
  CardContent 
} from "@/components/ui/card";
import { 
  Linkedin, 
  Twitter,
  Mail
} from "lucide-react";

interface TeamMemberCardProps {
  name: string;
  role: string;
  image: string;
  bio: string;
  social?: {
    linkedin?: string;
    twitter?: string;
    email?: string;
  };
  index?: number;
}

const TeamMemberCard = ({
  name,
  role,
  image,
  bio,
  social = {},
  index = 0
}: TeamMemberCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
    >
      <Card className="overflow-hidden h-full flex flex-col">
        <div className="relative aspect-square overflow-hidden bg-muted">
          <img 
            src={image} 
            alt={name}
            className="w-full h-full object-cover"
          />
        </div>
        
        <CardContent className="p-6 flex flex-col flex-grow">
          <h3 className="text-xl font-bold mb-1">{name}</h3>
          <p className="text-primary mb-4 font-medium">{role}</p>
          
          <p className="text-muted-foreground mb-4 text-sm flex-grow">
            {bio}
          </p>
          
          <div className="flex space-x-3">
            {social.linkedin && (
              <a 
                href={social.linkedin} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Linkedin className="h-5 w-5" />
              </a>
            )}
            
            {social.twitter && (
              <a 
                href={social.twitter} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Twitter className="h-5 w-5" />
              </a>
            )}
            
            {social.email && (
              <a 
                href={`mailto:${social.email}`}
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Mail className="h-5 w-5" />
              </a>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default TeamMemberCard;
