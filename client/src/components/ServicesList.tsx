import { motion } from "framer-motion";
import ServiceCard from "@/components/ServiceCard";
import { 
  LucideIcon,
  BarChart3, 
  BrainCircuit, 
  ShoppingCart, 
  LayoutDashboard, 
  Megaphone, 
  UserCheck,
  Workflow,
  Settings,
  BookOpen,
  MonitorSmartphone
} from "lucide-react";

interface Service {
  icon: LucideIcon;
  iconColor: string;
  title: string;
  description: string;
  link: string;
}

const services: Service[] = [
  {
    icon: BrainCircuit,
    iconColor: "text-blue-500",
    title: "AI Solutions",
    description: "Cutting-edge artificial intelligence solutions that improve decision-making, automate processes, and drive business innovation.",
    link: "/services#ai-solutions"
  },
  {
    icon: ShoppingCart,
    iconColor: "text-green-500",
    title: "Ecommerce",
    description: "Comprehensive ecommerce platforms and strategies to help retail and wholesale businesses succeed in the digital marketplace.",
    link: "/services#ecommerce"
  },
  {
    icon: Megaphone,
    iconColor: "text-purple-500",
    title: "Digital Marketing",
    description: "Strategic digital marketing services to increase your brand visibility, engage customers, and drive conversions across channels.",
    link: "/services#digital-marketing"
  },
  {
    icon: User<PERSON><PERSON><PERSON>,
    iconColor: "text-orange-500",
    title: "Leads Generation",
    description: "Targeted lead generation solutions that identify and nurture potential customers through your sales pipeline.",
    link: "/services#leads-generation"
  },
  {
    icon: Settings,
    iconColor: "text-red-500",
    title: "Business Automation",
    description: "Smart automation tools and strategies that eliminate repetitive tasks and streamline your core business processes.",
    link: "/services#business-automation"
  },
  {
    icon: Workflow,
    iconColor: "text-indigo-500",
    title: "Business Process Management",
    description: "End-to-end management solutions that optimize workflows, reduce inefficiencies, and enhance operational performance.",
    link: "/services#business-process-management"
  },
  {
    icon: BookOpen,
    iconColor: "text-emerald-500",
    title: "Order Fulfillment",
    description: "Efficient order fulfillment systems and strategies that improve accuracy, speed, and customer satisfaction.",
    link: "/services#order-fulfillment"
  },
  {
    icon: MonitorSmartphone,
    iconColor: "text-amber-500",
    title: "Digital Signage",
    description: "Interactive digital signage solutions for retail and business environments that engage customers and enhance brand experiences.",
    link: "/services#digital-signage"
  }
];

const ServicesList = () => {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
    >
      {services.map((service, index) => (
        <ServiceCard
          key={service.title}
          icon={service.icon}
          iconColor={service.iconColor}
          title={service.title}
          description={service.description}
          link={service.link}
          index={index}
        />
      ))}
    </motion.div>
  );
};

export default ServicesList;
