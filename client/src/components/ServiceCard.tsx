import { motion } from "framer-motion";
import { Link } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ServiceCardProps {
  icon: LucideIcon;
  iconColor?: string;
  title: string;
  description: string;
  link: string;
  index?: number;
  className?: string;
}

const ServiceCard = ({
  icon: Icon,
  iconColor = "text-primary",
  title,
  description,
  link,
  index = 0,
  className
}: ServiceCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className={cn(
        "bg-white dark:bg-slate-800 rounded-xl p-6 border border-border h-full flex flex-col shadow-sm hover:shadow-md transition-shadow",
        className
      )}
    >
      <div className={cn("p-3 rounded-xl w-12 h-12 flex items-center justify-center mb-6", `bg-opacity-15 bg-${iconColor.split('-')[1]}-100 dark:bg-${iconColor.split('-')[1]}-900/20`)}>
        <Icon className={cn("h-6 w-6", iconColor)} />
      </div>
      
      <h3 className="text-xl font-bold mb-3">{title}</h3>
      <p className="text-muted-foreground mb-6 flex-grow">{description}</p>
      
      <Link href={link}>
        <Button variant="link" className="p-0 h-auto font-medium mt-auto justify-start">
          Learn More
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </Link>
    </motion.div>
  );
};

export default ServiceCard;