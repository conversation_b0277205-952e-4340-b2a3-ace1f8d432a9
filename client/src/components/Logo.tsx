import { useState } from "react";
import MPAdvanceLogo from "@/assets/images/mp-advabce-solutions-logo.svg";
import MPAdvanceLogoPNG from "@/assets/images/mp-advabce-solutions-logo.png";

interface LogoProps {
  className?: string;
  alt?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  className = "h-8 w-auto", 
  alt = "MP Advance Solutions" 
}) => {
  const [useFallback, setUseFallback] = useState(false);

  const handleError = () => {
    setUseFallback(true);
  };

  return (
    <img
      src={useFallback ? MPAdvanceLogoPNG : MPAdvanceLogo}
      alt={alt}
      className={className}
      onError={handleError}
    />
  );
};

export default Logo;