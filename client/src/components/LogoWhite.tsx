import { useState } from "react";
import MPAdvanceLogoWhiteSVG from "@/assets/images/mp-advance-logo-white.svg";
import MPAdvanceLogoPNG from "@/assets/images/mp-advabce-solutions-logo.png";

interface LogoProps {
  className?: string;
  alt?: string;
}

const LogoWhite: React.FC<LogoProps> = ({ 
  className = "h-8 w-auto", 
  alt = "MP Advance Solutions" 
}) => {
  const [useFallback, setUseFallback] = useState(false);

  const handleError = () => {
    setUseFallback(true);
  };

  return (
    <img
      src={useFallback ? MPAdvanceLogoPNG : MPAdvanceLogoWhiteSVG}
      alt={alt}
      className={className}
      onError={handleError}
    />
  );
};

export default LogoWhite;