#!/bin/bash

# MP Advance Solutions - Deployment Script
# This script handles different deployment scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to build the application
build_app() {
    print_status "Building application..."
    npm run dist:build
    print_success "Application built successfully"
}

# Function to create npm package
create_npm_package() {
    print_status "Creating npm package..."
    npm run dist:package
    print_success "NPM package created successfully"
}

# Function to build Docker image
build_docker() {
    if ! command_exists docker; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    print_status "Building Docker image..."
    npm run dist:docker
    print_success "Docker image built successfully"
}

# Function to deploy with Docker Compose
deploy_docker_compose() {
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    print_status "Deploying with Docker Compose..."
    npm run dist:docker-compose
    print_success "Application deployed with Docker Compose"
}

# Function to create standalone distribution
create_standalone() {
    print_status "Creating standalone distribution..."
    
    # Create distribution directory
    mkdir -p dist-package
    
    # Copy built files
    cp -r dist/ dist-package/
    cp package.json dist-package/
    cp package-lock.json dist-package/
    
    # Create startup script
    cat > dist-package/start.sh << 'EOF'
#!/bin/bash
echo "Starting MP Advance Solutions..."
NODE_ENV=production node dist/index.js
EOF
    
    chmod +x dist-package/start.sh
    
    # Create README for distribution
    cat > dist-package/README.md << 'EOF'
# MP Advance Solutions - Distribution Package

## Quick Start

1. Install Node.js (version 18 or higher)
2. Install dependencies: `npm install --production`
3. Start the application: `./start.sh` or `npm start`

The application will be available at http://localhost:3000

## Environment Variables

- `NODE_ENV`: Set to "production" for production deployment
- `PORT`: Port number (default: 3000)

## Support

For support, please contact MP Advance Solutions.
EOF
    
    # Create archive
    tar -czf mp-advance-solutions-$(date +%Y%m%d-%H%M%S).tar.gz -C dist-package .
    
    print_success "Standalone distribution created"
}

# Main deployment function
main() {
    echo "MP Advance Solutions - Deployment Script"
    echo "========================================"
    
    case "${1:-help}" in
        "build")
            build_app
            ;;
        "package")
            build_app
            create_npm_package
            ;;
        "docker")
            build_app
            build_docker
            ;;
        "compose")
            deploy_docker_compose
            ;;
        "standalone")
            build_app
            create_standalone
            ;;
        "all")
            build_app
            create_npm_package
            build_docker
            create_standalone
            print_success "All distribution packages created"
            ;;
        "help"|*)
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  build      - Build the application"
            echo "  package    - Create npm package"
            echo "  docker     - Build Docker image"
            echo "  compose    - Deploy with Docker Compose"
            echo "  standalone - Create standalone distribution"
            echo "  all        - Create all distribution packages"
            echo "  help       - Show this help message"
            ;;
    esac
}

# Run main function with all arguments
main "$@"