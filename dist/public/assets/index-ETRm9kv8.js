var go=t=>{throw TypeError(t)};var ai=(t,e,s)=>e.has(t)||go("Cannot "+s);var S=(t,e,s)=>(ai(t,e,"read from private field"),s?s.call(t):e.get(t)),ie=(t,e,s)=>e.has(t)?go("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),Z=(t,e,s,n)=>(ai(t,e,"write to private field"),n?n.call(t,s):e.set(t,s),s),Ce=(t,e,s)=>(ai(t,e,"access private method"),s);var Gn=(t,e,s,n)=>({set _(r){Z(t,e,r,s)},get _(){return S(t,e,n)}});import{j as i,P as ue,c as On,a as Vc,u as _e,B as km,b as ur,d as Ts,e as J,f as et,R as Tm,g as Dc,h as wt,i as Am,k as Mr,D as Lc,C as Oc,l as Pm,A as Fc,m as Bc,S as zc,O as Uc,n as _m,o as Wc,p as Em,T as $c,q as Hc,r as Rm,s as Mm,t as Zc,v as Im,I as Vm,w as ca,x as Ir,y as Dm,z as Lm,E as Om,F as Fm,G as Bm,H as zm}from"./ui-DzzMdnWH.js";import{a as Vr,r as m,c as Um,R as de}from"./vendor-1XCZ5AD1.js";import{c as Kc,t as Wm}from"./utils-BTGVH9Kg.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function s(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function n(r){if(r.ep)return;r.ep=!0;const a=s(r);fetch(r.href,a)}})();var qc,xo=Vr;qc=xo.createRoot,xo.hydrateRoot;var Fn=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Dr=typeof window>"u"||"Deno"in globalThis;function Je(){}function $m(t,e){return typeof t=="function"?t(e):t}function Hm(t){return typeof t=="number"&&t>=0&&t!==1/0}function Zm(t,e){return Math.max(t+(e||0)-Date.now(),0)}function Ti(t,e){return typeof t=="function"?t(e):t}function Km(t,e){return typeof t=="function"?t(e):t}function yo(t,e){const{type:s="all",exact:n,fetchStatus:r,predicate:a,queryKey:o,stale:l}=t;if(o){if(n){if(e.queryHash!==da(o,e.options))return!1}else if(!Tn(e.queryKey,o))return!1}if(s!=="all"){const c=e.isActive();if(s==="active"&&!c||s==="inactive"&&c)return!1}return!(typeof l=="boolean"&&e.isStale()!==l||r&&r!==e.state.fetchStatus||a&&!a(e))}function vo(t,e){const{exact:s,status:n,predicate:r,mutationKey:a}=t;if(a){if(!e.options.mutationKey)return!1;if(s){if(Ns(e.options.mutationKey)!==Ns(a))return!1}else if(!Tn(e.options.mutationKey,a))return!1}return!(n&&e.state.status!==n||r&&!r(e))}function da(t,e){return((e==null?void 0:e.queryKeyHashFn)||Ns)(t)}function Ns(t){return JSON.stringify(t,(e,s)=>Ai(s)?Object.keys(s).sort().reduce((n,r)=>(n[r]=s[r],n),{}):s)}function Tn(t,e){return t===e?!0:typeof t!=typeof e?!1:t&&e&&typeof t=="object"&&typeof e=="object"?Object.keys(e).every(s=>Tn(t[s],e[s])):!1}function Gc(t,e){if(t===e)return t;const s=bo(t)&&bo(e);if(s||Ai(t)&&Ai(e)){const n=s?t:Object.keys(t),r=n.length,a=s?e:Object.keys(e),o=a.length,l=s?[]:{},c=new Set(n);let d=0;for(let u=0;u<o;u++){const h=s?u:a[u];(!s&&c.has(h)||s)&&t[h]===void 0&&e[h]===void 0?(l[h]=void 0,d++):(l[h]=Gc(t[h],e[h]),l[h]===t[h]&&t[h]!==void 0&&d++)}return r===o&&d===r?t:l}return e}function qm(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function bo(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Ai(t){if(!wo(t))return!1;const e=t.constructor;if(e===void 0)return!0;const s=e.prototype;return!(!wo(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function wo(t){return Object.prototype.toString.call(t)==="[object Object]"}function Gm(t){return new Promise(e=>{setTimeout(e,t)})}function Ym(t,e,s){return typeof s.structuralSharing=="function"?s.structuralSharing(t,e):s.structuralSharing!==!1?Gc(t,e):e}function Xm(t,e,s=0){const n=[...t,e];return s&&n.length>s?n.slice(1):n}function Qm(t,e,s=0){const n=[e,...t];return s&&n.length>s?n.slice(0,-1):n}var ua=Symbol();function Yc(t,e){return!t.queryFn&&(e!=null&&e.initialPromise)?()=>e.initialPromise:!t.queryFn||t.queryFn===ua?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}function Jm(t,e){return typeof t=="function"?t(...e):!!t}var xs,zt,zs,kc,ep=(kc=class extends Fn{constructor(){super();ie(this,xs);ie(this,zt);ie(this,zs);Z(this,zs,e=>{if(!Dr&&window.addEventListener){const s=()=>e();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){S(this,zt)||this.setEventListener(S(this,zs))}onUnsubscribe(){var e;this.hasListeners()||((e=S(this,zt))==null||e.call(this),Z(this,zt,void 0))}setEventListener(e){var s;Z(this,zs,e),(s=S(this,zt))==null||s.call(this),Z(this,zt,e(n=>{typeof n=="boolean"?this.setFocused(n):this.onFocus()}))}setFocused(e){S(this,xs)!==e&&(Z(this,xs,e),this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(s=>{s(e)})}isFocused(){var e;return typeof S(this,xs)=="boolean"?S(this,xs):((e=globalThis.document)==null?void 0:e.visibilityState)!=="hidden"}},xs=new WeakMap,zt=new WeakMap,zs=new WeakMap,kc),Xc=new ep,Us,Ut,Ws,Tc,tp=(Tc=class extends Fn{constructor(){super();ie(this,Us,!0);ie(this,Ut);ie(this,Ws);Z(this,Ws,e=>{if(!Dr&&window.addEventListener){const s=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",n)}}})}onSubscribe(){S(this,Ut)||this.setEventListener(S(this,Ws))}onUnsubscribe(){var e;this.hasListeners()||((e=S(this,Ut))==null||e.call(this),Z(this,Ut,void 0))}setEventListener(e){var s;Z(this,Ws,e),(s=S(this,Ut))==null||s.call(this),Z(this,Ut,e(this.setOnline.bind(this)))}setOnline(e){S(this,Us)!==e&&(Z(this,Us,e),this.listeners.forEach(n=>{n(e)}))}isOnline(){return S(this,Us)}},Us=new WeakMap,Ut=new WeakMap,Ws=new WeakMap,Tc),hr=new tp;function sp(){let t,e;const s=new Promise((r,a)=>{t=r,e=a});s.status="pending",s.catch(()=>{});function n(r){Object.assign(s,r),delete s.resolve,delete s.reject}return s.resolve=r=>{n({status:"fulfilled",value:r}),t(r)},s.reject=r=>{n({status:"rejected",reason:r}),e(r)},s}function np(t){return Math.min(1e3*2**t,3e4)}function Qc(t){return(t??"online")==="online"?hr.isOnline():!0}var Jc=class extends Error{constructor(t){super("CancelledError"),this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent}};function oi(t){return t instanceof Jc}function ed(t){let e=!1,s=0,n=!1,r;const a=sp(),o=p=>{var g;n||(f(new Jc(p)),(g=t.abort)==null||g.call(t))},l=()=>{e=!0},c=()=>{e=!1},d=()=>Xc.isFocused()&&(t.networkMode==="always"||hr.isOnline())&&t.canRun(),u=()=>Qc(t.networkMode)&&t.canRun(),h=p=>{var g;n||(n=!0,(g=t.onSuccess)==null||g.call(t,p),r==null||r(),a.resolve(p))},f=p=>{var g;n||(n=!0,(g=t.onError)==null||g.call(t,p),r==null||r(),a.reject(p))},x=()=>new Promise(p=>{var g;r=j=>{(n||d())&&p(j)},(g=t.onPause)==null||g.call(t)}).then(()=>{var p;r=void 0,n||(p=t.onContinue)==null||p.call(t)}),b=()=>{if(n)return;let p;const g=s===0?t.initialPromise:void 0;try{p=g??t.fn()}catch(j){p=Promise.reject(j)}Promise.resolve(p).then(h).catch(j=>{var M;if(n)return;const v=t.retry??(Dr?0:3),w=t.retryDelay??np,T=typeof w=="function"?w(s,j):w,C=v===!0||typeof v=="number"&&s<v||typeof v=="function"&&v(s,j);if(e||!C){f(j);return}s++,(M=t.onFail)==null||M.call(t,s,j),Gm(T).then(()=>d()?void 0:x()).then(()=>{e?f(j):b()})})};return{promise:a,cancel:o,continue:()=>(r==null||r(),a),cancelRetry:l,continueRetry:c,canStart:u,start:()=>(u()?b():x().then(b),a)}}var rp=t=>setTimeout(t,0);function ip(){let t=[],e=0,s=l=>{l()},n=l=>{l()},r=rp;const a=l=>{e?t.push(l):r(()=>{s(l)})},o=()=>{const l=t;t=[],l.length&&r(()=>{n(()=>{l.forEach(c=>{s(c)})})})};return{batch:l=>{let c;e++;try{c=l()}finally{e--,e||o()}return c},batchCalls:l=>(...c)=>{a(()=>{l(...c)})},schedule:a,setNotifyFunction:l=>{s=l},setBatchNotifyFunction:l=>{n=l},setScheduler:l=>{r=l}}}var Ve=ip(),ys,Ac,td=(Ac=class{constructor(){ie(this,ys)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Hm(this.gcTime)&&Z(this,ys,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(Dr?1/0:5*60*1e3))}clearGcTimeout(){S(this,ys)&&(clearTimeout(S(this,ys)),Z(this,ys,void 0))}},ys=new WeakMap,Ac),$s,Hs,Qe,vs,De,Dn,bs,lt,Tt,Pc,ap=(Pc=class extends td{constructor(e){super();ie(this,lt);ie(this,$s);ie(this,Hs);ie(this,Qe);ie(this,vs);ie(this,De);ie(this,Dn);ie(this,bs);Z(this,bs,!1),Z(this,Dn,e.defaultOptions),this.setOptions(e.options),this.observers=[],Z(this,vs,e.client),Z(this,Qe,S(this,vs).getQueryCache()),this.queryKey=e.queryKey,this.queryHash=e.queryHash,Z(this,$s,lp(this.options)),this.state=e.state??S(this,$s),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var e;return(e=S(this,De))==null?void 0:e.promise}setOptions(e){this.options={...S(this,Dn),...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&S(this,Qe).remove(this)}setData(e,s){const n=Ym(this.state.data,e,this.options);return Ce(this,lt,Tt).call(this,{data:n,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),n}setState(e,s){Ce(this,lt,Tt).call(this,{type:"setState",state:e,setStateOptions:s})}cancel(e){var n,r;const s=(n=S(this,De))==null?void 0:n.promise;return(r=S(this,De))==null||r.cancel(e),s?s.then(Je).catch(Je):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(S(this,$s))}isActive(){return this.observers.some(e=>Km(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ua||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(e=>Ti(e.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(e=0){return this.state.data===void 0?!0:e==="static"?!1:this.state.isInvalidated?!0:!Zm(this.state.dataUpdatedAt,e)}onFocus(){var s;const e=this.observers.find(n=>n.shouldFetchOnWindowFocus());e==null||e.refetch({cancelRefetch:!1}),(s=S(this,De))==null||s.continue()}onOnline(){var s;const e=this.observers.find(n=>n.shouldFetchOnReconnect());e==null||e.refetch({cancelRefetch:!1}),(s=S(this,De))==null||s.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),S(this,Qe).notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(s=>s!==e),this.observers.length||(S(this,De)&&(S(this,bs)?S(this,De).cancel({revert:!0}):S(this,De).cancelRetry()),this.scheduleGc()),S(this,Qe).notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Ce(this,lt,Tt).call(this,{type:"invalidate"})}fetch(e,s){var d,u,h;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(S(this,De))return S(this,De).continueRetry(),S(this,De).promise}if(e&&this.setOptions(e),!this.options.queryFn){const f=this.observers.find(x=>x.options.queryFn);f&&this.setOptions(f.options)}const n=new AbortController,r=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(Z(this,bs,!0),n.signal)})},a=()=>{const f=Yc(this.options,s),b=(()=>{const p={client:S(this,vs),queryKey:this.queryKey,meta:this.meta};return r(p),p})();return Z(this,bs,!1),this.options.persister?this.options.persister(f,b,this):f(b)},l=(()=>{const f={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:S(this,vs),state:this.state,fetchFn:a};return r(f),f})();(d=this.options.behavior)==null||d.onFetch(l,this),Z(this,Hs,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=l.fetchOptions)==null?void 0:u.meta))&&Ce(this,lt,Tt).call(this,{type:"fetch",meta:(h=l.fetchOptions)==null?void 0:h.meta});const c=f=>{var x,b,p,g;oi(f)&&f.silent||Ce(this,lt,Tt).call(this,{type:"error",error:f}),oi(f)||((b=(x=S(this,Qe).config).onError)==null||b.call(x,f,this),(g=(p=S(this,Qe).config).onSettled)==null||g.call(p,this.state.data,f,this)),this.scheduleGc()};return Z(this,De,ed({initialPromise:s==null?void 0:s.initialPromise,fn:l.fetchFn,abort:n.abort.bind(n),onSuccess:f=>{var x,b,p,g;if(f===void 0){c(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(j){c(j);return}(b=(x=S(this,Qe).config).onSuccess)==null||b.call(x,f,this),(g=(p=S(this,Qe).config).onSettled)==null||g.call(p,f,this.state.error,this),this.scheduleGc()},onError:c,onFail:(f,x)=>{Ce(this,lt,Tt).call(this,{type:"failed",failureCount:f,error:x})},onPause:()=>{Ce(this,lt,Tt).call(this,{type:"pause"})},onContinue:()=>{Ce(this,lt,Tt).call(this,{type:"continue"})},retry:l.options.retry,retryDelay:l.options.retryDelay,networkMode:l.options.networkMode,canRun:()=>!0})),S(this,De).start()}},$s=new WeakMap,Hs=new WeakMap,Qe=new WeakMap,vs=new WeakMap,De=new WeakMap,Dn=new WeakMap,bs=new WeakMap,lt=new WeakSet,Tt=function(e){const s=n=>{switch(e.type){case"failed":return{...n,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...n,fetchStatus:"paused"};case"continue":return{...n,fetchStatus:"fetching"};case"fetch":return{...n,...op(n.data,this.options),fetchMeta:e.meta??null};case"success":return{...n,data:e.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return oi(r)&&r.revert&&S(this,Hs)?{...S(this,Hs),fetchStatus:"idle"}:{...n,error:r,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...n,isInvalidated:!0};case"setState":return{...n,...e.state}}};this.state=s(this.state),Ve.batch(()=>{this.observers.forEach(n=>{n.onQueryUpdate()}),S(this,Qe).notify({query:this,type:"updated",action:e})})},Pc);function op(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Qc(e.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function lp(t){const e=typeof t.initialData=="function"?t.initialData():t.initialData,s=e!==void 0,n=s?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var mt,_c,cp=(_c=class extends Fn{constructor(e={}){super();ie(this,mt);this.config=e,Z(this,mt,new Map)}build(e,s,n){const r=s.queryKey,a=s.queryHash??da(r,s);let o=this.get(a);return o||(o=new ap({client:e,queryKey:r,queryHash:a,options:e.defaultQueryOptions(s),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(o)),o}add(e){S(this,mt).has(e.queryHash)||(S(this,mt).set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const s=S(this,mt).get(e.queryHash);s&&(e.destroy(),s===e&&S(this,mt).delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){Ve.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return S(this,mt).get(e)}getAll(){return[...S(this,mt).values()]}find(e){const s={exact:!0,...e};return this.getAll().find(n=>yo(s,n))}findAll(e={}){const s=this.getAll();return Object.keys(e).length>0?s.filter(n=>yo(e,n)):s}notify(e){Ve.batch(()=>{this.listeners.forEach(s=>{s(e)})})}onFocus(){Ve.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){Ve.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},mt=new WeakMap,_c),pt,Fe,ws,gt,Lt,Ec,dp=(Ec=class extends td{constructor(e){super();ie(this,gt);ie(this,pt);ie(this,Fe);ie(this,ws);this.mutationId=e.mutationId,Z(this,Fe,e.mutationCache),Z(this,pt,[]),this.state=e.state||sd(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){S(this,pt).includes(e)||(S(this,pt).push(e),this.clearGcTimeout(),S(this,Fe).notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){Z(this,pt,S(this,pt).filter(s=>s!==e)),this.scheduleGc(),S(this,Fe).notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){S(this,pt).length||(this.state.status==="pending"?this.scheduleGc():S(this,Fe).remove(this))}continue(){var e;return((e=S(this,ws))==null?void 0:e.continue())??this.execute(this.state.variables)}async execute(e){var a,o,l,c,d,u,h,f,x,b,p,g,j,v,w,T,C,M,O,I;const s=()=>{Ce(this,gt,Lt).call(this,{type:"continue"})};Z(this,ws,ed({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(_,$)=>{Ce(this,gt,Lt).call(this,{type:"failed",failureCount:_,error:$})},onPause:()=>{Ce(this,gt,Lt).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>S(this,Fe).canRun(this)}));const n=this.state.status==="pending",r=!S(this,ws).canStart();try{if(n)s();else{Ce(this,gt,Lt).call(this,{type:"pending",variables:e,isPaused:r}),await((o=(a=S(this,Fe).config).onMutate)==null?void 0:o.call(a,e,this));const $=await((c=(l=this.options).onMutate)==null?void 0:c.call(l,e));$!==this.state.context&&Ce(this,gt,Lt).call(this,{type:"pending",context:$,variables:e,isPaused:r})}const _=await S(this,ws).start();return await((u=(d=S(this,Fe).config).onSuccess)==null?void 0:u.call(d,_,e,this.state.context,this)),await((f=(h=this.options).onSuccess)==null?void 0:f.call(h,_,e,this.state.context)),await((b=(x=S(this,Fe).config).onSettled)==null?void 0:b.call(x,_,null,this.state.variables,this.state.context,this)),await((g=(p=this.options).onSettled)==null?void 0:g.call(p,_,null,e,this.state.context)),Ce(this,gt,Lt).call(this,{type:"success",data:_}),_}catch(_){try{throw await((v=(j=S(this,Fe).config).onError)==null?void 0:v.call(j,_,e,this.state.context,this)),await((T=(w=this.options).onError)==null?void 0:T.call(w,_,e,this.state.context)),await((M=(C=S(this,Fe).config).onSettled)==null?void 0:M.call(C,void 0,_,this.state.variables,this.state.context,this)),await((I=(O=this.options).onSettled)==null?void 0:I.call(O,void 0,_,e,this.state.context)),_}finally{Ce(this,gt,Lt).call(this,{type:"error",error:_})}}finally{S(this,Fe).runNext(this)}}},pt=new WeakMap,Fe=new WeakMap,ws=new WeakMap,gt=new WeakSet,Lt=function(e){const s=n=>{switch(e.type){case"failed":return{...n,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...n,isPaused:!0};case"continue":return{...n,isPaused:!1};case"pending":return{...n,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...n,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...n,data:void 0,error:e.error,failureCount:n.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=s(this.state),Ve.batch(()=>{S(this,pt).forEach(n=>{n.onMutationUpdate(e)}),S(this,Fe).notify({mutation:this,type:"updated",action:e})})},Ec);function sd(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var At,ct,Ln,Rc,up=(Rc=class extends Fn{constructor(e={}){super();ie(this,At);ie(this,ct);ie(this,Ln);this.config=e,Z(this,At,new Set),Z(this,ct,new Map),Z(this,Ln,0)}build(e,s,n){const r=new dp({mutationCache:this,mutationId:++Gn(this,Ln)._,options:e.defaultMutationOptions(s),state:n});return this.add(r),r}add(e){S(this,At).add(e);const s=Yn(e);if(typeof s=="string"){const n=S(this,ct).get(s);n?n.push(e):S(this,ct).set(s,[e])}this.notify({type:"added",mutation:e})}remove(e){if(S(this,At).delete(e)){const s=Yn(e);if(typeof s=="string"){const n=S(this,ct).get(s);if(n)if(n.length>1){const r=n.indexOf(e);r!==-1&&n.splice(r,1)}else n[0]===e&&S(this,ct).delete(s)}}this.notify({type:"removed",mutation:e})}canRun(e){const s=Yn(e);if(typeof s=="string"){const n=S(this,ct).get(s),r=n==null?void 0:n.find(a=>a.state.status==="pending");return!r||r===e}else return!0}runNext(e){var n;const s=Yn(e);if(typeof s=="string"){const r=(n=S(this,ct).get(s))==null?void 0:n.find(a=>a!==e&&a.state.isPaused);return(r==null?void 0:r.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Ve.batch(()=>{S(this,At).forEach(e=>{this.notify({type:"removed",mutation:e})}),S(this,At).clear(),S(this,ct).clear()})}getAll(){return Array.from(S(this,At))}find(e){const s={exact:!0,...e};return this.getAll().find(n=>vo(s,n))}findAll(e={}){return this.getAll().filter(s=>vo(e,s))}notify(e){Ve.batch(()=>{this.listeners.forEach(s=>{s(e)})})}resumePausedMutations(){const e=this.getAll().filter(s=>s.state.isPaused);return Ve.batch(()=>Promise.all(e.map(s=>s.continue().catch(Je))))}},At=new WeakMap,ct=new WeakMap,Ln=new WeakMap,Rc);function Yn(t){var e;return(e=t.options.scope)==null?void 0:e.id}function jo(t){return{onFetch:(e,s)=>{var u,h,f,x,b;const n=e.options,r=(f=(h=(u=e.fetchOptions)==null?void 0:u.meta)==null?void 0:h.fetchMore)==null?void 0:f.direction,a=((x=e.state.data)==null?void 0:x.pages)||[],o=((b=e.state.data)==null?void 0:b.pageParams)||[];let l={pages:[],pageParams:[]},c=0;const d=async()=>{let p=!1;const g=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(e.signal.aborted?p=!0:e.signal.addEventListener("abort",()=>{p=!0}),e.signal)})},j=Yc(e.options,e.fetchOptions),v=async(w,T,C)=>{if(p)return Promise.reject();if(T==null&&w.pages.length)return Promise.resolve(w);const O=(()=>{const q={client:e.client,queryKey:e.queryKey,pageParam:T,direction:C?"backward":"forward",meta:e.options.meta};return g(q),q})(),I=await j(O),{maxPages:_}=e.options,$=C?Qm:Xm;return{pages:$(w.pages,I,_),pageParams:$(w.pageParams,T,_)}};if(r&&a.length){const w=r==="backward",T=w?hp:No,C={pages:a,pageParams:o},M=T(n,C);l=await v(C,M,w)}else{const w=t??a.length;do{const T=c===0?o[0]??n.initialPageParam:No(n,l);if(c>0&&T==null)break;l=await v(l,T),c++}while(c<w)}return l};e.options.persister?e.fetchFn=()=>{var p,g;return(g=(p=e.options).persister)==null?void 0:g.call(p,d,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=d}}}function No(t,{pages:e,pageParams:s}){const n=e.length-1;return e.length>0?t.getNextPageParam(e[n],e,s[n],s):void 0}function hp(t,{pages:e,pageParams:s}){var n;return e.length>0?(n=t.getPreviousPageParam)==null?void 0:n.call(t,e[0],e,s[0],s):void 0}var Ne,Wt,$t,Zs,Ks,Ht,qs,Gs,Mc,fp=(Mc=class{constructor(t={}){ie(this,Ne);ie(this,Wt);ie(this,$t);ie(this,Zs);ie(this,Ks);ie(this,Ht);ie(this,qs);ie(this,Gs);Z(this,Ne,t.queryCache||new cp),Z(this,Wt,t.mutationCache||new up),Z(this,$t,t.defaultOptions||{}),Z(this,Zs,new Map),Z(this,Ks,new Map),Z(this,Ht,0)}mount(){Gn(this,Ht)._++,S(this,Ht)===1&&(Z(this,qs,Xc.subscribe(async t=>{t&&(await this.resumePausedMutations(),S(this,Ne).onFocus())})),Z(this,Gs,hr.subscribe(async t=>{t&&(await this.resumePausedMutations(),S(this,Ne).onOnline())})))}unmount(){var t,e;Gn(this,Ht)._--,S(this,Ht)===0&&((t=S(this,qs))==null||t.call(this),Z(this,qs,void 0),(e=S(this,Gs))==null||e.call(this),Z(this,Gs,void 0))}isFetching(t){return S(this,Ne).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return S(this,Wt).findAll({...t,status:"pending"}).length}getQueryData(t){var s;const e=this.defaultQueryOptions({queryKey:t});return(s=S(this,Ne).get(e.queryHash))==null?void 0:s.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=S(this,Ne).build(this,e),n=s.state.data;return n===void 0?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(Ti(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(n))}getQueriesData(t){return S(this,Ne).findAll(t).map(({queryKey:e,state:s})=>{const n=s.data;return[e,n]})}setQueryData(t,e,s){const n=this.defaultQueryOptions({queryKey:t}),r=S(this,Ne).get(n.queryHash),a=r==null?void 0:r.state.data,o=$m(e,a);if(o!==void 0)return S(this,Ne).build(this,n).setData(o,{...s,manual:!0})}setQueriesData(t,e,s){return Ve.batch(()=>S(this,Ne).findAll(t).map(({queryKey:n})=>[n,this.setQueryData(n,e,s)]))}getQueryState(t){var s;const e=this.defaultQueryOptions({queryKey:t});return(s=S(this,Ne).get(e.queryHash))==null?void 0:s.state}removeQueries(t){const e=S(this,Ne);Ve.batch(()=>{e.findAll(t).forEach(s=>{e.remove(s)})})}resetQueries(t,e){const s=S(this,Ne);return Ve.batch(()=>(s.findAll(t).forEach(n=>{n.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){const s={revert:!0,...e},n=Ve.batch(()=>S(this,Ne).findAll(t).map(r=>r.cancel(s)));return Promise.all(n).then(Je).catch(Je)}invalidateQueries(t,e={}){return Ve.batch(()=>(S(this,Ne).findAll(t).forEach(s=>{s.invalidate()}),(t==null?void 0:t.refetchType)==="none"?Promise.resolve():this.refetchQueries({...t,type:(t==null?void 0:t.refetchType)??(t==null?void 0:t.type)??"active"},e)))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},n=Ve.batch(()=>S(this,Ne).findAll(t).filter(r=>!r.isDisabled()&&!r.isStatic()).map(r=>{let a=r.fetch(void 0,s);return s.throwOnError||(a=a.catch(Je)),r.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(n).then(Je)}fetchQuery(t){const e=this.defaultQueryOptions(t);e.retry===void 0&&(e.retry=!1);const s=S(this,Ne).build(this,e);return s.isStaleByTime(Ti(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Je).catch(Je)}fetchInfiniteQuery(t){return t.behavior=jo(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Je).catch(Je)}ensureInfiniteQueryData(t){return t.behavior=jo(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return hr.isOnline()?S(this,Wt).resumePausedMutations():Promise.resolve()}getQueryCache(){return S(this,Ne)}getMutationCache(){return S(this,Wt)}getDefaultOptions(){return S(this,$t)}setDefaultOptions(t){Z(this,$t,t)}setQueryDefaults(t,e){S(this,Zs).set(Ns(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...S(this,Zs).values()],s={};return e.forEach(n=>{Tn(t,n.queryKey)&&Object.assign(s,n.defaultOptions)}),s}setMutationDefaults(t,e){S(this,Ks).set(Ns(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...S(this,Ks).values()],s={};return e.forEach(n=>{Tn(t,n.mutationKey)&&Object.assign(s,n.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...S(this,$t).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=da(e.queryKey,e)),e.refetchOnReconnect===void 0&&(e.refetchOnReconnect=e.networkMode!=="always"),e.throwOnError===void 0&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===ua&&(e.enabled=!1),e}defaultMutationOptions(t){return t!=null&&t._defaulted?t:{...S(this,$t).mutations,...(t==null?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){S(this,Ne).clear(),S(this,Wt).clear()}},Ne=new WeakMap,Wt=new WeakMap,$t=new WeakMap,Zs=new WeakMap,Ks=new WeakMap,Ht=new WeakMap,qs=new WeakMap,Gs=new WeakMap,Mc),Zt,Kt,We,Pt,Rt,sr,Pi,Ic,mp=(Ic=class extends Fn{constructor(s,n){super();ie(this,Rt);ie(this,Zt);ie(this,Kt);ie(this,We);ie(this,Pt);Z(this,Zt,s),this.setOptions(n),this.bindMethods(),Ce(this,Rt,sr).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(s){var r;const n=this.options;this.options=S(this,Zt).defaultMutationOptions(s),qm(this.options,n)||S(this,Zt).getMutationCache().notify({type:"observerOptionsUpdated",mutation:S(this,We),observer:this}),n!=null&&n.mutationKey&&this.options.mutationKey&&Ns(n.mutationKey)!==Ns(this.options.mutationKey)?this.reset():((r=S(this,We))==null?void 0:r.state.status)==="pending"&&S(this,We).setOptions(this.options)}onUnsubscribe(){var s;this.hasListeners()||(s=S(this,We))==null||s.removeObserver(this)}onMutationUpdate(s){Ce(this,Rt,sr).call(this),Ce(this,Rt,Pi).call(this,s)}getCurrentResult(){return S(this,Kt)}reset(){var s;(s=S(this,We))==null||s.removeObserver(this),Z(this,We,void 0),Ce(this,Rt,sr).call(this),Ce(this,Rt,Pi).call(this)}mutate(s,n){var r;return Z(this,Pt,n),(r=S(this,We))==null||r.removeObserver(this),Z(this,We,S(this,Zt).getMutationCache().build(S(this,Zt),this.options)),S(this,We).addObserver(this),S(this,We).execute(s)}},Zt=new WeakMap,Kt=new WeakMap,We=new WeakMap,Pt=new WeakMap,Rt=new WeakSet,sr=function(){var n;const s=((n=S(this,We))==null?void 0:n.state)??sd();Z(this,Kt,{...s,isPending:s.status==="pending",isSuccess:s.status==="success",isError:s.status==="error",isIdle:s.status==="idle",mutate:this.mutate,reset:this.reset})},Pi=function(s){Ve.batch(()=>{var n,r,a,o,l,c,d,u;if(S(this,Pt)&&this.hasListeners()){const h=S(this,Kt).variables,f=S(this,Kt).context;(s==null?void 0:s.type)==="success"?((r=(n=S(this,Pt)).onSuccess)==null||r.call(n,s.data,h,f),(o=(a=S(this,Pt)).onSettled)==null||o.call(a,s.data,null,h,f)):(s==null?void 0:s.type)==="error"&&((c=(l=S(this,Pt)).onError)==null||c.call(l,s.error,h,f),(u=(d=S(this,Pt)).onSettled)==null||u.call(d,void 0,s.error,h,f))}this.listeners.forEach(h=>{h(S(this,Kt))})})},Ic),nd=m.createContext(void 0),pp=t=>{const e=m.useContext(nd);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},gp=({client:t,children:e})=>(m.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),i.jsx(nd.Provider,{value:t,children:e}));function xp(t,e){const s=pp(),[n]=m.useState(()=>new mp(s,t));m.useEffect(()=>{n.setOptions(t)},[n,t]);const r=m.useSyncExternalStore(m.useCallback(o=>n.subscribe(Ve.batchCalls(o)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),a=m.useCallback((o,l)=>{n.mutate(o,l).catch(Je)},[n]);if(r.error&&Jm(n.options.throwOnError,[r.error]))throw r.error;return{...r,mutate:a,mutateAsync:r.mutate}}async function rd(t){if(!t.ok){const e=await t.text()||t.statusText;throw new Error(`${t.status}: ${e}`)}}async function yp(t,e){const s=e==null?void 0:e.method,n=e==null?void 0:e.data,r=(e==null?void 0:e.headers)||{},a=await fetch(t,{method:s,headers:{...n?{"Content-Type":"application/json"}:{},...r},body:n?JSON.stringify(n):void 0,credentials:"include"});return await rd(a),a.status===204?{}:a.json()}const vp=({on401:t})=>async({queryKey:e})=>{const s=await fetch(e[0],{credentials:"include"});return await rd(s),await s.json()},bp=new fp({defaultOptions:{queries:{queryFn:vp({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),wp={theme:"system",setTheme:()=>null},id=m.createContext(wp);function jp({children:t,defaultTheme:e="system",storageKey:s="ui-theme",...n}){const[r,a]=m.useState(()=>localStorage.getItem(s)||e);m.useEffect(()=>{const l=window.document.documentElement;if(l.classList.remove("light","dark"),r==="system"){const c=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";l.classList.add(c);return}l.classList.add(r)},[r]);const o={theme:r,setTheme:l=>{localStorage.setItem(s,l),a(l)}};return i.jsx(id.Provider,{...n,value:o,children:t})}const Np=()=>{const t=m.useContext(id);if(t===void 0)throw new Error("useTheme must be used within a ThemeProvider");return t};function Sp(t,e){if(t instanceof RegExp)return{keys:!1,pattern:t};var s,n,r,a,o=[],l="",c=t.split("/");for(c[0]||c.shift();r=c.shift();)s=r[0],s==="*"?(o.push(s),l+=r[1]==="?"?"(?:/(.*))?":"/(.*)"):s===":"?(n=r.indexOf("?",1),a=r.indexOf(".",1),o.push(r.substring(1,~n?n:~a?a:r.length)),l+=~n&&!~a?"(?:/([^/]+?))?":"/([^/]+?)",~a&&(l+=(~n?"?":"")+"\\"+r.substring(a))):l+="/"+r;return{keys:o,pattern:new RegExp("^"+l+(e?"(?=$|/)":"/?$"),"i")}}var ad={exports:{}},od={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ys=m;function Cp(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var kp=typeof Object.is=="function"?Object.is:Cp,Tp=Ys.useState,Ap=Ys.useEffect,Pp=Ys.useLayoutEffect,_p=Ys.useDebugValue;function Ep(t,e){var s=e(),n=Tp({inst:{value:s,getSnapshot:e}}),r=n[0].inst,a=n[1];return Pp(function(){r.value=s,r.getSnapshot=e,li(r)&&a({inst:r})},[t,s,e]),Ap(function(){return li(r)&&a({inst:r}),t(function(){li(r)&&a({inst:r})})},[t]),_p(s),s}function li(t){var e=t.getSnapshot;t=t.value;try{var s=e();return!kp(t,s)}catch{return!0}}function Rp(t,e){return e()}var Mp=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Rp:Ep;od.useSyncExternalStore=Ys.useSyncExternalStore!==void 0?Ys.useSyncExternalStore:Mp;ad.exports=od;var Ip=ad.exports;const Vp=Um.useInsertionEffect,Dp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Lp=Dp?m.useLayoutEffect:m.useEffect,Op=Vp||Lp,ld=t=>{const e=m.useRef([t,(...s)=>e[0](...s)]).current;return Op(()=>{e[0]=t}),e[1]},Fp="popstate",ha="pushState",fa="replaceState",Bp="hashchange",So=[Fp,ha,fa,Bp],zp=t=>{for(const e of So)addEventListener(e,t);return()=>{for(const e of So)removeEventListener(e,t)}},cd=(t,e)=>Ip.useSyncExternalStore(zp,t,e),Up=()=>location.search,Wp=({ssrSearch:t=""}={})=>cd(Up,()=>t),Co=()=>location.pathname,$p=({ssrPath:t}={})=>cd(Co,t?()=>t:Co),Hp=(t,{replace:e=!1,state:s=null}={})=>history[e?fa:ha](s,"",t),Zp=(t={})=>[$p(t),Hp],ko=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[ko]>"u"){for(const t of[ha,fa]){const e=history[t];history[t]=function(){const s=e.apply(this,arguments),n=new Event(t);return n.arguments=arguments,dispatchEvent(n),s}}Object.defineProperty(window,ko,{value:!0})}const Kp=(t,e)=>e.toLowerCase().indexOf(t.toLowerCase())?"~"+e:e.slice(t.length)||"/",dd=(t="")=>t==="/"?"":t,qp=(t,e)=>t[0]==="~"?t.slice(1):dd(e)+t,Gp=(t="",e)=>Kp(To(dd(t)),To(e)),To=t=>{try{return decodeURI(t)}catch{return t}},ud={hook:Zp,searchHook:Wp,parser:Sp,base:"",ssrPath:void 0,ssrSearch:void 0,ssrContext:void 0,hrefs:t=>t},hd=m.createContext(ud),Bn=()=>m.useContext(hd),fd={},md=m.createContext(fd),Yp=()=>m.useContext(md),Lr=t=>{const[e,s]=t.hook(t);return[Gp(t.base,e),ld((n,r)=>s(qp(n,t.base),r))]},Xp=()=>Lr(Bn()),pd=(t,e,s,n)=>{const{pattern:r,keys:a}=e instanceof RegExp?{keys:!1,pattern:e}:t(e||"*",n),o=r.exec(s)||[],[l,...c]=o;return l!==void 0?[!0,(()=>{const d=a!==!1?Object.fromEntries(a.map((h,f)=>[h,c[f]])):o.groups;let u={...c};return d&&Object.assign(u,d),u})(),...n?[l]:[]]:[!1,null]},Qp=({children:t,...e})=>{var u,h;const s=Bn(),n=e.hook?ud:s;let r=n;const[a,o]=((u=e.ssrPath)==null?void 0:u.split("?"))??[];o&&(e.ssrSearch=o,e.ssrPath=a),e.hrefs=e.hrefs??((h=e.hook)==null?void 0:h.hrefs);let l=m.useRef({}),c=l.current,d=c;for(let f in n){const x=f==="base"?n[f]+(e[f]||""):e[f]||n[f];c===d&&x!==d[f]&&(l.current=d={...d}),d[f]=x,(x!==n[f]||x!==r[f])&&(r=d)}return m.createElement(hd.Provider,{value:r,children:t})},Ao=({children:t,component:e},s)=>e?m.createElement(e,{params:s}):typeof t=="function"?t(s):t,Jp=t=>{let e=m.useRef(fd);const s=e.current;return e.current=Object.keys(t).length!==Object.keys(s).length||Object.entries(t).some(([n,r])=>r!==s[n])?t:s},_s=({path:t,nest:e,match:s,...n})=>{const r=Bn(),[a]=Lr(r),[o,l,c]=s??pd(r.parser,t,a,e),d=Jp({...Yp(),...l});if(!o)return null;const u=c?m.createElement(Qp,{base:c},Ao(n,d)):Ao(n,d);return m.createElement(md.Provider,{value:d,children:u})},ae=m.forwardRef((t,e)=>{const s=Bn(),[n,r]=Lr(s),{to:a="",href:o=a,onClick:l,asChild:c,children:d,className:u,replace:h,state:f,...x}=t,b=ld(g=>{g.ctrlKey||g.metaKey||g.altKey||g.shiftKey||g.button!==0||(l==null||l(g),g.defaultPrevented||(g.preventDefault(),r(o,t)))}),p=s.hrefs(o[0]==="~"?o.slice(1):s.base+o,s);return c&&m.isValidElement(d)?m.cloneElement(d,{onClick:b,href:p}):m.createElement("a",{...x,onClick:b,href:p,className:u!=null&&u.call?u(n===o):u,children:d,ref:e})}),gd=t=>Array.isArray(t)?t.flatMap(e=>gd(e&&e.type===m.Fragment?e.props.children:e)):[t],eg=({children:t,location:e})=>{const s=Bn(),[n]=Lr(s);for(const r of gd(t)){let a=0;if(m.isValidElement(r)&&(a=pd(s.parser,r.props.path,e||n,r.props.nest))[0])return m.cloneElement(r,{match:a})}return null},tg=1,sg=1e6;let ci=0;function ng(){return ci=(ci+1)%Number.MAX_SAFE_INTEGER,ci.toString()}const di=new Map,Po=t=>{if(di.has(t))return;const e=setTimeout(()=>{di.delete(t),bn({type:"REMOVE_TOAST",toastId:t})},sg);di.set(t,e)},rg=(t,e)=>{switch(e.type){case"ADD_TOAST":return{...t,toasts:[e.toast,...t.toasts].slice(0,tg)};case"UPDATE_TOAST":return{...t,toasts:t.toasts.map(s=>s.id===e.toast.id?{...s,...e.toast}:s)};case"DISMISS_TOAST":{const{toastId:s}=e;return s?Po(s):t.toasts.forEach(n=>{Po(n.id)}),{...t,toasts:t.toasts.map(n=>n.id===s||s===void 0?{...n,open:!1}:n)}}case"REMOVE_TOAST":return e.toastId===void 0?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(s=>s.id!==e.toastId)}}},nr=[];let rr={toasts:[]};function bn(t){rr=rg(rr,t),nr.forEach(e=>{e(rr)})}function ig({...t}){const e=ng(),s=r=>bn({type:"UPDATE_TOAST",toast:{...r,id:e}}),n=()=>bn({type:"DISMISS_TOAST",toastId:e});return bn({type:"ADD_TOAST",toast:{...t,id:e,open:!0,onOpenChange:r=>{r||n()}}}),{id:e,dismiss:n,update:s}}function xd(){const[t,e]=m.useState(rr);return m.useEffect(()=>(nr.push(e),()=>{const s=nr.indexOf(e);s>-1&&nr.splice(s,1)}),[t]),{...t,toast:ig,dismiss:s=>bn({type:"DISMISS_TOAST",toastId:s})}}var yd=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ag="VisuallyHidden",Or=m.forwardRef((t,e)=>i.jsx(ue.span,{...t,ref:e,style:{...yd,...t.style}}));Or.displayName=ag;var og=Or,ma="ToastProvider",[pa,lg,cg]=Vc("Toast"),[vd,Oj]=On("Toast",[cg]),[dg,Fr]=vd(ma),bd=t=>{const{__scopeToast:e,label:s="Notification",duration:n=5e3,swipeDirection:r="right",swipeThreshold:a=50,children:o}=t,[l,c]=m.useState(null),[d,u]=m.useState(0),h=m.useRef(!1),f=m.useRef(!1);return s.trim()||console.error(`Invalid prop \`label\` supplied to \`${ma}\`. Expected non-empty \`string\`.`),i.jsx(pa.Provider,{scope:e,children:i.jsx(dg,{scope:e,label:s,duration:n,swipeDirection:r,swipeThreshold:a,toastCount:d,viewport:l,onViewportChange:c,onToastAdd:m.useCallback(()=>u(x=>x+1),[]),onToastRemove:m.useCallback(()=>u(x=>x-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:f,children:o})})};bd.displayName=ma;var wd="ToastViewport",ug=["F8"],_i="toast.viewportPause",Ei="toast.viewportResume",jd=m.forwardRef((t,e)=>{const{__scopeToast:s,hotkey:n=ug,label:r="Notifications ({hotkey})",...a}=t,o=Fr(wd,s),l=lg(s),c=m.useRef(null),d=m.useRef(null),u=m.useRef(null),h=m.useRef(null),f=_e(e,h,o.onViewportChange),x=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=o.toastCount>0;m.useEffect(()=>{const g=j=>{var w;n.length!==0&&n.every(T=>j[T]||j.code===T)&&((w=h.current)==null||w.focus())};return document.addEventListener("keydown",g),()=>document.removeEventListener("keydown",g)},[n]),m.useEffect(()=>{const g=c.current,j=h.current;if(b&&g&&j){const v=()=>{if(!o.isClosePausedRef.current){const M=new CustomEvent(_i);j.dispatchEvent(M),o.isClosePausedRef.current=!0}},w=()=>{if(o.isClosePausedRef.current){const M=new CustomEvent(Ei);j.dispatchEvent(M),o.isClosePausedRef.current=!1}},T=M=>{!g.contains(M.relatedTarget)&&w()},C=()=>{g.contains(document.activeElement)||w()};return g.addEventListener("focusin",v),g.addEventListener("focusout",T),g.addEventListener("pointermove",v),g.addEventListener("pointerleave",C),window.addEventListener("blur",v),window.addEventListener("focus",w),()=>{g.removeEventListener("focusin",v),g.removeEventListener("focusout",T),g.removeEventListener("pointermove",v),g.removeEventListener("pointerleave",C),window.removeEventListener("blur",v),window.removeEventListener("focus",w)}}},[b,o.isClosePausedRef]);const p=m.useCallback(({tabbingDirection:g})=>{const v=l().map(w=>{const T=w.ref.current,C=[T,...Sg(T)];return g==="forwards"?C:C.reverse()});return(g==="forwards"?v.reverse():v).flat()},[l]);return m.useEffect(()=>{const g=h.current;if(g){const j=v=>{var C,M,O;const w=v.altKey||v.ctrlKey||v.metaKey;if(v.key==="Tab"&&!w){const I=document.activeElement,_=v.shiftKey;if(v.target===g&&_){(C=d.current)==null||C.focus();return}const U=p({tabbingDirection:_?"backwards":"forwards"}),G=U.findIndex(V=>V===I);ui(U.slice(G+1))?v.preventDefault():_?(M=d.current)==null||M.focus():(O=u.current)==null||O.focus()}};return g.addEventListener("keydown",j),()=>g.removeEventListener("keydown",j)}},[l,p]),i.jsxs(km,{ref:c,role:"region","aria-label":r.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&i.jsx(Ri,{ref:d,onFocusFromOutsideViewport:()=>{const g=p({tabbingDirection:"forwards"});ui(g)}}),i.jsx(pa.Slot,{scope:s,children:i.jsx(ue.ol,{tabIndex:-1,...a,ref:f})}),b&&i.jsx(Ri,{ref:u,onFocusFromOutsideViewport:()=>{const g=p({tabbingDirection:"backwards"});ui(g)}})]})});jd.displayName=wd;var Nd="ToastFocusProxy",Ri=m.forwardRef((t,e)=>{const{__scopeToast:s,onFocusFromOutsideViewport:n,...r}=t,a=Fr(Nd,s);return i.jsx(Or,{"aria-hidden":!0,tabIndex:0,...r,ref:e,style:{position:"fixed"},onFocus:o=>{var d;const l=o.relatedTarget;!((d=a.viewport)!=null&&d.contains(l))&&n()}})});Ri.displayName=Nd;var zn="Toast",hg="toast.swipeStart",fg="toast.swipeMove",mg="toast.swipeCancel",pg="toast.swipeEnd",Sd=m.forwardRef((t,e)=>{const{forceMount:s,open:n,defaultOpen:r,onOpenChange:a,...o}=t,[l,c]=ur({prop:n,defaultProp:r??!0,onChange:a,caller:zn});return i.jsx(Ts,{present:s||l,children:i.jsx(yg,{open:l,...o,ref:e,onClose:()=>c(!1),onPause:et(t.onPause),onResume:et(t.onResume),onSwipeStart:J(t.onSwipeStart,d=>{d.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:J(t.onSwipeMove,d=>{const{x:u,y:h}=d.detail.delta;d.currentTarget.setAttribute("data-swipe","move"),d.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${u}px`),d.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${h}px`)}),onSwipeCancel:J(t.onSwipeCancel,d=>{d.currentTarget.setAttribute("data-swipe","cancel"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),d.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:J(t.onSwipeEnd,d=>{const{x:u,y:h}=d.detail.delta;d.currentTarget.setAttribute("data-swipe","end"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),d.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),d.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${u}px`),d.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${h}px`),c(!1)})})})});Sd.displayName=zn;var[gg,xg]=vd(zn,{onClose(){}}),yg=m.forwardRef((t,e)=>{const{__scopeToast:s,type:n="foreground",duration:r,open:a,onClose:o,onEscapeKeyDown:l,onPause:c,onResume:d,onSwipeStart:u,onSwipeMove:h,onSwipeCancel:f,onSwipeEnd:x,...b}=t,p=Fr(zn,s),[g,j]=m.useState(null),v=_e(e,V=>j(V)),w=m.useRef(null),T=m.useRef(null),C=r||p.duration,M=m.useRef(0),O=m.useRef(C),I=m.useRef(0),{onToastAdd:_,onToastRemove:$}=p,q=et(()=>{var te;(g==null?void 0:g.contains(document.activeElement))&&((te=p.viewport)==null||te.focus()),o()}),U=m.useCallback(V=>{!V||V===1/0||(window.clearTimeout(I.current),M.current=new Date().getTime(),I.current=window.setTimeout(q,V))},[q]);m.useEffect(()=>{const V=p.viewport;if(V){const te=()=>{U(O.current),d==null||d()},se=()=>{const Q=new Date().getTime()-M.current;O.current=O.current-Q,window.clearTimeout(I.current),c==null||c()};return V.addEventListener(_i,se),V.addEventListener(Ei,te),()=>{V.removeEventListener(_i,se),V.removeEventListener(Ei,te)}}},[p.viewport,C,c,d,U]),m.useEffect(()=>{a&&!p.isClosePausedRef.current&&U(C)},[a,C,p.isClosePausedRef,U]),m.useEffect(()=>(_(),()=>$()),[_,$]);const G=m.useMemo(()=>g?Ed(g):null,[g]);return p.viewport?i.jsxs(i.Fragment,{children:[G&&i.jsx(vg,{__scopeToast:s,role:"status","aria-live":n==="foreground"?"assertive":"polite","aria-atomic":!0,children:G}),i.jsx(gg,{scope:s,onClose:q,children:Vr.createPortal(i.jsx(pa.ItemSlot,{scope:s,children:i.jsx(Tm,{asChild:!0,onEscapeKeyDown:J(l,()=>{p.isFocusedToastEscapeKeyDownRef.current||q(),p.isFocusedToastEscapeKeyDownRef.current=!1}),children:i.jsx(ue.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":a?"open":"closed","data-swipe-direction":p.swipeDirection,...b,ref:v,style:{userSelect:"none",touchAction:"none",...t.style},onKeyDown:J(t.onKeyDown,V=>{V.key==="Escape"&&(l==null||l(V.nativeEvent),V.nativeEvent.defaultPrevented||(p.isFocusedToastEscapeKeyDownRef.current=!0,q()))}),onPointerDown:J(t.onPointerDown,V=>{V.button===0&&(w.current={x:V.clientX,y:V.clientY})}),onPointerMove:J(t.onPointerMove,V=>{if(!w.current)return;const te=V.clientX-w.current.x,se=V.clientY-w.current.y,Q=!!T.current,ce=["left","right"].includes(p.swipeDirection),we=["left","up"].includes(p.swipeDirection)?Math.min:Math.max,Ue=ce?we(0,te):0,Nt=ce?0:we(0,se),St=V.pointerType==="touch"?10:2,nt={x:Ue,y:Nt},rt={originalEvent:V,delta:nt};Q?(T.current=nt,Xn(fg,h,rt,{discrete:!1})):_o(nt,p.swipeDirection,St)?(T.current=nt,Xn(hg,u,rt,{discrete:!1}),V.target.setPointerCapture(V.pointerId)):(Math.abs(te)>St||Math.abs(se)>St)&&(w.current=null)}),onPointerUp:J(t.onPointerUp,V=>{const te=T.current,se=V.target;if(se.hasPointerCapture(V.pointerId)&&se.releasePointerCapture(V.pointerId),T.current=null,w.current=null,te){const Q=V.currentTarget,ce={originalEvent:V,delta:te};_o(te,p.swipeDirection,p.swipeThreshold)?Xn(pg,x,ce,{discrete:!0}):Xn(mg,f,ce,{discrete:!0}),Q.addEventListener("click",we=>we.preventDefault(),{once:!0})}})})})}),p.viewport)})]}):null}),vg=t=>{const{__scopeToast:e,children:s,...n}=t,r=Fr(zn,e),[a,o]=m.useState(!1),[l,c]=m.useState(!1);return jg(()=>o(!0)),m.useEffect(()=>{const d=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(d)},[]),l?null:i.jsx(Dc,{asChild:!0,children:i.jsx(Or,{...n,children:a&&i.jsxs(i.Fragment,{children:[r.label," ",s]})})})},bg="ToastTitle",Cd=m.forwardRef((t,e)=>{const{__scopeToast:s,...n}=t;return i.jsx(ue.div,{...n,ref:e})});Cd.displayName=bg;var wg="ToastDescription",kd=m.forwardRef((t,e)=>{const{__scopeToast:s,...n}=t;return i.jsx(ue.div,{...n,ref:e})});kd.displayName=wg;var Td="ToastAction",Ad=m.forwardRef((t,e)=>{const{altText:s,...n}=t;return s.trim()?i.jsx(_d,{altText:s,asChild:!0,children:i.jsx(ga,{...n,ref:e})}):(console.error(`Invalid prop \`altText\` supplied to \`${Td}\`. Expected non-empty \`string\`.`),null)});Ad.displayName=Td;var Pd="ToastClose",ga=m.forwardRef((t,e)=>{const{__scopeToast:s,...n}=t,r=xg(Pd,s);return i.jsx(_d,{asChild:!0,children:i.jsx(ue.button,{type:"button",...n,ref:e,onClick:J(t.onClick,r.onClose)})})});ga.displayName=Pd;var _d=m.forwardRef((t,e)=>{const{__scopeToast:s,altText:n,...r}=t;return i.jsx(ue.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...r,ref:e})});function Ed(t){const e=[];return Array.from(t.childNodes).forEach(n=>{if(n.nodeType===n.TEXT_NODE&&n.textContent&&e.push(n.textContent),Ng(n)){const r=n.ariaHidden||n.hidden||n.style.display==="none",a=n.dataset.radixToastAnnounceExclude==="";if(!r)if(a){const o=n.dataset.radixToastAnnounceAlt;o&&e.push(o)}else e.push(...Ed(n))}}),e}function Xn(t,e,s,{discrete:n}){const r=s.originalEvent.currentTarget,a=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:s});e&&r.addEventListener(t,e,{once:!0}),n?Am(r,a):r.dispatchEvent(a)}var _o=(t,e,s=0)=>{const n=Math.abs(t.x),r=Math.abs(t.y),a=n>r;return e==="left"||e==="right"?a&&n>s:!a&&r>s};function jg(t=()=>{}){const e=et(t);wt(()=>{let s=0,n=0;return s=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(e)),()=>{window.cancelAnimationFrame(s),window.cancelAnimationFrame(n)}},[e])}function Ng(t){return t.nodeType===t.ELEMENT_NODE}function Sg(t){const e=[],s=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const r=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||r?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)e.push(s.currentNode);return e}function ui(t){const e=document.activeElement;return t.some(s=>s===e?!0:(s.focus(),document.activeElement!==e))}var Cg=bd,Rd=jd,Md=Sd,Id=Cd,Vd=kd,Dd=Ad,Ld=ga;const Eo=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,Ro=Kc,Br=(t,e)=>s=>{var n;if((e==null?void 0:e.variants)==null)return Ro(t,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:r,defaultVariants:a}=e,o=Object.keys(r).map(d=>{const u=s==null?void 0:s[d],h=a==null?void 0:a[d];if(u===null)return null;const f=Eo(u)||Eo(h);return r[d][f]}),l=s&&Object.entries(s).reduce((d,u)=>{let[h,f]=u;return f===void 0||(d[h]=f),d},{}),c=e==null||(n=e.compoundVariants)===null||n===void 0?void 0:n.reduce((d,u)=>{let{class:h,className:f,...x}=u;return Object.entries(x).every(b=>{let[p,g]=b;return Array.isArray(g)?g.includes({...a,...l}[p]):{...a,...l}[p]===g})?[...d,h,f]:d},[]);return Ro(t,o,c,s==null?void 0:s.class,s==null?void 0:s.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kg=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Od=(...t)=>t.filter((e,s,n)=>!!e&&n.indexOf(e)===s).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Tg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ag=m.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:r="",children:a,iconNode:o,...l},c)=>m.createElement("svg",{ref:c,...Tg,width:e,height:e,stroke:t,strokeWidth:n?Number(s)*24/Number(e):s,className:Od("lucide",r),...l},[...o.map(([d,u])=>m.createElement(d,u)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=(t,e)=>{const s=m.forwardRef(({className:n,...r},a)=>m.createElement(Ag,{ref:a,iconNode:e,className:Od(`lucide-${kg(t)}`,n),...r}));return s.displayName=`${t}`,s};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pg=re("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=re("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _g=re("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mi=re("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xa=re("BrainCircuit",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M9 13a4.5 4.5 0 0 0 3-4",key:"10igwf"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M16 8V5a2 2 0 0 1 2-2",key:"u6izg6"}],["circle",{cx:"16",cy:"13",r:".5",key:"ry7gng"}],["circle",{cx:"18",cy:"3",r:".5",key:"1aiba7"}],["circle",{cx:"20",cy:"21",r:".5",key:"yhc1fs"}],["circle",{cx:"20",cy:"8",r:".5",key:"1e43v0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eg=re("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rg=re("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mg=re("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ig=re("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fd=re("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dt=re("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vg=re("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ir=re("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dg=re("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lg=re("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Og=re("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fg=re("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bd=re("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bg=re("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zg=re("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ug=re("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wg=re("MonitorSmartphone",[["path",{d:"M18 8V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h8",key:"10dyio"}],["path",{d:"M10 19v-3.96 3.15",key:"1irgej"}],["path",{d:"M7 19h5",key:"qswx4l"}],["rect",{width:"6",height:"10",x:"16",y:"12",rx:"2",key:"1egngj"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $g=re("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hg=re("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zg=re("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kg=re("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zd=re("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qg=re("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gg=re("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=re("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yg=re("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xg=re("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qg=re("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wd=re("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jg=re("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=re("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=re("Workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Un=re("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function ee(...t){return Wm(Kc(t))}function sx(t){return new Date(t).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})}const nx=Cg,$d=m.forwardRef(({className:t,...e},s)=>i.jsx(Rd,{ref:s,className:ee("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",t),...e}));$d.displayName=Rd.displayName;const rx=Br("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Hd=m.forwardRef(({className:t,variant:e,...s},n)=>i.jsx(Md,{ref:n,className:ee(rx({variant:e}),t),...s}));Hd.displayName=Md.displayName;const ix=m.forwardRef(({className:t,...e},s)=>i.jsx(Dd,{ref:s,className:ee("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",t),...e}));ix.displayName=Dd.displayName;const Zd=m.forwardRef(({className:t,...e},s)=>i.jsx(Ld,{ref:s,className:ee("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",t),"toast-close":"",...e,children:i.jsx(Un,{className:"h-4 w-4"})}));Zd.displayName=Ld.displayName;const Kd=m.forwardRef(({className:t,...e},s)=>i.jsx(Id,{ref:s,className:ee("text-sm font-semibold",t),...e}));Kd.displayName=Id.displayName;const qd=m.forwardRef(({className:t,...e},s)=>i.jsx(Vd,{ref:s,className:ee("text-sm opacity-90",t),...e}));qd.displayName=Vd.displayName;function ax(){const{toasts:t}=xd();return i.jsxs(nx,{children:[t.map(function({id:e,title:s,description:n,action:r,...a}){return i.jsxs(Hd,{...a,children:[i.jsxs("div",{className:"grid gap-1",children:[s&&i.jsx(Kd,{children:s}),n&&i.jsx(qd,{children:n})]}),r,i.jsx(Zd,{})]},e)}),i.jsx($d,{})]})}var[zr,Fj]=On("Tooltip",[Mr]),ya=Mr(),Gd="TooltipProvider",ox=700,Mo="tooltip.open",[lx,Yd]=zr(Gd),Xd=t=>{const{__scopeTooltip:e,delayDuration:s=ox,skipDelayDuration:n=300,disableHoverableContent:r=!1,children:a}=t,o=m.useRef(!0),l=m.useRef(!1),c=m.useRef(0);return m.useEffect(()=>{const d=c.current;return()=>window.clearTimeout(d)},[]),i.jsx(lx,{scope:e,isOpenDelayedRef:o,delayDuration:s,onOpen:m.useCallback(()=>{window.clearTimeout(c.current),o.current=!1},[]),onClose:m.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>o.current=!0,n)},[n]),isPointerInTransitRef:l,onPointerInTransitChange:m.useCallback(d=>{l.current=d},[]),disableHoverableContent:r,children:a})};Xd.displayName=Gd;var Qd="Tooltip",[Bj,Ur]=zr(Qd),Ii="TooltipTrigger",cx=m.forwardRef((t,e)=>{const{__scopeTooltip:s,...n}=t,r=Ur(Ii,s),a=Yd(Ii,s),o=ya(s),l=m.useRef(null),c=_e(e,l,r.onTriggerChange),d=m.useRef(!1),u=m.useRef(!1),h=m.useCallback(()=>d.current=!1,[]);return m.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),i.jsx(Fc,{asChild:!0,...o,children:i.jsx(ue.button,{"aria-describedby":r.open?r.contentId:void 0,"data-state":r.stateAttribute,...n,ref:c,onPointerMove:J(t.onPointerMove,f=>{f.pointerType!=="touch"&&!u.current&&!a.isPointerInTransitRef.current&&(r.onTriggerEnter(),u.current=!0)}),onPointerLeave:J(t.onPointerLeave,()=>{r.onTriggerLeave(),u.current=!1}),onPointerDown:J(t.onPointerDown,()=>{r.open&&r.onClose(),d.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:J(t.onFocus,()=>{d.current||r.onOpen()}),onBlur:J(t.onBlur,r.onClose),onClick:J(t.onClick,r.onClose)})})});cx.displayName=Ii;var dx="TooltipPortal",[zj,ux]=zr(dx,{forceMount:void 0}),Xs="TooltipContent",Jd=m.forwardRef((t,e)=>{const s=ux(Xs,t.__scopeTooltip),{forceMount:n=s.forceMount,side:r="top",...a}=t,o=Ur(Xs,t.__scopeTooltip);return i.jsx(Ts,{present:n||o.open,children:o.disableHoverableContent?i.jsx(eu,{side:r,...a,ref:e}):i.jsx(hx,{side:r,...a,ref:e})})}),hx=m.forwardRef((t,e)=>{const s=Ur(Xs,t.__scopeTooltip),n=Yd(Xs,t.__scopeTooltip),r=m.useRef(null),a=_e(e,r),[o,l]=m.useState(null),{trigger:c,onClose:d}=s,u=r.current,{onPointerInTransitChange:h}=n,f=m.useCallback(()=>{l(null),h(!1)},[h]),x=m.useCallback((b,p)=>{const g=b.currentTarget,j={x:b.clientX,y:b.clientY},v=xx(j,g.getBoundingClientRect()),w=yx(j,v),T=vx(p.getBoundingClientRect()),C=wx([...w,...T]);l(C),h(!0)},[h]);return m.useEffect(()=>()=>f(),[f]),m.useEffect(()=>{if(c&&u){const b=g=>x(g,u),p=g=>x(g,c);return c.addEventListener("pointerleave",b),u.addEventListener("pointerleave",p),()=>{c.removeEventListener("pointerleave",b),u.removeEventListener("pointerleave",p)}}},[c,u,x,f]),m.useEffect(()=>{if(o){const b=p=>{const g=p.target,j={x:p.clientX,y:p.clientY},v=(c==null?void 0:c.contains(g))||(u==null?void 0:u.contains(g)),w=!bx(j,o);v?f():w&&(f(),d())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[c,u,o,d,f]),i.jsx(eu,{...t,ref:a})}),[fx,mx]=zr(Qd,{isInside:!1}),px=Pm("TooltipContent"),eu=m.forwardRef((t,e)=>{const{__scopeTooltip:s,children:n,"aria-label":r,onEscapeKeyDown:a,onPointerDownOutside:o,...l}=t,c=Ur(Xs,s),d=ya(s),{onClose:u}=c;return m.useEffect(()=>(document.addEventListener(Mo,u),()=>document.removeEventListener(Mo,u)),[u]),m.useEffect(()=>{if(c.trigger){const h=f=>{const x=f.target;x!=null&&x.contains(c.trigger)&&u()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[c.trigger,u]),i.jsx(Lc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:o,onFocusOutside:h=>h.preventDefault(),onDismiss:u,children:i.jsxs(Oc,{"data-state":c.stateAttribute,...d,...l,ref:e,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[i.jsx(px,{children:n}),i.jsx(fx,{scope:s,isInside:!0,children:i.jsx(og,{id:c.contentId,role:"tooltip",children:r||n})})]})})});Jd.displayName=Xs;var tu="TooltipArrow",gx=m.forwardRef((t,e)=>{const{__scopeTooltip:s,...n}=t,r=ya(s);return mx(tu,s).isInside?null:i.jsx(Bc,{...r,...n,ref:e})});gx.displayName=tu;function xx(t,e){const s=Math.abs(e.top-t.y),n=Math.abs(e.bottom-t.y),r=Math.abs(e.right-t.x),a=Math.abs(e.left-t.x);switch(Math.min(s,n,r,a)){case a:return"left";case r:return"right";case s:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function yx(t,e,s=5){const n=[];switch(e){case"top":n.push({x:t.x-s,y:t.y+s},{x:t.x+s,y:t.y+s});break;case"bottom":n.push({x:t.x-s,y:t.y-s},{x:t.x+s,y:t.y-s});break;case"left":n.push({x:t.x+s,y:t.y-s},{x:t.x+s,y:t.y+s});break;case"right":n.push({x:t.x-s,y:t.y-s},{x:t.x-s,y:t.y+s});break}return n}function vx(t){const{top:e,right:s,bottom:n,left:r}=t;return[{x:r,y:e},{x:s,y:e},{x:s,y:n},{x:r,y:n}]}function bx(t,e){const{x:s,y:n}=t;let r=!1;for(let a=0,o=e.length-1;a<e.length;o=a++){const l=e[a],c=e[o],d=l.x,u=l.y,h=c.x,f=c.y;u>n!=f>n&&s<(h-d)*(n-u)/(f-u)+d&&(r=!r)}return r}function wx(t){const e=t.slice();return e.sort((s,n)=>s.x<n.x?-1:s.x>n.x?1:s.y<n.y?-1:s.y>n.y?1:0),jx(e)}function jx(t){if(t.length<=1)return t.slice();const e=[];for(let n=0;n<t.length;n++){const r=t[n];for(;e.length>=2;){const a=e[e.length-1],o=e[e.length-2];if((a.x-o.x)*(r.y-o.y)>=(a.y-o.y)*(r.x-o.x))e.pop();else break}e.push(r)}e.pop();const s=[];for(let n=t.length-1;n>=0;n--){const r=t[n];for(;s.length>=2;){const a=s[s.length-1],o=s[s.length-2];if((a.x-o.x)*(r.y-o.y)>=(a.y-o.y)*(r.x-o.x))s.pop();else break}s.push(r)}return s.pop(),e.length===1&&s.length===1&&e[0].x===s[0].x&&e[0].y===s[0].y?e:e.concat(s)}var Nx=Xd,su=Jd;const Sx=Nx,Cx=m.forwardRef(({className:t,sideOffset:e=4,...s},n)=>i.jsx(su,{ref:n,sideOffset:e,className:ee("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",t),...s}));Cx.displayName=su.displayName;const kx=Br("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),me=m.forwardRef(({className:t,variant:e,size:s,asChild:n=!1,...r},a)=>{const o=n?zc:"button";return i.jsx(o,{className:ee(kx({variant:e,size:s,className:t})),ref:a,...r})});me.displayName="Button";function Tx(){return i.jsxs("div",{className:"flex flex-col items-center justify-center min-h-[70vh] px-4 text-center",children:[i.jsx("h1",{className:"text-9xl font-bold text-primary",children:"404"}),i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mt-6 mb-8",children:"Page Not Found"}),i.jsx("p",{className:"text-lg text-muted-foreground max-w-md mb-8",children:"Sorry, the page you are looking for doesn't exist or has been moved."}),i.jsx(ae,{href:"/",children:i.jsxs(me,{children:[i.jsx(Pg,{className:"mr-2 h-4 w-4"}),"Back to Home"]})})]})}const Ax=Rm,Px=Mm,_x=_m,nu=m.forwardRef(({className:t,...e},s)=>i.jsx(Uc,{className:ee("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...e,ref:s}));nu.displayName=Uc.displayName;const Ex=Br("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),ru=m.forwardRef(({side:t="right",className:e,children:s,...n},r)=>i.jsxs(_x,{children:[i.jsx(nu,{}),i.jsxs(Wc,{ref:r,className:ee(Ex({side:t}),e),...n,children:[s,i.jsxs(Em,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[i.jsx(Un,{className:"h-4 w-4"}),i.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));ru.displayName=Wc.displayName;const Rx=m.forwardRef(({className:t,...e},s)=>i.jsx($c,{ref:s,className:ee("text-lg font-semibold text-foreground",t),...e}));Rx.displayName=$c.displayName;const Mx=m.forwardRef(({className:t,...e},s)=>i.jsx(Hc,{ref:s,className:ee("text-sm text-muted-foreground",t),...e}));Mx.displayName=Hc.displayName;function Ix(){const[t,e]=m.useState(!1);return m.useEffect(()=>{const s=()=>{e(window.innerWidth<768)};return window.addEventListener("resize",s),s(),()=>window.removeEventListener("resize",s)},[]),t}const Vx="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCA2NzAgODAiPgogIDwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyOS41LjEsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiAyLjEuMCBCdWlsZCAxNDEpICAtLT4KICA8ZGVmcz4KICAgIDxzdHlsZT4KICAgICAgLnN0MCB7CiAgICAgICAgZmlsbDogIzQ2MDA4YjsKICAgICAgICBmb250LWZhbWlseTogQmxlbmRhU2NyaXB0LCAnQmxlbmRhIFNjcmlwdCc7CiAgICAgICAgZm9udC1zaXplOiA3MnB4OwogICAgICB9CgogICAgICAuc3QxIHsKICAgICAgICBmaWxsOiAjZmFmYWZhOwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8cmVjdCBjbGFzcz0ic3QxIiB3aWR0aD0iNjcwIiBoZWlnaHQ9IjgwIi8+CiAgPHRleHQgY2xhc3M9InN0MCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoOS43OSA2NS40KSI+PHRzcGFuIHg9IjAiIHk9IjAiPk1QIEFkdmFuY2UgU29sdXRpb25zPC90c3Bhbj48L3RleHQ+Cjwvc3ZnPg==",iu="/assets/mp-advabce-solutions-logo-CMEbzAE0.png",fr=({className:t="h-8 w-auto",alt:e="MP Advance Solutions"})=>{const[s,n]=m.useState(!1),r=()=>{n(!0)};return i.jsx("img",{src:s?iu:Vx,alt:e,className:t,onError:r})},Io=[{name:"Home",path:"/"},{name:"Services",path:"/services"},{name:"AI Solutions",path:"/ai-solutions"},{name:"About",path:"/about"},{name:"Contact",path:"/contact"}],Dx=()=>{const[t,e]=m.useState(!1),[s,n]=m.useState(!1),[r]=Xp(),{theme:a,setTheme:o}=Np();return Ix(),m.useEffect(()=>{const l=()=>{window.scrollY>20?n(!0):n(!1)};return window.addEventListener("scroll",l),()=>{window.removeEventListener("scroll",l)}},[]),i.jsx("header",{className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${s?"bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm shadow-sm py-3":"bg-transparent py-5"}`,children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx(ae,{href:"/",children:i.jsx("a",{className:"flex items-center",children:i.jsx(fr,{className:"h-8 w-auto company-name"})})}),i.jsx("nav",{className:"hidden md:flex",children:i.jsx("ul",{className:"flex space-x-8",children:Io.map(l=>i.jsx("li",{children:i.jsx(ae,{href:l.path,children:i.jsx("a",{className:`font-medium hover:text-primary transition-colors ${r===l.path?"text-primary":""}`,children:l.name})})},l.path))})}),i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsx(me,{variant:"ghost",size:"icon",onClick:()=>o(a==="dark"?"light":"dark"),"aria-label":"Toggle theme",children:a==="dark"?i.jsx(Xg,{className:"h-5 w-5"}):i.jsx($g,{className:"h-5 w-5"})}),i.jsx("div",{className:"hidden md:block",children:i.jsx(ae,{href:"/contact",children:i.jsx(me,{children:"Get Started"})})}),i.jsx("div",{className:"md:hidden",children:i.jsxs(Ax,{open:t,onOpenChange:e,children:[i.jsx(Px,{asChild:!0,children:i.jsxs(me,{variant:"ghost",size:"icon",children:[i.jsx(Bg,{className:"h-6 w-6"}),i.jsx("span",{className:"sr-only",children:"Open menu"})]})}),i.jsx(ru,{side:"right",className:"w-full sm:w-80",children:i.jsxs("div",{className:"flex flex-col h-full",children:[i.jsxs("div",{className:"flex items-center justify-between mb-8",children:[i.jsx("div",{className:"flex items-center",children:i.jsx(fr,{className:"h-6 w-auto company-name"})}),i.jsxs(me,{variant:"ghost",size:"icon",onClick:()=>e(!1),children:[i.jsx(Un,{className:"h-5 w-5"}),i.jsx("span",{className:"sr-only",children:"Close menu"})]})]}),i.jsx("nav",{className:"flex-grow",children:i.jsx("ul",{className:"flex flex-col space-y-3",children:Io.map(l=>i.jsx("li",{children:i.jsx(ae,{href:l.path,children:i.jsx("a",{className:`block px-4 py-2 rounded-md text-lg font-medium transition-colors ${r===l.path?"bg-primary/10 text-primary":"hover:bg-muted"}`,onClick:()=>e(!1),children:l.name})})},l.path))})}),i.jsx("div",{className:"mt-auto pt-6",children:i.jsx(ae,{href:"/contact",children:i.jsx(me,{className:"w-full",onClick:()=>e(!1),children:"Get Started"})})})]})})]})})]})]})})})},Lx=()=>i.jsx("footer",{className:"bg-slate-900 text-white pt-16 pb-8",children:i.jsxs("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12",children:[i.jsxs("div",{className:"lg:col-span-2",children:[i.jsx("div",{className:"flex items-center mb-6",children:i.jsx(fr,{className:"h-10 w-auto"})}),i.jsxs("p",{className:"text-slate-400 mb-6 max-w-md",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," provides cutting-edge business technology solutions including AI, ecommerce, digital marketing, and business automation for retail and wholesale businesses."]}),i.jsxs("div",{className:"flex space-x-4",children:[i.jsx("a",{href:"#",className:"bg-slate-800 hover:bg-primary transition-colors duration-200 h-10 w-10 rounded-full flex items-center justify-center",children:i.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{fillRule:"evenodd",d:"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z",clipRule:"evenodd"})})}),i.jsx("a",{href:"#",className:"bg-slate-800 hover:bg-primary transition-colors duration-200 h-10 w-10 rounded-full flex items-center justify-center",children:i.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})}),i.jsx("a",{href:"#",className:"bg-slate-800 hover:bg-primary transition-colors duration-200 h-10 w-10 rounded-full flex items-center justify-center",children:i.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{fillRule:"evenodd",d:"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z",clipRule:"evenodd"})})}),i.jsx("a",{href:"#",className:"bg-slate-800 hover:bg-primary transition-colors duration-200 h-10 w-10 rounded-full flex items-center justify-center",children:i.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:i.jsx("path",{fillRule:"evenodd",d:"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z",clipRule:"evenodd"})})})]})]}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-bold text-lg mb-6",children:"Quick Links"}),i.jsxs("ul",{className:"space-y-4",children:[i.jsx("li",{children:i.jsx(ae,{href:"/",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Home"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/services",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Services"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/ai-solutions",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"AI Solutions"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/about",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"About Us"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/contact",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Contact"})})})]})]}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-bold text-lg mb-6",children:"Services"}),i.jsxs("ul",{className:"space-y-4",children:[i.jsx("li",{children:i.jsx(ae,{href:"/services#ai-solutions",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"AI Solutions"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/services#ecommerce",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Ecommerce"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/services#digital-marketing",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Digital Marketing"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/services#leads-generation",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Leads Generation"})})}),i.jsx("li",{children:i.jsx(ae,{href:"/services#business-automation",children:i.jsx("a",{className:"text-slate-400 hover:text-white transition-colors duration-200",children:"Business Automation"})})})]})]}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-bold text-lg mb-6",children:"Contact Us"}),i.jsxs("ul",{className:"space-y-4",children:[i.jsxs("li",{className:"flex",style:{display:"none"},children:[i.jsxs("svg",{className:"h-6 w-6 text-primary mr-3 flex-shrink-0",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),i.jsxs("span",{className:"text-slate-400",children:["123 Business Avenue, ",i.jsx("br",{}),"Suite 456, ",i.jsx("br",{}),"New York, NY 10001"]})]}),i.jsxs("li",{className:"flex",children:[i.jsx("svg",{className:"h-6 w-6 text-primary mr-3 flex-shrink-0",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),i.jsx("a",{href:"mailto:<EMAIL>",className:"text-slate-400 hover:text-white transition-colors duration-200",children:"<EMAIL>"})]}),i.jsxs("li",{className:"flex",style:{display:"none"},children:[i.jsx("svg",{className:"h-6 w-6 text-primary mr-3 flex-shrink-0",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),i.jsx("a",{href:"tel:+15551234567",className:"text-slate-400 hover:text-white transition-colors duration-200",children:"+****************"})]})]})]})]}),i.jsx("div",{className:"border-t border-slate-800 pt-8 mt-8 text-center text-slate-400 text-sm",children:i.jsxs("p",{children:["© ",new Date().getFullYear()," ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"}),". All rights reserved."]})})]})}),va=m.createContext({});function ba(t){const e=m.useRef(null);return e.current===null&&(e.current=t()),e.current}const Wr=m.createContext(null),wa=m.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class Ox extends m.Component{getSnapshotBeforeUpdate(e){const s=this.props.childRef.current;if(s&&e.isPresent&&!this.props.isPresent){const n=this.props.sizeRef.current;n.height=s.offsetHeight||0,n.width=s.offsetWidth||0,n.top=s.offsetTop,n.left=s.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Fx({children:t,isPresent:e}){const s=m.useId(),n=m.useRef(null),r=m.useRef({width:0,height:0,top:0,left:0}),{nonce:a}=m.useContext(wa);return m.useInsertionEffect(()=>{const{width:o,height:l,top:c,left:d}=r.current;if(e||!n.current||!o||!l)return;n.current.dataset.motionPopId=s;const u=document.createElement("style");return a&&(u.nonce=a),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${l}px !important;
            top: ${c}px !important;
            left: ${d}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[e]),i.jsx(Ox,{isPresent:e,childRef:n,sizeRef:r,children:m.cloneElement(t,{ref:n})})}const Bx=({children:t,initial:e,isPresent:s,onExitComplete:n,custom:r,presenceAffectsLayout:a,mode:o})=>{const l=ba(zx),c=m.useId(),d=m.useCallback(h=>{l.set(h,!0);for(const f of l.values())if(!f)return;n&&n()},[l,n]),u=m.useMemo(()=>({id:c,initial:e,isPresent:s,custom:r,onExitComplete:d,register:h=>(l.set(h,!1),()=>l.delete(h))}),a?[Math.random(),d]:[s,d]);return m.useMemo(()=>{l.forEach((h,f)=>l.set(f,!1))},[s]),m.useEffect(()=>{!s&&!l.size&&n&&n()},[s]),o==="popLayout"&&(t=i.jsx(Fx,{isPresent:s,children:t})),i.jsx(Wr.Provider,{value:u,children:t})};function zx(){return new Map}function au(t=!0){const e=m.useContext(Wr);if(e===null)return[!0,null];const{isPresent:s,onExitComplete:n,register:r}=e,a=m.useId();m.useEffect(()=>{t&&r(a)},[t]);const o=m.useCallback(()=>t&&n&&n(a),[a,n,t]);return!s&&n?[!1,o]:[!0]}const Qn=t=>t.key||"";function Vo(t){const e=[];return m.Children.forEach(t,s=>{m.isValidElement(s)&&e.push(s)}),e}const ja=typeof window<"u",ou=ja?m.useLayoutEffect:m.useEffect,Ux=({children:t,custom:e,initial:s=!0,onExitComplete:n,presenceAffectsLayout:r=!0,mode:a="sync",propagate:o=!1})=>{const[l,c]=au(o),d=m.useMemo(()=>Vo(t),[t]),u=o&&!l?[]:d.map(Qn),h=m.useRef(!0),f=m.useRef(d),x=ba(()=>new Map),[b,p]=m.useState(d),[g,j]=m.useState(d);ou(()=>{h.current=!1,f.current=d;for(let T=0;T<g.length;T++){const C=Qn(g[T]);u.includes(C)?x.delete(C):x.get(C)!==!0&&x.set(C,!1)}},[g,u.length,u.join("-")]);const v=[];if(d!==b){let T=[...d];for(let C=0;C<g.length;C++){const M=g[C],O=Qn(M);u.includes(O)||(T.splice(C,0,M),v.push(M))}a==="wait"&&v.length&&(T=v),j(Vo(T)),p(d);return}const{forceRender:w}=m.useContext(va);return i.jsx(i.Fragment,{children:g.map(T=>{const C=Qn(T),M=o&&!l?!1:d===g||u.includes(C),O=()=>{if(x.has(C))x.set(C,!0);else return;let I=!0;x.forEach(_=>{_||(I=!1)}),I&&(w==null||w(),j(f.current),o&&(c==null||c()),n&&n())};return i.jsx(Bx,{isPresent:M,initial:!h.current||s?void 0:!1,custom:M?void 0:e,presenceAffectsLayout:r,mode:a,onExitComplete:M?void 0:O,children:T},C)})})},Ze=t=>t;let lu=Ze;function Na(t){let e;return()=>(e===void 0&&(e=t()),e)}const Qs=(t,e,s)=>{const n=e-t;return n===0?1:(s-t)/n},_t=t=>t*1e3,Et=t=>t/1e3,Wx={useManualTiming:!1};function $x(t){let e=new Set,s=new Set,n=!1,r=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function l(d){a.has(d)&&(c.schedule(d),t()),d(o)}const c={schedule:(d,u=!1,h=!1)=>{const x=h&&n?e:s;return u&&a.add(d),x.has(d)||x.add(d),d},cancel:d=>{s.delete(d),a.delete(d)},process:d=>{if(o=d,n){r=!0;return}n=!0,[e,s]=[s,e],e.forEach(l),e.clear(),n=!1,r&&(r=!1,c.process(d))}};return c}const Jn=["read","resolveKeyframes","update","preRender","render","postRender"],Hx=40;function cu(t,e){let s=!1,n=!0;const r={delta:0,timestamp:0,isProcessing:!1},a=()=>s=!0,o=Jn.reduce((j,v)=>(j[v]=$x(a),j),{}),{read:l,resolveKeyframes:c,update:d,preRender:u,render:h,postRender:f}=o,x=()=>{const j=performance.now();s=!1,r.delta=n?1e3/60:Math.max(Math.min(j-r.timestamp,Hx),1),r.timestamp=j,r.isProcessing=!0,l.process(r),c.process(r),d.process(r),u.process(r),h.process(r),f.process(r),r.isProcessing=!1,s&&e&&(n=!1,t(x))},b=()=>{s=!0,n=!0,r.isProcessing||t(x)};return{schedule:Jn.reduce((j,v)=>{const w=o[v];return j[v]=(T,C=!1,M=!1)=>(s||b(),w.schedule(T,C,M)),j},{}),cancel:j=>{for(let v=0;v<Jn.length;v++)o[Jn[v]].cancel(j)},state:r,steps:o}}const{schedule:be,cancel:Yt,state:Ie,steps:hi}=cu(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ze,!0),du=m.createContext({strict:!1}),Do={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Js={};for(const t in Do)Js[t]={isEnabled:e=>Do[t].some(s=>!!e[s])};function Zx(t){for(const e in t)Js[e]={...Js[e],...t[e]}}const Kx=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function mr(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Kx.has(t)}let uu=t=>!mr(t);function qx(t){t&&(uu=e=>e.startsWith("on")?!mr(e):t(e))}try{qx(require("@emotion/is-prop-valid").default)}catch{}function Gx(t,e,s){const n={};for(const r in t)r==="values"&&typeof t.values=="object"||(uu(r)||s===!0&&mr(r)||!e&&!mr(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}function Yx(t){if(typeof Proxy>"u")return t;const e=new Map,s=(...n)=>t(...n);return new Proxy(s,{get:(n,r)=>r==="create"?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}const $r=m.createContext({});function An(t){return typeof t=="string"||Array.isArray(t)}function Hr(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const Sa=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ca=["initial",...Sa];function Zr(t){return Hr(t.animate)||Ca.some(e=>An(t[e]))}function hu(t){return!!(Zr(t)||t.variants)}function Xx(t,e){if(Zr(t)){const{initial:s,animate:n}=t;return{initial:s===!1||An(s)?s:void 0,animate:An(n)?n:void 0}}return t.inherit!==!1?e:{}}function Qx(t){const{initial:e,animate:s}=Xx(t,m.useContext($r));return m.useMemo(()=>({initial:e,animate:s}),[Lo(e),Lo(s)])}function Lo(t){return Array.isArray(t)?t.join(" "):t}const Jx=Symbol.for("motionComponentSymbol");function Vs(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function e0(t,e,s){return m.useCallback(n=>{n&&t.onMount&&t.onMount(n),e&&(n?e.mount(n):e.unmount()),s&&(typeof s=="function"?s(n):Vs(s)&&(s.current=n))},[e])}const ka=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),t0="framerAppearId",fu="data-"+ka(t0),{schedule:Ta}=cu(queueMicrotask,!1),mu=m.createContext({});function s0(t,e,s,n,r){var a,o;const{visualElement:l}=m.useContext($r),c=m.useContext(du),d=m.useContext(Wr),u=m.useContext(wa).reducedMotion,h=m.useRef(null);n=n||c.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:l,props:s,presenceContext:d,blockInitialAnimation:d?d.initial===!1:!1,reducedMotionConfig:u}));const f=h.current,x=m.useContext(mu);f&&!f.projection&&r&&(f.type==="html"||f.type==="svg")&&n0(h.current,s,r,x);const b=m.useRef(!1);m.useInsertionEffect(()=>{f&&b.current&&f.update(s,d)});const p=s[fu],g=m.useRef(!!p&&!(!((a=window.MotionHandoffIsComplete)===null||a===void 0)&&a.call(window,p))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,p)));return ou(()=>{f&&(b.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Ta.render(f.render),g.current&&f.animationState&&f.animationState.animateChanges())}),m.useEffect(()=>{f&&(!g.current&&f.animationState&&f.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{var j;(j=window.MotionHandoffMarkAsComplete)===null||j===void 0||j.call(window,p)}),g.current=!1))}),f}function n0(t,e,s,n){const{layoutId:r,layout:a,drag:o,dragConstraints:l,layoutScroll:c,layoutRoot:d}=e;t.projection=new s(t.latestValues,e["data-framer-portal-id"]?void 0:pu(t.parent)),t.projection.setOptions({layoutId:r,layout:a,alwaysMeasureLayout:!!o||l&&Vs(l),visualElement:t,animationType:typeof a=="string"?a:"both",initialPromotionConfig:n,layoutScroll:c,layoutRoot:d})}function pu(t){if(t)return t.options.allowProjection!==!1?t.projection:pu(t.parent)}function r0({preloadedFeatures:t,createVisualElement:e,useRender:s,useVisualState:n,Component:r}){var a,o;t&&Zx(t);function l(d,u){let h;const f={...m.useContext(wa),...d,layoutId:i0(d)},{isStatic:x}=f,b=Qx(d),p=n(d,x);if(!x&&ja){a0();const g=o0(f);h=g.MeasureLayout,b.visualElement=s0(r,p,f,e,g.ProjectionNode)}return i.jsxs($r.Provider,{value:b,children:[h&&b.visualElement?i.jsx(h,{visualElement:b.visualElement,...f}):null,s(r,d,e0(p,b.visualElement,u),p,x,b.visualElement)]})}l.displayName=`motion.${typeof r=="string"?r:`create(${(o=(a=r.displayName)!==null&&a!==void 0?a:r.name)!==null&&o!==void 0?o:""})`}`;const c=m.forwardRef(l);return c[Jx]=r,c}function i0({layoutId:t}){const e=m.useContext(va).id;return e&&t!==void 0?e+"-"+t:t}function a0(t,e){m.useContext(du).strict}function o0(t){const{drag:e,layout:s}=Js;if(!e&&!s)return{};const n={...e,...s};return{MeasureLayout:e!=null&&e.isEnabled(t)||s!=null&&s.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}const l0=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Aa(t){return typeof t!="string"||t.includes("-")?!1:!!(l0.indexOf(t)>-1||/[A-Z]/u.test(t))}function Oo(t){const e=[{},{}];return t==null||t.values.forEach((s,n)=>{e[0][n]=s.get(),e[1][n]=s.getVelocity()}),e}function Pa(t,e,s,n){if(typeof e=="function"){const[r,a]=Oo(n);e=e(s!==void 0?s:t.custom,r,a)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[r,a]=Oo(n);e=e(s!==void 0?s:t.custom,r,a)}return e}const Vi=t=>Array.isArray(t),c0=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),d0=t=>Vi(t)?t[t.length-1]||0:t,Oe=t=>!!(t&&t.getVelocity);function ar(t){const e=Oe(t)?t.get():t;return c0(e)?e.toValue():e}function u0({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:s},n,r,a){const o={latestValues:h0(n,r,a,t),renderState:e()};return s&&(o.onMount=l=>s({props:n,current:l,...o}),o.onUpdate=l=>s(l)),o}const gu=t=>(e,s)=>{const n=m.useContext($r),r=m.useContext(Wr),a=()=>u0(t,e,n,r);return s?a():ba(a)};function h0(t,e,s,n){const r={},a=n(t,{});for(const f in a)r[f]=ar(a[f]);let{initial:o,animate:l}=t;const c=Zr(t),d=hu(t);e&&d&&!c&&t.inherit!==!1&&(o===void 0&&(o=e.initial),l===void 0&&(l=e.animate));let u=s?s.initial===!1:!1;u=u||o===!1;const h=u?l:o;if(h&&typeof h!="boolean"&&!Hr(h)){const f=Array.isArray(h)?h:[h];for(let x=0;x<f.length;x++){const b=Pa(t,f[x]);if(b){const{transitionEnd:p,transition:g,...j}=b;for(const v in j){let w=j[v];if(Array.isArray(w)){const T=u?w.length-1:0;w=w[T]}w!==null&&(r[v]=w)}for(const v in p)r[v]=p[v]}}}return r}const on=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],As=new Set(on),xu=t=>e=>typeof e=="string"&&e.startsWith(t),yu=xu("--"),f0=xu("var(--"),_a=t=>f0(t)?m0.test(t.split("/*")[0].trim()):!1,m0=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,vu=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Mt=(t,e,s)=>s>e?e:s<t?t:s,ln={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Pn={...ln,transform:t=>Mt(0,1,t)},er={...ln,default:1},Wn=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Ot=Wn("deg"),yt=Wn("%"),H=Wn("px"),p0=Wn("vh"),g0=Wn("vw"),Fo={...yt,parse:t=>yt.parse(t)/100,transform:t=>yt.transform(t*100)},x0={borderWidth:H,borderTopWidth:H,borderRightWidth:H,borderBottomWidth:H,borderLeftWidth:H,borderRadius:H,radius:H,borderTopLeftRadius:H,borderTopRightRadius:H,borderBottomRightRadius:H,borderBottomLeftRadius:H,width:H,maxWidth:H,height:H,maxHeight:H,top:H,right:H,bottom:H,left:H,padding:H,paddingTop:H,paddingRight:H,paddingBottom:H,paddingLeft:H,margin:H,marginTop:H,marginRight:H,marginBottom:H,marginLeft:H,backgroundPositionX:H,backgroundPositionY:H},y0={rotate:Ot,rotateX:Ot,rotateY:Ot,rotateZ:Ot,scale:er,scaleX:er,scaleY:er,scaleZ:er,skew:Ot,skewX:Ot,skewY:Ot,distance:H,translateX:H,translateY:H,translateZ:H,x:H,y:H,z:H,perspective:H,transformPerspective:H,opacity:Pn,originX:Fo,originY:Fo,originZ:H},Bo={...ln,transform:Math.round},Ea={...x0,...y0,zIndex:Bo,size:H,fillOpacity:Pn,strokeOpacity:Pn,numOctaves:Bo},v0={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},b0=on.length;function w0(t,e,s){let n="",r=!0;for(let a=0;a<b0;a++){const o=on[a],l=t[o];if(l===void 0)continue;let c=!0;if(typeof l=="number"?c=l===(o.startsWith("scale")?1:0):c=parseFloat(l)===0,!c||s){const d=vu(l,Ea[o]);if(!c){r=!1;const u=v0[o]||o;n+=`${u}(${d}) `}s&&(e[o]=d)}}return n=n.trim(),s?n=s(e,r?"":n):r&&(n="none"),n}function Ra(t,e,s){const{style:n,vars:r,transformOrigin:a}=t;let o=!1,l=!1;for(const c in e){const d=e[c];if(As.has(c)){o=!0;continue}else if(yu(c)){r[c]=d;continue}else{const u=vu(d,Ea[c]);c.startsWith("origin")?(l=!0,a[c]=u):n[c]=u}}if(e.transform||(o||s?n.transform=w0(e,t.transform,s):n.transform&&(n.transform="none")),l){const{originX:c="50%",originY:d="50%",originZ:u=0}=a;n.transformOrigin=`${c} ${d} ${u}`}}const j0={offset:"stroke-dashoffset",array:"stroke-dasharray"},N0={offset:"strokeDashoffset",array:"strokeDasharray"};function S0(t,e,s=1,n=0,r=!0){t.pathLength=1;const a=r?j0:N0;t[a.offset]=H.transform(-n);const o=H.transform(e),l=H.transform(s);t[a.array]=`${o} ${l}`}function zo(t,e,s){return typeof t=="string"?t:H.transform(e+s*t)}function C0(t,e,s){const n=zo(e,t.x,t.width),r=zo(s,t.y,t.height);return`${n} ${r}`}function Ma(t,{attrX:e,attrY:s,attrScale:n,originX:r,originY:a,pathLength:o,pathSpacing:l=1,pathOffset:c=0,...d},u,h){if(Ra(t,d,h),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:f,style:x,dimensions:b}=t;f.transform&&(b&&(x.transform=f.transform),delete f.transform),b&&(r!==void 0||a!==void 0||x.transform)&&(x.transformOrigin=C0(b,r!==void 0?r:.5,a!==void 0?a:.5)),e!==void 0&&(f.x=e),s!==void 0&&(f.y=s),n!==void 0&&(f.scale=n),o!==void 0&&S0(f,o,l,c,!1)}const Ia=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),bu=()=>({...Ia(),attrs:{}}),Va=t=>typeof t=="string"&&t.toLowerCase()==="svg";function wu(t,{style:e,vars:s},n,r){Object.assign(t.style,e,r&&r.getProjectionStyles(n));for(const a in s)t.style.setProperty(a,s[a])}const ju=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Nu(t,e,s,n){wu(t,e,void 0,n);for(const r in e.attrs)t.setAttribute(ju.has(r)?r:ka(r),e.attrs[r])}const pr={};function k0(t){Object.assign(pr,t)}function Su(t,{layout:e,layoutId:s}){return As.has(t)||t.startsWith("origin")||(e||s!==void 0)&&(!!pr[t]||t==="opacity")}function Da(t,e,s){var n;const{style:r}=t,a={};for(const o in r)(Oe(r[o])||e.style&&Oe(e.style[o])||Su(o,t)||((n=s==null?void 0:s.getValue(o))===null||n===void 0?void 0:n.liveStyle)!==void 0)&&(a[o]=r[o]);return a}function Cu(t,e,s){const n=Da(t,e,s);for(const r in t)if(Oe(t[r])||Oe(e[r])){const a=on.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[a]=t[r]}return n}function T0(t,e){try{e.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{e.dimensions={x:0,y:0,width:0,height:0}}}const Uo=["x","y","width","height","cx","cy","r"],A0={useVisualState:gu({scrapeMotionValuesFromProps:Cu,createRenderState:bu,onUpdate:({props:t,prevProps:e,current:s,renderState:n,latestValues:r})=>{if(!s)return;let a=!!t.drag;if(!a){for(const l in r)if(As.has(l)){a=!0;break}}if(!a)return;let o=!e;if(e)for(let l=0;l<Uo.length;l++){const c=Uo[l];t[c]!==e[c]&&(o=!0)}o&&be.read(()=>{T0(s,n),be.render(()=>{Ma(n,r,Va(s.tagName),t.transformTemplate),Nu(s,n)})})}})},P0={useVisualState:gu({scrapeMotionValuesFromProps:Da,createRenderState:Ia})};function ku(t,e,s){for(const n in e)!Oe(e[n])&&!Su(n,s)&&(t[n]=e[n])}function _0({transformTemplate:t},e){return m.useMemo(()=>{const s=Ia();return Ra(s,e,t),Object.assign({},s.vars,s.style)},[e])}function E0(t,e){const s=t.style||{},n={};return ku(n,s,t),Object.assign(n,_0(t,e)),n}function R0(t,e){const s={},n=E0(t,e);return t.drag&&t.dragListener!==!1&&(s.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=n,s}function M0(t,e,s,n){const r=m.useMemo(()=>{const a=bu();return Ma(a,e,Va(n),t.transformTemplate),{...a.attrs,style:{...a.style}}},[e]);if(t.style){const a={};ku(a,t.style,t),r.style={...a,...r.style}}return r}function I0(t=!1){return(s,n,r,{latestValues:a},o)=>{const c=(Aa(s)?M0:R0)(n,a,o,s),d=Gx(n,typeof s=="string",t),u=s!==m.Fragment?{...d,...c,ref:r}:{},{children:h}=n,f=m.useMemo(()=>Oe(h)?h.get():h,[h]);return m.createElement(s,{...u,children:f})}}function V0(t,e){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){const o={...Aa(n)?A0:P0,preloadedFeatures:t,useRender:I0(r),createVisualElement:e,Component:n};return r0(o)}}function Tu(t,e){if(!Array.isArray(e))return!1;const s=e.length;if(s!==t.length)return!1;for(let n=0;n<s;n++)if(e[n]!==t[n])return!1;return!0}function Kr(t,e,s){const n=t.getProps();return Pa(n,e,s!==void 0?s:n.custom,t)}const D0=Na(()=>window.ScrollTimeline!==void 0);class L0{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,s){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=s}attachTimeline(e,s){const n=this.animations.map(r=>{if(D0()&&r.attachTimeline)return r.attachTimeline(e);if(typeof s=="function")return s(r)});return()=>{n.forEach((r,a)=>{r&&r(),this.animations[a].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let s=0;s<this.animations.length;s++)e=Math.max(e,this.animations[s].duration);return e}runAll(e){this.animations.forEach(s=>s[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class O0 extends L0{then(e,s){return Promise.all(this.animations).then(e).catch(s)}}function La(t,e){return t?t[e]||t.default||t:void 0}const Di=2e4;function Au(t){let e=0;const s=50;let n=t.next(e);for(;!n.done&&e<Di;)e+=s,n=t.next(e);return e>=Di?1/0:e}function Oa(t){return typeof t=="function"}function Wo(t,e){t.timeline=e,t.onfinish=null}const Fa=t=>Array.isArray(t)&&typeof t[0]=="number",F0={linearEasing:void 0};function B0(t,e){const s=Na(t);return()=>{var n;return(n=F0[e])!==null&&n!==void 0?n:s()}}const gr=B0(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Pu=(t,e,s=10)=>{let n="";const r=Math.max(Math.round(e/s),2);for(let a=0;a<r;a++)n+=t(Qs(0,r-1,a))+", ";return`linear(${n.substring(0,n.length-2)})`};function _u(t){return!!(typeof t=="function"&&gr()||!t||typeof t=="string"&&(t in Li||gr())||Fa(t)||Array.isArray(t)&&t.every(_u))}const pn=([t,e,s,n])=>`cubic-bezier(${t}, ${e}, ${s}, ${n})`,Li={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:pn([0,.65,.55,1]),circOut:pn([.55,0,1,.45]),backIn:pn([.31,.01,.66,-.59]),backOut:pn([.33,1.53,.69,.99])};function Eu(t,e){if(t)return typeof t=="function"&&gr()?Pu(t,e):Fa(t)?pn(t):Array.isArray(t)?t.map(s=>Eu(s,e)||Li.easeOut):Li[t]}const it={x:!1,y:!1};function Ru(){return it.x||it.y}function z0(t,e,s){var n;if(t instanceof Element)return[t];if(typeof t=="string"){let r=document;const a=(n=void 0)!==null&&n!==void 0?n:r.querySelectorAll(t);return a?Array.from(a):[]}return Array.from(t)}function Mu(t,e){const s=z0(t),n=new AbortController,r={passive:!0,...e,signal:n.signal};return[s,r,()=>n.abort()]}function $o(t){return e=>{e.pointerType==="touch"||Ru()||t(e)}}function U0(t,e,s={}){const[n,r,a]=Mu(t,s),o=$o(l=>{const{target:c}=l,d=e(l);if(typeof d!="function"||!c)return;const u=$o(h=>{d(h),c.removeEventListener("pointerleave",u)});c.addEventListener("pointerleave",u,r)});return n.forEach(l=>{l.addEventListener("pointerenter",o,r)}),a}const Iu=(t,e)=>e?t===e?!0:Iu(t,e.parentElement):!1,Ba=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,W0=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function $0(t){return W0.has(t.tagName)||t.tabIndex!==-1}const gn=new WeakSet;function Ho(t){return e=>{e.key==="Enter"&&t(e)}}function fi(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const H0=(t,e)=>{const s=t.currentTarget;if(!s)return;const n=Ho(()=>{if(gn.has(s))return;fi(s,"down");const r=Ho(()=>{fi(s,"up")}),a=()=>fi(s,"cancel");s.addEventListener("keyup",r,e),s.addEventListener("blur",a,e)});s.addEventListener("keydown",n,e),s.addEventListener("blur",()=>s.removeEventListener("keydown",n),e)};function Zo(t){return Ba(t)&&!Ru()}function Z0(t,e,s={}){const[n,r,a]=Mu(t,s),o=l=>{const c=l.currentTarget;if(!Zo(l)||gn.has(c))return;gn.add(c);const d=e(l),u=(x,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),!(!Zo(x)||!gn.has(c))&&(gn.delete(c),typeof d=="function"&&d(x,{success:b}))},h=x=>{u(x,s.useGlobalTarget||Iu(c,x.target))},f=x=>{u(x,!1)};window.addEventListener("pointerup",h,r),window.addEventListener("pointercancel",f,r)};return n.forEach(l=>{!$0(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(s.useGlobalTarget?window:l).addEventListener("pointerdown",o,r),l.addEventListener("focus",d=>H0(d,r),r)}),a}function K0(t){return t==="x"||t==="y"?it[t]?null:(it[t]=!0,()=>{it[t]=!1}):it.x||it.y?null:(it.x=it.y=!0,()=>{it.x=it.y=!1})}const Vu=new Set(["width","height","top","left","right","bottom",...on]);let or;function q0(){or=void 0}const vt={now:()=>(or===void 0&&vt.set(Ie.isProcessing||Wx.useManualTiming?Ie.timestamp:performance.now()),or),set:t=>{or=t,queueMicrotask(q0)}};function za(t,e){t.indexOf(e)===-1&&t.push(e)}function Ua(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}class Wa{constructor(){this.subscriptions=[]}add(e){return za(this.subscriptions,e),()=>Ua(this.subscriptions,e)}notify(e,s,n){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](e,s,n);else for(let a=0;a<r;a++){const o=this.subscriptions[a];o&&o(e,s,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Du(t,e){return e?t*(1e3/e):0}const Ko=30,G0=t=>!isNaN(parseFloat(t));class Y0{constructor(e,s={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,r=!0)=>{const a=vt.now();this.updatedAt!==a&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),r&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=s.owner}setCurrent(e){this.current=e,this.updatedAt=vt.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=G0(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,s){this.events[e]||(this.events[e]=new Wa);const n=this.events[e].add(s);return e==="change"?()=>{n(),be.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,s){this.passiveEffect=e,this.stopPassiveEffect=s}set(e,s=!0){!s||!this.passiveEffect?this.updateAndNotify(e,s):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,s,n){this.set(s),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,s=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,s&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=vt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Ko)return 0;const s=Math.min(this.updatedAt-this.prevUpdatedAt,Ko);return Du(parseFloat(this.current)-parseFloat(this.prevFrameValue),s)}start(e){return this.stop(),new Promise(s=>{this.hasAnimated=!0,this.animation=e(s),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function _n(t,e){return new Y0(t,e)}function X0(t,e,s){t.hasValue(e)?t.getValue(e).set(s):t.addValue(e,_n(s))}function Q0(t,e){const s=Kr(t,e);let{transitionEnd:n={},transition:r={},...a}=s||{};a={...a,...n};for(const o in a){const l=d0(a[o]);X0(t,o,l)}}function J0(t){return!!(Oe(t)&&t.add)}function Oi(t,e){const s=t.getValue("willChange");if(J0(s))return s.add(e)}function Lu(t){return t.props[fu]}const Ou=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t,ey=1e-7,ty=12;function sy(t,e,s,n,r){let a,o,l=0;do o=e+(s-e)/2,a=Ou(o,n,r)-t,a>0?s=o:e=o;while(Math.abs(a)>ey&&++l<ty);return o}function $n(t,e,s,n){if(t===e&&s===n)return Ze;const r=a=>sy(a,0,1,t,s);return a=>a===0||a===1?a:Ou(r(a),e,n)}const Fu=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Bu=t=>e=>1-t(1-e),zu=$n(.33,1.53,.69,.99),$a=Bu(zu),Uu=Fu($a),Wu=t=>(t*=2)<1?.5*$a(t):.5*(2-Math.pow(2,-10*(t-1))),Ha=t=>1-Math.sin(Math.acos(t)),$u=Bu(Ha),Hu=Fu(Ha),Zu=t=>/^0[^.\s]+$/u.test(t);function ny(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Zu(t):!0}const wn=t=>Math.round(t*1e5)/1e5,Za=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function ry(t){return t==null}const iy=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ka=(t,e)=>s=>!!(typeof s=="string"&&iy.test(s)&&s.startsWith(t)||e&&!ry(s)&&Object.prototype.hasOwnProperty.call(s,e)),Ku=(t,e,s)=>n=>{if(typeof n!="string")return n;const[r,a,o,l]=n.match(Za);return{[t]:parseFloat(r),[e]:parseFloat(a),[s]:parseFloat(o),alpha:l!==void 0?parseFloat(l):1}},ay=t=>Mt(0,255,t),mi={...ln,transform:t=>Math.round(ay(t))},ms={test:Ka("rgb","red"),parse:Ku("red","green","blue"),transform:({red:t,green:e,blue:s,alpha:n=1})=>"rgba("+mi.transform(t)+", "+mi.transform(e)+", "+mi.transform(s)+", "+wn(Pn.transform(n))+")"};function oy(t){let e="",s="",n="",r="";return t.length>5?(e=t.substring(1,3),s=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),s=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,s+=s,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(s,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}}const Fi={test:Ka("#"),parse:oy,transform:ms.transform},Ds={test:Ka("hsl","hue"),parse:Ku("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:s,alpha:n=1})=>"hsla("+Math.round(t)+", "+yt.transform(wn(e))+", "+yt.transform(wn(s))+", "+wn(Pn.transform(n))+")"},Le={test:t=>ms.test(t)||Fi.test(t)||Ds.test(t),parse:t=>ms.test(t)?ms.parse(t):Ds.test(t)?Ds.parse(t):Fi.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?ms.transform(t):Ds.transform(t)},ly=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function cy(t){var e,s;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Za))===null||e===void 0?void 0:e.length)||0)+(((s=t.match(ly))===null||s===void 0?void 0:s.length)||0)>0}const qu="number",Gu="color",dy="var",uy="var(",qo="${}",hy=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function En(t){const e=t.toString(),s=[],n={color:[],number:[],var:[]},r=[];let a=0;const l=e.replace(hy,c=>(Le.test(c)?(n.color.push(a),r.push(Gu),s.push(Le.parse(c))):c.startsWith(uy)?(n.var.push(a),r.push(dy),s.push(c)):(n.number.push(a),r.push(qu),s.push(parseFloat(c))),++a,qo)).split(qo);return{values:s,split:l,indexes:n,types:r}}function Yu(t){return En(t).values}function Xu(t){const{split:e,types:s}=En(t),n=e.length;return r=>{let a="";for(let o=0;o<n;o++)if(a+=e[o],r[o]!==void 0){const l=s[o];l===qu?a+=wn(r[o]):l===Gu?a+=Le.transform(r[o]):a+=r[o]}return a}}const fy=t=>typeof t=="number"?0:t;function my(t){const e=Yu(t);return Xu(t)(e.map(fy))}const Xt={test:cy,parse:Yu,createTransformer:Xu,getAnimatableNone:my},py=new Set(["brightness","contrast","saturate","opacity"]);function gy(t){const[e,s]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[n]=s.match(Za)||[];if(!n)return t;const r=s.replace(n,"");let a=py.has(e)?1:0;return n!==s&&(a*=100),e+"("+a+r+")"}const xy=/\b([a-z-]*)\(.*?\)/gu,Bi={...Xt,getAnimatableNone:t=>{const e=t.match(xy);return e?e.map(gy).join(" "):t}},yy={...Ea,color:Le,backgroundColor:Le,outlineColor:Le,fill:Le,stroke:Le,borderColor:Le,borderTopColor:Le,borderRightColor:Le,borderBottomColor:Le,borderLeftColor:Le,filter:Bi,WebkitFilter:Bi},qa=t=>yy[t];function Qu(t,e){let s=qa(t);return s!==Bi&&(s=Xt),s.getAnimatableNone?s.getAnimatableNone(e):void 0}const vy=new Set(["auto","none","0"]);function by(t,e,s){let n=0,r;for(;n<t.length&&!r;){const a=t[n];typeof a=="string"&&!vy.has(a)&&En(a).values.length&&(r=t[n]),n++}if(r&&s)for(const a of e)t[a]=Qu(s,r)}const Go=t=>t===ln||t===H,Yo=(t,e)=>parseFloat(t.split(", ")[e]),Xo=(t,e)=>(s,{transform:n})=>{if(n==="none"||!n)return 0;const r=n.match(/^matrix3d\((.+)\)$/u);if(r)return Yo(r[1],e);{const a=n.match(/^matrix\((.+)\)$/u);return a?Yo(a[1],t):0}},wy=new Set(["x","y","z"]),jy=on.filter(t=>!wy.has(t));function Ny(t){const e=[];return jy.forEach(s=>{const n=t.getValue(s);n!==void 0&&(e.push([s,n.get()]),n.set(s.startsWith("scale")?1:0))}),e}const en={width:({x:t},{paddingLeft:e="0",paddingRight:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),height:({y:t},{paddingTop:e="0",paddingBottom:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Xo(4,13),y:Xo(5,14)};en.translateX=en.x;en.translateY=en.y;const js=new Set;let zi=!1,Ui=!1;function Ju(){if(Ui){const t=Array.from(js).filter(n=>n.needsMeasurement),e=new Set(t.map(n=>n.element)),s=new Map;e.forEach(n=>{const r=Ny(n);r.length&&(s.set(n,r),n.render())}),t.forEach(n=>n.measureInitialState()),e.forEach(n=>{n.render();const r=s.get(n);r&&r.forEach(([a,o])=>{var l;(l=n.getValue(a))===null||l===void 0||l.set(o)})}),t.forEach(n=>n.measureEndState()),t.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}Ui=!1,zi=!1,js.forEach(t=>t.complete()),js.clear()}function eh(){js.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ui=!0)})}function Sy(){eh(),Ju()}class Ga{constructor(e,s,n,r,a,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=s,this.name=n,this.motionValue=r,this.element=a,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(js.add(this),zi||(zi=!0,be.read(eh),be.resolveKeyframes(Ju))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:s,element:n,motionValue:r}=this;for(let a=0;a<e.length;a++)if(e[a]===null)if(a===0){const o=r==null?void 0:r.get(),l=e[e.length-1];if(o!==void 0)e[0]=o;else if(n&&s){const c=n.readValue(s,l);c!=null&&(e[0]=c)}e[0]===void 0&&(e[0]=l),r&&o===void 0&&r.set(e[0])}else e[a]=e[a-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),js.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,js.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const th=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Cy=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ky(t){const e=Cy.exec(t);if(!e)return[,];const[,s,n,r]=e;return[`--${s??n}`,r]}function sh(t,e,s=1){const[n,r]=ky(t);if(!n)return;const a=window.getComputedStyle(e).getPropertyValue(n);if(a){const o=a.trim();return th(o)?parseFloat(o):o}return _a(r)?sh(r,e,s+1):r}const nh=t=>e=>e.test(t),Ty={test:t=>t==="auto",parse:t=>t},rh=[ln,H,yt,Ot,g0,p0,Ty],Qo=t=>rh.find(nh(t));class ih extends Ga{constructor(e,s,n,r,a){super(e,s,n,r,a,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:s,name:n}=this;if(!s||!s.current)return;super.readKeyframes();for(let c=0;c<e.length;c++){let d=e[c];if(typeof d=="string"&&(d=d.trim(),_a(d))){const u=sh(d,s.current);u!==void 0&&(e[c]=u),c===e.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!Vu.has(n)||e.length!==2)return;const[r,a]=e,o=Qo(r),l=Qo(a);if(o!==l)if(Go(o)&&Go(l))for(let c=0;c<e.length;c++){const d=e[c];typeof d=="string"&&(e[c]=parseFloat(d))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:s}=this,n=[];for(let r=0;r<e.length;r++)ny(e[r])&&n.push(r);n.length&&by(e,n,s)}measureInitialState(){const{element:e,unresolvedKeyframes:s,name:n}=this;if(!e||!e.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=en[n](e.measureViewportBox(),window.getComputedStyle(e.current)),s[0]=this.measuredOrigin;const r=s[s.length-1];r!==void 0&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;const{element:s,name:n,unresolvedKeyframes:r}=this;if(!s||!s.current)return;const a=s.getValue(n);a&&a.jump(this.measuredOrigin,!1);const o=r.length-1,l=r[o];r[o]=en[n](s.measureViewportBox(),window.getComputedStyle(s.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([c,d])=>{s.getValue(c).set(d)}),this.resolveNoneKeyframes()}}const Jo=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Xt.test(t)||t==="0")&&!t.startsWith("url("));function Ay(t){const e=t[0];if(t.length===1)return!0;for(let s=0;s<t.length;s++)if(t[s]!==e)return!0}function Py(t,e,s,n){const r=t[0];if(r===null)return!1;if(e==="display"||e==="visibility")return!0;const a=t[t.length-1],o=Jo(r,e),l=Jo(a,e);return!o||!l?!1:Ay(t)||(s==="spring"||Oa(s))&&n}const _y=t=>t!==null;function qr(t,{repeat:e,repeatType:s="loop"},n){const r=t.filter(_y),a=e&&s!=="loop"&&e%2===1?0:r.length-1;return!a||n===void 0?r[a]:n}const Ey=40;class ah{constructor({autoplay:e=!0,delay:s=0,type:n="keyframes",repeat:r=0,repeatDelay:a=0,repeatType:o="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=vt.now(),this.options={autoplay:e,delay:s,type:n,repeat:r,repeatDelay:a,repeatType:o,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Ey?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Sy(),this._resolved}onKeyframesResolved(e,s){this.resolvedAt=vt.now(),this.hasAttemptedResolve=!0;const{name:n,type:r,velocity:a,delay:o,onComplete:l,onUpdate:c,isGenerator:d}=this.options;if(!d&&!Py(e,n,r,a))if(o)this.options.duration=0;else{c&&c(qr(e,this.options,s)),l&&l(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,s);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:s,...u},this.onPostResolved())}onPostResolved(){}then(e,s){return this.currentFinishedPromise.then(e,s)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const je=(t,e,s)=>t+(e-t)*s;function pi(t,e,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+(e-t)*6*s:s<1/2?e:s<2/3?t+(e-t)*(2/3-s)*6:t}function Ry({hue:t,saturation:e,lightness:s,alpha:n}){t/=360,e/=100,s/=100;let r=0,a=0,o=0;if(!e)r=a=o=s;else{const l=s<.5?s*(1+e):s+e-s*e,c=2*s-l;r=pi(c,l,t+1/3),a=pi(c,l,t),o=pi(c,l,t-1/3)}return{red:Math.round(r*255),green:Math.round(a*255),blue:Math.round(o*255),alpha:n}}function xr(t,e){return s=>s>0?e:t}const gi=(t,e,s)=>{const n=t*t,r=s*(e*e-n)+n;return r<0?0:Math.sqrt(r)},My=[Fi,ms,Ds],Iy=t=>My.find(e=>e.test(t));function el(t){const e=Iy(t);if(!e)return!1;let s=e.parse(t);return e===Ds&&(s=Ry(s)),s}const tl=(t,e)=>{const s=el(t),n=el(e);if(!s||!n)return xr(t,e);const r={...s};return a=>(r.red=gi(s.red,n.red,a),r.green=gi(s.green,n.green,a),r.blue=gi(s.blue,n.blue,a),r.alpha=je(s.alpha,n.alpha,a),ms.transform(r))},Vy=(t,e)=>s=>e(t(s)),Hn=(...t)=>t.reduce(Vy),Wi=new Set(["none","hidden"]);function Dy(t,e){return Wi.has(t)?s=>s<=0?t:e:s=>s>=1?e:t}function Ly(t,e){return s=>je(t,e,s)}function Ya(t){return typeof t=="number"?Ly:typeof t=="string"?_a(t)?xr:Le.test(t)?tl:By:Array.isArray(t)?oh:typeof t=="object"?Le.test(t)?tl:Oy:xr}function oh(t,e){const s=[...t],n=s.length,r=t.map((a,o)=>Ya(a)(a,e[o]));return a=>{for(let o=0;o<n;o++)s[o]=r[o](a);return s}}function Oy(t,e){const s={...t,...e},n={};for(const r in s)t[r]!==void 0&&e[r]!==void 0&&(n[r]=Ya(t[r])(t[r],e[r]));return r=>{for(const a in n)s[a]=n[a](r);return s}}function Fy(t,e){var s;const n=[],r={color:0,var:0,number:0};for(let a=0;a<e.values.length;a++){const o=e.types[a],l=t.indexes[o][r[o]],c=(s=t.values[l])!==null&&s!==void 0?s:0;n[a]=c,r[o]++}return n}const By=(t,e)=>{const s=Xt.createTransformer(e),n=En(t),r=En(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?Wi.has(t)&&!r.values.length||Wi.has(e)&&!n.values.length?Dy(t,e):Hn(oh(Fy(n,r),r.values),s):xr(t,e)};function lh(t,e,s){return typeof t=="number"&&typeof e=="number"&&typeof s=="number"?je(t,e,s):Ya(t)(t,e)}const zy=5;function ch(t,e,s){const n=Math.max(e-zy,0);return Du(s-t(n),e-n)}const Se={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},xi=.001;function Uy({duration:t=Se.duration,bounce:e=Se.bounce,velocity:s=Se.velocity,mass:n=Se.mass}){let r,a,o=1-e;o=Mt(Se.minDamping,Se.maxDamping,o),t=Mt(Se.minDuration,Se.maxDuration,Et(t)),o<1?(r=d=>{const u=d*o,h=u*t,f=u-s,x=$i(d,o),b=Math.exp(-h);return xi-f/x*b},a=d=>{const h=d*o*t,f=h*s+s,x=Math.pow(o,2)*Math.pow(d,2)*t,b=Math.exp(-h),p=$i(Math.pow(d,2),o);return(-r(d)+xi>0?-1:1)*((f-x)*b)/p}):(r=d=>{const u=Math.exp(-d*t),h=(d-s)*t+1;return-xi+u*h},a=d=>{const u=Math.exp(-d*t),h=(s-d)*(t*t);return u*h});const l=5/t,c=$y(r,a,l);if(t=_t(t),isNaN(c))return{stiffness:Se.stiffness,damping:Se.damping,duration:t};{const d=Math.pow(c,2)*n;return{stiffness:d,damping:o*2*Math.sqrt(n*d),duration:t}}}const Wy=12;function $y(t,e,s){let n=s;for(let r=1;r<Wy;r++)n=n-t(n)/e(n);return n}function $i(t,e){return t*Math.sqrt(1-e*e)}const Hy=["duration","bounce"],Zy=["stiffness","damping","mass"];function sl(t,e){return e.some(s=>t[s]!==void 0)}function Ky(t){let e={velocity:Se.velocity,stiffness:Se.stiffness,damping:Se.damping,mass:Se.mass,isResolvedFromDuration:!1,...t};if(!sl(t,Zy)&&sl(t,Hy))if(t.visualDuration){const s=t.visualDuration,n=2*Math.PI/(s*1.2),r=n*n,a=2*Mt(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:Se.mass,stiffness:r,damping:a}}else{const s=Uy(t);e={...e,...s,mass:Se.mass},e.isResolvedFromDuration=!0}return e}function dh(t=Se.visualDuration,e=Se.bounce){const s=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:n,restDelta:r}=s;const a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:c,damping:d,mass:u,duration:h,velocity:f,isResolvedFromDuration:x}=Ky({...s,velocity:-Et(s.velocity||0)}),b=f||0,p=d/(2*Math.sqrt(c*u)),g=o-a,j=Et(Math.sqrt(c/u)),v=Math.abs(g)<5;n||(n=v?Se.restSpeed.granular:Se.restSpeed.default),r||(r=v?Se.restDelta.granular:Se.restDelta.default);let w;if(p<1){const C=$i(j,p);w=M=>{const O=Math.exp(-p*j*M);return o-O*((b+p*j*g)/C*Math.sin(C*M)+g*Math.cos(C*M))}}else if(p===1)w=C=>o-Math.exp(-j*C)*(g+(b+j*g)*C);else{const C=j*Math.sqrt(p*p-1);w=M=>{const O=Math.exp(-p*j*M),I=Math.min(C*M,300);return o-O*((b+p*j*g)*Math.sinh(I)+C*g*Math.cosh(I))/C}}const T={calculatedDuration:x&&h||null,next:C=>{const M=w(C);if(x)l.done=C>=h;else{let O=0;p<1&&(O=C===0?_t(b):ch(w,C,M));const I=Math.abs(O)<=n,_=Math.abs(o-M)<=r;l.done=I&&_}return l.value=l.done?o:M,l},toString:()=>{const C=Math.min(Au(T),Di),M=Pu(O=>T.next(C*O).value,C,30);return C+"ms "+M}};return T}function nl({keyframes:t,velocity:e=0,power:s=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:a=500,modifyTarget:o,min:l,max:c,restDelta:d=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},x=I=>l!==void 0&&I<l||c!==void 0&&I>c,b=I=>l===void 0?c:c===void 0||Math.abs(l-I)<Math.abs(c-I)?l:c;let p=s*e;const g=h+p,j=o===void 0?g:o(g);j!==g&&(p=j-h);const v=I=>-p*Math.exp(-I/n),w=I=>j+v(I),T=I=>{const _=v(I),$=w(I);f.done=Math.abs(_)<=d,f.value=f.done?j:$};let C,M;const O=I=>{x(f.value)&&(C=I,M=dh({keyframes:[f.value,b(f.value)],velocity:ch(w,I,f.value),damping:r,stiffness:a,restDelta:d,restSpeed:u}))};return O(0),{calculatedDuration:null,next:I=>{let _=!1;return!M&&C===void 0&&(_=!0,T(I),O(I)),C!==void 0&&I>=C?M.next(I-C):(!_&&T(I),f)}}}const qy=$n(.42,0,1,1),Gy=$n(0,0,.58,1),uh=$n(.42,0,.58,1),Yy=t=>Array.isArray(t)&&typeof t[0]!="number",Xy={linear:Ze,easeIn:qy,easeInOut:uh,easeOut:Gy,circIn:Ha,circInOut:Hu,circOut:$u,backIn:$a,backInOut:Uu,backOut:zu,anticipate:Wu},rl=t=>{if(Fa(t)){lu(t.length===4);const[e,s,n,r]=t;return $n(e,s,n,r)}else if(typeof t=="string")return Xy[t];return t};function Qy(t,e,s){const n=[],r=s||lh,a=t.length-1;for(let o=0;o<a;o++){let l=r(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]||Ze:e;l=Hn(c,l)}n.push(l)}return n}function Jy(t,e,{clamp:s=!0,ease:n,mixer:r}={}){const a=t.length;if(lu(a===e.length),a===1)return()=>e[0];if(a===2&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());const l=Qy(e,n,r),c=l.length,d=u=>{if(o&&u<t[0])return e[0];let h=0;if(c>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const f=Qs(t[h],t[h+1],u);return l[h](f)};return s?u=>d(Mt(t[0],t[a-1],u)):d}function ev(t,e){const s=t[t.length-1];for(let n=1;n<=e;n++){const r=Qs(0,e,n);t.push(je(s,1,r))}}function tv(t){const e=[0];return ev(e,t.length-1),e}function sv(t,e){return t.map(s=>s*e)}function nv(t,e){return t.map(()=>e||uh).splice(0,t.length-1)}function yr({duration:t=300,keyframes:e,times:s,ease:n="easeInOut"}){const r=Yy(n)?n.map(rl):rl(n),a={done:!1,value:e[0]},o=sv(s&&s.length===e.length?s:tv(e),t),l=Jy(o,e,{ease:Array.isArray(r)?r:nv(e,r)});return{calculatedDuration:t,next:c=>(a.value=l(c),a.done=c>=t,a)}}const rv=t=>{const e=({timestamp:s})=>t(s);return{start:()=>be.update(e,!0),stop:()=>Yt(e),now:()=>Ie.isProcessing?Ie.timestamp:vt.now()}},iv={decay:nl,inertia:nl,tween:yr,keyframes:yr,spring:dh},av=t=>t/100;class Xa extends ah{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:c}=this.options;c&&c()};const{name:s,motionValue:n,element:r,keyframes:a}=this.options,o=(r==null?void 0:r.KeyframeResolver)||Ga,l=(c,d)=>this.onKeyframesResolved(c,d);this.resolver=new o(a,l,s,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:a,velocity:o=0}=this.options,l=Oa(s)?s:iv[s]||yr;let c,d;l!==yr&&typeof e[0]!="number"&&(c=Hn(av,lh(e[0],e[1])),e=[0,100]);const u=l({...this.options,keyframes:e});a==="mirror"&&(d=l({...this.options,keyframes:[...e].reverse(),velocity:-o})),u.calculatedDuration===null&&(u.calculatedDuration=Au(u));const{calculatedDuration:h}=u,f=h+r,x=f*(n+1)-r;return{generator:u,mirroredGenerator:d,mapPercentToKeyframes:c,calculatedDuration:h,resolvedDuration:f,totalDuration:x}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,s=!1){const{resolved:n}=this;if(!n){const{keyframes:I}=this.options;return{done:!0,value:I[I.length-1]}}const{finalKeyframe:r,generator:a,mirroredGenerator:o,mapPercentToKeyframes:l,keyframes:c,calculatedDuration:d,totalDuration:u,resolvedDuration:h}=n;if(this.startTime===null)return a.next(0);const{delay:f,repeat:x,repeatType:b,repeatDelay:p,onUpdate:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),s?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const j=this.currentTime-f*(this.speed>=0?1:-1),v=this.speed>=0?j<0:j>u;this.currentTime=Math.max(j,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let w=this.currentTime,T=a;if(x){const I=Math.min(this.currentTime,u)/h;let _=Math.floor(I),$=I%1;!$&&I>=1&&($=1),$===1&&_--,_=Math.min(_,x+1),!!(_%2)&&(b==="reverse"?($=1-$,p&&($-=p/h)):b==="mirror"&&(T=o)),w=Mt(0,1,$)*h}const C=v?{done:!1,value:c[0]}:T.next(w);l&&(C.value=l(C.value));let{done:M}=C;!v&&d!==null&&(M=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const O=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&M);return O&&r!==void 0&&(C.value=qr(c,this.options,r)),g&&g(C.value),O&&this.finish(),C}get duration(){const{resolved:e}=this;return e?Et(e.calculatedDuration):0}get time(){return Et(this.currentTime)}set time(e){e=_t(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const s=this.playbackSpeed!==e;this.playbackSpeed=e,s&&(this.time=Et(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=rv,onPlay:s,startTime:n}=this.options;this.driver||(this.driver=e(a=>this.tick(a))),s&&s();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=r):this.startTime=n??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const ov=new Set(["opacity","clipPath","filter","transform"]);function lv(t,e,s,{delay:n=0,duration:r=300,repeat:a=0,repeatType:o="loop",ease:l="easeInOut",times:c}={}){const d={[e]:s};c&&(d.offset=c);const u=Eu(l,r);return Array.isArray(u)&&(d.easing=u),t.animate(d,{delay:n,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:a+1,direction:o==="reverse"?"alternate":"normal"})}const cv=Na(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),vr=10,dv=2e4;function uv(t){return Oa(t.type)||t.type==="spring"||!_u(t.ease)}function hv(t,e){const s=new Xa({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let n={done:!1,value:t[0]};const r=[];let a=0;for(;!n.done&&a<dv;)n=s.sample(a),r.push(n.value),a+=vr;return{times:void 0,keyframes:r,duration:a-vr,ease:"linear"}}const hh={anticipate:Wu,backInOut:Uu,circInOut:Hu};function fv(t){return t in hh}class il extends ah{constructor(e){super(e);const{name:s,motionValue:n,element:r,keyframes:a}=this.options;this.resolver=new ih(a,(o,l)=>this.onKeyframesResolved(o,l),s,n,r),this.resolver.scheduleResolve()}initPlayback(e,s){let{duration:n=300,times:r,ease:a,type:o,motionValue:l,name:c,startTime:d}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof a=="string"&&gr()&&fv(a)&&(a=hh[a]),uv(this.options)){const{onComplete:h,onUpdate:f,motionValue:x,element:b,...p}=this.options,g=hv(e,p);e=g.keyframes,e.length===1&&(e[1]=e[0]),n=g.duration,r=g.times,a=g.ease,o="keyframes"}const u=lv(l.owner.current,c,e,{...this.options,duration:n,times:r,ease:a});return u.startTime=d??this.calcStartTime(),this.pendingTimeline?(Wo(u,this.pendingTimeline),this.pendingTimeline=void 0):u.onfinish=()=>{const{onComplete:h}=this.options;l.set(qr(e,this.options,s)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:u,duration:n,times:r,type:o,ease:a,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:s}=e;return Et(s)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:s}=e;return Et(s.currentTime||0)}set time(e){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.currentTime=_t(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:s}=e;return s.playbackRate}set speed(e){const{resolved:s}=this;if(!s)return;const{animation:n}=s;n.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:s}=e;return s.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:s}=e;return s.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:s}=this;if(!s)return Ze;const{animation:n}=s;Wo(n,e)}return Ze}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:s}=e;s.playState==="finished"&&this.updateFinishedPromise(),s.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:s}=e;s.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:s,keyframes:n,duration:r,type:a,ease:o,times:l}=e;if(s.playState==="idle"||s.playState==="finished")return;if(this.time){const{motionValue:d,onUpdate:u,onComplete:h,element:f,...x}=this.options,b=new Xa({...x,keyframes:n,duration:r,type:a,ease:o,times:l,isGenerator:!0}),p=_t(this.time);d.setWithVelocity(b.sample(p-vr).value,b.sample(p).value,vr)}const{onStop:c}=this.options;c&&c(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:s,name:n,repeatDelay:r,repeatType:a,damping:o,type:l}=e;if(!s||!s.owner||!(s.owner.current instanceof HTMLElement))return!1;const{onUpdate:c,transformTemplate:d}=s.owner.getProps();return cv()&&n&&ov.has(n)&&!c&&!d&&!r&&a!=="mirror"&&o!==0&&l!=="inertia"}}const mv={type:"spring",stiffness:500,damping:25,restSpeed:10},pv=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),gv={type:"keyframes",duration:.8},xv={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},yv=(t,{keyframes:e})=>e.length>2?gv:As.has(t)?t.startsWith("scale")?pv(e[1]):mv:xv;function vv({when:t,delay:e,delayChildren:s,staggerChildren:n,staggerDirection:r,repeat:a,repeatType:o,repeatDelay:l,from:c,elapsed:d,...u}){return!!Object.keys(u).length}const Qa=(t,e,s,n={},r,a)=>o=>{const l=La(n,t)||{},c=l.delay||n.delay||0;let{elapsed:d=0}=n;d=d-_t(c);let u={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-d,onUpdate:f=>{e.set(f),l.onUpdate&&l.onUpdate(f)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:a?void 0:r};vv(l)||(u={...u,...yv(t,u)}),u.duration&&(u.duration=_t(u.duration)),u.repeatDelay&&(u.repeatDelay=_t(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),h&&!a&&e.get()!==void 0){const f=qr(u.keyframes,l);if(f!==void 0)return be.update(()=>{u.onUpdate(f),u.onComplete()}),new O0([])}return!a&&il.supports(u)?new il(u):new Xa(u)};function bv({protectedKeys:t,needsAnimating:e},s){const n=t.hasOwnProperty(s)&&e[s]!==!0;return e[s]=!1,n}function fh(t,e,{delay:s=0,transitionOverride:n,type:r}={}){var a;let{transition:o=t.getDefaultTransition(),transitionEnd:l,...c}=e;n&&(o=n);const d=[],u=r&&t.animationState&&t.animationState.getState()[r];for(const h in c){const f=t.getValue(h,(a=t.latestValues[h])!==null&&a!==void 0?a:null),x=c[h];if(x===void 0||u&&bv(u,h))continue;const b={delay:s,...La(o||{},h)};let p=!1;if(window.MotionHandoffAnimation){const j=Lu(t);if(j){const v=window.MotionHandoffAnimation(j,h,be);v!==null&&(b.startTime=v,p=!0)}}Oi(t,h),f.start(Qa(h,f,x,t.shouldReduceMotion&&Vu.has(h)?{type:!1}:b,t,p));const g=f.animation;g&&d.push(g)}return l&&Promise.all(d).then(()=>{be.update(()=>{l&&Q0(t,l)})}),d}function Hi(t,e,s={}){var n;const r=Kr(t,e,s.type==="exit"?(n=t.presenceContext)===null||n===void 0?void 0:n.custom:void 0);let{transition:a=t.getDefaultTransition()||{}}=r||{};s.transitionOverride&&(a=s.transitionOverride);const o=r?()=>Promise.all(fh(t,r,s)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?(d=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=a;return wv(t,e,u+d,h,f,s)}:()=>Promise.resolve(),{when:c}=a;if(c){const[d,u]=c==="beforeChildren"?[o,l]:[l,o];return d().then(()=>u())}else return Promise.all([o(),l(s.delay)])}function wv(t,e,s=0,n=0,r=1,a){const o=[],l=(t.variantChildren.size-1)*n,c=r===1?(d=0)=>d*n:(d=0)=>l-d*n;return Array.from(t.variantChildren).sort(jv).forEach((d,u)=>{d.notify("AnimationStart",e),o.push(Hi(d,e,{...a,delay:s+c(u)}).then(()=>d.notify("AnimationComplete",e)))}),Promise.all(o)}function jv(t,e){return t.sortNodePosition(e)}function Nv(t,e,s={}){t.notify("AnimationStart",e);let n;if(Array.isArray(e)){const r=e.map(a=>Hi(t,a,s));n=Promise.all(r)}else if(typeof e=="string")n=Hi(t,e,s);else{const r=typeof e=="function"?Kr(t,e,s.custom):e;n=Promise.all(fh(t,r,s))}return n.then(()=>{t.notify("AnimationComplete",e)})}const Sv=Ca.length;function mh(t){if(!t)return;if(!t.isControllingVariants){const s=t.parent?mh(t.parent)||{}:{};return t.props.initial!==void 0&&(s.initial=t.props.initial),s}const e={};for(let s=0;s<Sv;s++){const n=Ca[s],r=t.props[n];(An(r)||r===!1)&&(e[n]=r)}return e}const Cv=[...Sa].reverse(),kv=Sa.length;function Tv(t){return e=>Promise.all(e.map(({animation:s,options:n})=>Nv(t,s,n)))}function Av(t){let e=Tv(t),s=al(),n=!0;const r=c=>(d,u)=>{var h;const f=Kr(t,u,c==="exit"?(h=t.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:x,transitionEnd:b,...p}=f;d={...d,...p,...b}}return d};function a(c){e=c(t)}function o(c){const{props:d}=t,u=mh(t.parent)||{},h=[],f=new Set;let x={},b=1/0;for(let g=0;g<kv;g++){const j=Cv[g],v=s[j],w=d[j]!==void 0?d[j]:u[j],T=An(w),C=j===c?v.isActive:null;C===!1&&(b=g);let M=w===u[j]&&w!==d[j]&&T;if(M&&n&&t.manuallyAnimateOnMount&&(M=!1),v.protectedKeys={...x},!v.isActive&&C===null||!w&&!v.prevProp||Hr(w)||typeof w=="boolean")continue;const O=Pv(v.prevProp,w);let I=O||j===c&&v.isActive&&!M&&T||g>b&&T,_=!1;const $=Array.isArray(w)?w:[w];let q=$.reduce(r(j),{});C===!1&&(q={});const{prevResolvedValues:U={}}=v,G={...U,...q},V=Q=>{I=!0,f.has(Q)&&(_=!0,f.delete(Q)),v.needsAnimating[Q]=!0;const ce=t.getValue(Q);ce&&(ce.liveStyle=!1)};for(const Q in G){const ce=q[Q],we=U[Q];if(x.hasOwnProperty(Q))continue;let Ue=!1;Vi(ce)&&Vi(we)?Ue=!Tu(ce,we):Ue=ce!==we,Ue?ce!=null?V(Q):f.add(Q):ce!==void 0&&f.has(Q)?V(Q):v.protectedKeys[Q]=!0}v.prevProp=w,v.prevResolvedValues=q,v.isActive&&(x={...x,...q}),n&&t.blockInitialAnimation&&(I=!1),I&&(!(M&&O)||_)&&h.push(...$.map(Q=>({animation:Q,options:{type:j}})))}if(f.size){const g={};f.forEach(j=>{const v=t.getBaseTarget(j),w=t.getValue(j);w&&(w.liveStyle=!0),g[j]=v??null}),h.push({animation:g})}let p=!!h.length;return n&&(d.initial===!1||d.initial===d.animate)&&!t.manuallyAnimateOnMount&&(p=!1),n=!1,p?e(h):Promise.resolve()}function l(c,d){var u;if(s[c].isActive===d)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(f=>{var x;return(x=f.animationState)===null||x===void 0?void 0:x.setActive(c,d)}),s[c].isActive=d;const h=o(c);for(const f in s)s[f].protectedKeys={};return h}return{animateChanges:o,setActive:l,setAnimateFunction:a,getState:()=>s,reset:()=>{s=al(),n=!0}}}function Pv(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Tu(e,t):!1}function is(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function al(){return{animate:is(!0),whileInView:is(),whileHover:is(),whileTap:is(),whileDrag:is(),whileFocus:is(),exit:is()}}class es{constructor(e){this.isMounted=!1,this.node=e}update(){}}class _v extends es{constructor(e){super(e),e.animationState||(e.animationState=Av(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Hr(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:s}=this.node.prevProps||{};e!==s&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let Ev=0;class Rv extends es{constructor(){super(...arguments),this.id=Ev++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:s}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;const r=this.node.animationState.setActive("exit",!e);s&&!e&&r.then(()=>s(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const Mv={animation:{Feature:_v},exit:{Feature:Rv}};function Rn(t,e,s,n={passive:!0}){return t.addEventListener(e,s,n),()=>t.removeEventListener(e,s)}function Zn(t){return{point:{x:t.pageX,y:t.pageY}}}const Iv=t=>e=>Ba(e)&&t(e,Zn(e));function jn(t,e,s,n){return Rn(t,e,Iv(s),n)}const ol=(t,e)=>Math.abs(t-e);function Vv(t,e){const s=ol(t.x,e.x),n=ol(t.y,e.y);return Math.sqrt(s**2+n**2)}class ph{constructor(e,s,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:a=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=vi(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,x=Vv(h.offset,{x:0,y:0})>=3;if(!f&&!x)return;const{point:b}=h,{timestamp:p}=Ie;this.history.push({...b,timestamp:p});const{onStart:g,onMove:j}=this.handlers;f||(g&&g(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),j&&j(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=yi(f,this.transformPagePoint),be.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:x,onSessionEnd:b,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=vi(h.type==="pointercancel"?this.lastMoveEventInfo:yi(f,this.transformPagePoint),this.history);this.startEvent&&x&&x(h,g),b&&b(h,g)},!Ba(e))return;this.dragSnapToOrigin=a,this.handlers=s,this.transformPagePoint=n,this.contextWindow=r||window;const o=Zn(e),l=yi(o,this.transformPagePoint),{point:c}=l,{timestamp:d}=Ie;this.history=[{...c,timestamp:d}];const{onSessionStart:u}=s;u&&u(e,vi(l,this.history)),this.removeListeners=Hn(jn(this.contextWindow,"pointermove",this.handlePointerMove),jn(this.contextWindow,"pointerup",this.handlePointerUp),jn(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Yt(this.updatePoint)}}function yi(t,e){return e?{point:e(t.point)}:t}function ll(t,e){return{x:t.x-e.x,y:t.y-e.y}}function vi({point:t},e){return{point:t,delta:ll(t,gh(e)),offset:ll(t,Dv(e)),velocity:Lv(e,.1)}}function Dv(t){return t[0]}function gh(t){return t[t.length-1]}function Lv(t,e){if(t.length<2)return{x:0,y:0};let s=t.length-1,n=null;const r=gh(t);for(;s>=0&&(n=t[s],!(r.timestamp-n.timestamp>_t(e)));)s--;if(!n)return{x:0,y:0};const a=Et(r.timestamp-n.timestamp);if(a===0)return{x:0,y:0};const o={x:(r.x-n.x)/a,y:(r.y-n.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const xh=1e-4,Ov=1-xh,Fv=1+xh,yh=.01,Bv=0-yh,zv=0+yh;function Ke(t){return t.max-t.min}function Uv(t,e,s){return Math.abs(t-e)<=s}function cl(t,e,s,n=.5){t.origin=n,t.originPoint=je(e.min,e.max,t.origin),t.scale=Ke(s)/Ke(e),t.translate=je(s.min,s.max,t.origin)-t.originPoint,(t.scale>=Ov&&t.scale<=Fv||isNaN(t.scale))&&(t.scale=1),(t.translate>=Bv&&t.translate<=zv||isNaN(t.translate))&&(t.translate=0)}function Nn(t,e,s,n){cl(t.x,e.x,s.x,n?n.originX:void 0),cl(t.y,e.y,s.y,n?n.originY:void 0)}function dl(t,e,s){t.min=s.min+e.min,t.max=t.min+Ke(e)}function Wv(t,e,s){dl(t.x,e.x,s.x),dl(t.y,e.y,s.y)}function ul(t,e,s){t.min=e.min-s.min,t.max=t.min+Ke(e)}function Sn(t,e,s){ul(t.x,e.x,s.x),ul(t.y,e.y,s.y)}function $v(t,{min:e,max:s},n){return e!==void 0&&t<e?t=n?je(e,t,n.min):Math.max(t,e):s!==void 0&&t>s&&(t=n?je(s,t,n.max):Math.min(t,s)),t}function hl(t,e,s){return{min:e!==void 0?t.min+e:void 0,max:s!==void 0?t.max+s-(t.max-t.min):void 0}}function Hv(t,{top:e,left:s,bottom:n,right:r}){return{x:hl(t.x,s,r),y:hl(t.y,e,n)}}function fl(t,e){let s=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([s,n]=[n,s]),{min:s,max:n}}function Zv(t,e){return{x:fl(t.x,e.x),y:fl(t.y,e.y)}}function Kv(t,e){let s=.5;const n=Ke(t),r=Ke(e);return r>n?s=Qs(e.min,e.max-n,t.min):n>r&&(s=Qs(t.min,t.max-r,e.min)),Mt(0,1,s)}function qv(t,e){const s={};return e.min!==void 0&&(s.min=e.min-t.min),e.max!==void 0&&(s.max=e.max-t.min),s}const Zi=.35;function Gv(t=Zi){return t===!1?t=0:t===!0&&(t=Zi),{x:ml(t,"left","right"),y:ml(t,"top","bottom")}}function ml(t,e,s){return{min:pl(t,e),max:pl(t,s)}}function pl(t,e){return typeof t=="number"?t:t[e]||0}const gl=()=>({translate:0,scale:1,origin:0,originPoint:0}),Ls=()=>({x:gl(),y:gl()}),xl=()=>({min:0,max:0}),ke=()=>({x:xl(),y:xl()});function Xe(t){return[t("x"),t("y")]}function vh({top:t,left:e,right:s,bottom:n}){return{x:{min:e,max:s},y:{min:t,max:n}}}function Yv({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Xv(t,e){if(!e)return t;const s=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:s.y,left:s.x,bottom:n.y,right:n.x}}function bi(t){return t===void 0||t===1}function Ki({scale:t,scaleX:e,scaleY:s}){return!bi(t)||!bi(e)||!bi(s)}function as(t){return Ki(t)||bh(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function bh(t){return yl(t.x)||yl(t.y)}function yl(t){return t&&t!=="0%"}function br(t,e,s){const n=t-s,r=e*n;return s+r}function vl(t,e,s,n,r){return r!==void 0&&(t=br(t,r,n)),br(t,s,n)+e}function qi(t,e=0,s=1,n,r){t.min=vl(t.min,e,s,n,r),t.max=vl(t.max,e,s,n,r)}function wh(t,{x:e,y:s}){qi(t.x,e.translate,e.scale,e.originPoint),qi(t.y,s.translate,s.scale,s.originPoint)}const bl=.999999999999,wl=1.0000000000001;function Qv(t,e,s,n=!1){const r=s.length;if(!r)return;e.x=e.y=1;let a,o;for(let l=0;l<r;l++){a=s[l],o=a.projectionDelta;const{visualElement:c}=a.options;c&&c.props.style&&c.props.style.display==="contents"||(n&&a.options.layoutScroll&&a.scroll&&a!==a.root&&Fs(t,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,wh(t,o)),n&&as(a.latestValues)&&Fs(t,a.latestValues))}e.x<wl&&e.x>bl&&(e.x=1),e.y<wl&&e.y>bl&&(e.y=1)}function Os(t,e){t.min=t.min+e,t.max=t.max+e}function jl(t,e,s,n,r=.5){const a=je(t.min,t.max,r);qi(t,e,s,a,n)}function Fs(t,e){jl(t.x,e.x,e.scaleX,e.scale,e.originX),jl(t.y,e.y,e.scaleY,e.scale,e.originY)}function jh(t,e){return vh(Xv(t.getBoundingClientRect(),e))}function Jv(t,e,s){const n=jh(t,s),{scroll:r}=e;return r&&(Os(n.x,r.offset.x),Os(n.y,r.offset.y)),n}const Nh=({current:t})=>t?t.ownerDocument.defaultView:null,eb=new WeakMap;class tb{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ke(),this.visualElement=e}start(e,{snapToCursor:s=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;const r=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),s&&this.snapToCursor(Zn(u).point)},a=(u,h)=>{const{drag:f,dragPropagation:x,onDragStart:b}=this.getProps();if(f&&!x&&(this.openDragLock&&this.openDragLock(),this.openDragLock=K0(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Xe(g=>{let j=this.getAxisMotionValue(g).get()||0;if(yt.test(j)){const{projection:v}=this.visualElement;if(v&&v.layout){const w=v.layout.layoutBox[g];w&&(j=Ke(w)*(parseFloat(j)/100))}}this.originPoint[g]=j}),b&&be.postRender(()=>b(u,h)),Oi(this.visualElement,"transform");const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},o=(u,h)=>{const{dragPropagation:f,dragDirectionLock:x,onDirectionLock:b,onDrag:p}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=h;if(x&&this.currentDirection===null){this.currentDirection=sb(g),this.currentDirection!==null&&b&&b(this.currentDirection);return}this.updateAxis("x",h.point,g),this.updateAxis("y",h.point,g),this.visualElement.render(),p&&p(u,h)},l=(u,h)=>this.stop(u,h),c=()=>Xe(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:d}=this.getProps();this.panSession=new ph(e,{onSessionStart:r,onStart:a,onMove:o,onSessionEnd:l,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:d,contextWindow:Nh(this.visualElement)})}stop(e,s){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=s;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&be.postRender(()=>a(e,s))}cancel(){this.isDragging=!1;const{projection:e,animationState:s}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),s&&s.setActive("whileDrag",!1)}updateAxis(e,s,n){const{drag:r}=this.getProps();if(!n||!tr(e,r,this.currentDirection))return;const a=this.getAxisMotionValue(e);let o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=$v(o,this.constraints[e],this.elastic[e])),a.set(o)}resolveConstraints(){var e;const{dragConstraints:s,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,a=this.constraints;s&&Vs(s)?this.constraints||(this.constraints=this.resolveRefConstraints()):s&&r?this.constraints=Hv(r.layoutBox,s):this.constraints=!1,this.elastic=Gv(n),a!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Xe(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=qv(r.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:s}=this.getProps();if(!e||!Vs(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const a=Jv(n,r.root,this.visualElement.getTransformPagePoint());let o=Zv(r.layout.layoutBox,a);if(s){const l=s(Yv(o));this.hasMutatedConstraints=!!l,l&&(o=vh(l))}return o}startAnimation(e){const{drag:s,dragMomentum:n,dragElastic:r,dragTransition:a,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),c=this.constraints||{},d=Xe(u=>{if(!tr(u,s,this.currentDirection))return;let h=c&&c[u]||{};o&&(h={min:0,max:0});const f=r?200:1e6,x=r?40:1e7,b={type:"inertia",velocity:n?e[u]:0,bounceStiffness:f,bounceDamping:x,timeConstant:750,restDelta:1,restSpeed:10,...a,...h};return this.startAxisValueAnimation(u,b)});return Promise.all(d).then(l)}startAxisValueAnimation(e,s){const n=this.getAxisMotionValue(e);return Oi(this.visualElement,e),n.start(Qa(e,n,0,s,this.visualElement,!1))}stopAnimation(){Xe(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Xe(e=>{var s;return(s=this.getAxisMotionValue(e).animation)===null||s===void 0?void 0:s.pause()})}getAnimationState(e){var s;return(s=this.getAxisMotionValue(e).animation)===null||s===void 0?void 0:s.state}getAxisMotionValue(e){const s=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps(),r=n[s];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){Xe(s=>{const{drag:n}=this.getProps();if(!tr(s,n,this.currentDirection))return;const{projection:r}=this.visualElement,a=this.getAxisMotionValue(s);if(r&&r.layout){const{min:o,max:l}=r.layout.layoutBox[s];a.set(e[s]-je(o,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:s}=this.getProps(),{projection:n}=this.visualElement;if(!Vs(s)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};Xe(o=>{const l=this.getAxisMotionValue(o);if(l&&this.constraints!==!1){const c=l.get();r[o]=Kv({min:c,max:c},this.constraints[o])}});const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Xe(o=>{if(!tr(o,e,null))return;const l=this.getAxisMotionValue(o),{min:c,max:d}=this.constraints[o];l.set(je(c,d,r[o]))})}addListeners(){if(!this.visualElement.current)return;eb.set(this.visualElement,this);const e=this.visualElement.current,s=jn(e,"pointerdown",c=>{const{drag:d,dragListener:u=!0}=this.getProps();d&&u&&this.start(c)}),n=()=>{const{dragConstraints:c}=this.getProps();Vs(c)&&c.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,a=r.addEventListener("measure",n);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),be.read(n);const o=Rn(window,"resize",()=>this.scalePositionWithinConstraints()),l=r.addEventListener("didUpdate",({delta:c,hasLayoutChanged:d})=>{this.isDragging&&d&&(Xe(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=c[u].translate,h.set(h.get()+c[u].translate))}),this.visualElement.render())});return()=>{o(),s(),a(),l&&l()}}getProps(){const e=this.visualElement.getProps(),{drag:s=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:a=!1,dragElastic:o=Zi,dragMomentum:l=!0}=e;return{...e,drag:s,dragDirectionLock:n,dragPropagation:r,dragConstraints:a,dragElastic:o,dragMomentum:l}}}function tr(t,e,s){return(e===!0||e===t)&&(s===null||s===t)}function sb(t,e=10){let s=null;return Math.abs(t.y)>e?s="y":Math.abs(t.x)>e&&(s="x"),s}class nb extends es{constructor(e){super(e),this.removeGroupControls=Ze,this.removeListeners=Ze,this.controls=new tb(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ze}unmount(){this.removeGroupControls(),this.removeListeners()}}const Nl=t=>(e,s)=>{t&&be.postRender(()=>t(e,s))};class rb extends es{constructor(){super(...arguments),this.removePointerDownListener=Ze}onPointerDown(e){this.session=new ph(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Nh(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:s,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Nl(e),onStart:Nl(s),onMove:n,onEnd:(a,o)=>{delete this.session,r&&be.postRender(()=>r(a,o))}}}mount(){this.removePointerDownListener=jn(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const lr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Sl(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const hn={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(H.test(t))t=parseFloat(t);else return t;const s=Sl(t,e.target.x),n=Sl(t,e.target.y);return`${s}% ${n}%`}},ib={correct:(t,{treeScale:e,projectionDelta:s})=>{const n=t,r=Xt.parse(t);if(r.length>5)return n;const a=Xt.createTransformer(t),o=typeof r[0]!="number"?1:0,l=s.x.scale*e.x,c=s.y.scale*e.y;r[0+o]/=l,r[1+o]/=c;const d=je(l,c,.5);return typeof r[2+o]=="number"&&(r[2+o]/=d),typeof r[3+o]=="number"&&(r[3+o]/=d),a(r)}};class ab extends m.Component{componentDidMount(){const{visualElement:e,layoutGroup:s,switchLayoutGroup:n,layoutId:r}=this.props,{projection:a}=e;k0(ob),a&&(s.group&&s.group.add(a),n&&n.register&&r&&n.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",()=>{this.safeToRemove()}),a.setOptions({...a.options,onExitComplete:()=>this.safeToRemove()})),lr.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:s,visualElement:n,drag:r,isPresent:a}=this.props,o=n.projection;return o&&(o.isPresent=a,r||e.layoutDependency!==s||s===void 0?o.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?o.promote():o.relegate()||be.postRender(()=>{const l=o.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Ta.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:s,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),s&&s.group&&s.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Sh(t){const[e,s]=au(),n=m.useContext(va);return i.jsx(ab,{...t,layoutGroup:n,switchLayoutGroup:m.useContext(mu),isPresent:e,safeToRemove:s})}const ob={borderRadius:{...hn,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:hn,borderTopRightRadius:hn,borderBottomLeftRadius:hn,borderBottomRightRadius:hn,boxShadow:ib};function lb(t,e,s){const n=Oe(t)?t:_n(t);return n.start(Qa("",n,e,s)),n.animation}function cb(t){return t instanceof SVGElement&&t.tagName!=="svg"}const db=(t,e)=>t.depth-e.depth;class ub{constructor(){this.children=[],this.isDirty=!1}add(e){za(this.children,e),this.isDirty=!0}remove(e){Ua(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(db),this.isDirty=!1,this.children.forEach(e)}}function hb(t,e){const s=vt.now(),n=({timestamp:r})=>{const a=r-s;a>=e&&(Yt(n),t(a-e))};return be.read(n,!0),()=>Yt(n)}const Ch=["TopLeft","TopRight","BottomLeft","BottomRight"],fb=Ch.length,Cl=t=>typeof t=="string"?parseFloat(t):t,kl=t=>typeof t=="number"||H.test(t);function mb(t,e,s,n,r,a){r?(t.opacity=je(0,s.opacity!==void 0?s.opacity:1,pb(n)),t.opacityExit=je(e.opacity!==void 0?e.opacity:1,0,gb(n))):a&&(t.opacity=je(e.opacity!==void 0?e.opacity:1,s.opacity!==void 0?s.opacity:1,n));for(let o=0;o<fb;o++){const l=`border${Ch[o]}Radius`;let c=Tl(e,l),d=Tl(s,l);if(c===void 0&&d===void 0)continue;c||(c=0),d||(d=0),c===0||d===0||kl(c)===kl(d)?(t[l]=Math.max(je(Cl(c),Cl(d),n),0),(yt.test(d)||yt.test(c))&&(t[l]+="%")):t[l]=d}(e.rotate||s.rotate)&&(t.rotate=je(e.rotate||0,s.rotate||0,n))}function Tl(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const pb=kh(0,.5,$u),gb=kh(.5,.95,Ze);function kh(t,e,s){return n=>n<t?0:n>e?1:s(Qs(t,e,n))}function Al(t,e){t.min=e.min,t.max=e.max}function Ye(t,e){Al(t.x,e.x),Al(t.y,e.y)}function Pl(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function _l(t,e,s,n,r){return t-=e,t=br(t,1/s,n),r!==void 0&&(t=br(t,1/r,n)),t}function xb(t,e=0,s=1,n=.5,r,a=t,o=t){if(yt.test(e)&&(e=parseFloat(e),e=je(o.min,o.max,e/100)-o.min),typeof e!="number")return;let l=je(a.min,a.max,n);t===a&&(l-=e),t.min=_l(t.min,e,s,l,r),t.max=_l(t.max,e,s,l,r)}function El(t,e,[s,n,r],a,o){xb(t,e[s],e[n],e[r],e.scale,a,o)}const yb=["x","scaleX","originX"],vb=["y","scaleY","originY"];function Rl(t,e,s,n){El(t.x,e,yb,s?s.x:void 0,n?n.x:void 0),El(t.y,e,vb,s?s.y:void 0,n?n.y:void 0)}function Ml(t){return t.translate===0&&t.scale===1}function Th(t){return Ml(t.x)&&Ml(t.y)}function Il(t,e){return t.min===e.min&&t.max===e.max}function bb(t,e){return Il(t.x,e.x)&&Il(t.y,e.y)}function Vl(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Ah(t,e){return Vl(t.x,e.x)&&Vl(t.y,e.y)}function Dl(t){return Ke(t.x)/Ke(t.y)}function Ll(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class wb{constructor(){this.members=[]}add(e){za(this.members,e),e.scheduleRender()}remove(e){if(Ua(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const s=this.members[this.members.length-1];s&&this.promote(s)}}relegate(e){const s=this.members.findIndex(r=>e===r);if(s===0)return!1;let n;for(let r=s;r>=0;r--){const a=this.members[r];if(a.isPresent!==!1){n=a;break}}return n?(this.promote(n),!0):!1}promote(e,s){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,s&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;r===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:s,resumingFrom:n}=e;s.onExitComplete&&s.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function jb(t,e,s){let n="";const r=t.x.translate/e.x,a=t.y.translate/e.y,o=(s==null?void 0:s.z)||0;if((r||a||o)&&(n=`translate3d(${r}px, ${a}px, ${o}px) `),(e.x!==1||e.y!==1)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),s){const{transformPerspective:d,rotate:u,rotateX:h,rotateY:f,skewX:x,skewY:b}=s;d&&(n=`perspective(${d}px) ${n}`),u&&(n+=`rotate(${u}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),x&&(n+=`skewX(${x}deg) `),b&&(n+=`skewY(${b}deg) `)}const l=t.x.scale*e.x,c=t.y.scale*e.y;return(l!==1||c!==1)&&(n+=`scale(${l}, ${c})`),n||"none"}const os={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},xn=typeof window<"u"&&window.MotionDebug!==void 0,wi=["","X","Y","Z"],Nb={visibility:"hidden"},Ol=1e3;let Sb=0;function ji(t,e,s,n){const{latestValues:r}=e;r[t]&&(s[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function Ph(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const s=Lu(e);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:r,layoutId:a}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",be,!(r||a))}const{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&Ph(n)}function _h({attachResizeListener:t,defaultParent:e,measureScroll:s,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(o={},l=e==null?void 0:e()){this.id=Sb++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,xn&&(os.totalNodes=os.resolvedTargetDeltas=os.recalculatedProjection=0),this.nodes.forEach(Tb),this.nodes.forEach(Rb),this.nodes.forEach(Mb),this.nodes.forEach(Ab),xn&&window.MotionDebug.record(os)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new ub)}addEventListener(o,l){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new Wa),this.eventHandlers.get(o).add(l)}notifyListeners(o,...l){const c=this.eventHandlers.get(o);c&&c.notify(...l)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=cb(o),this.instance=o;const{layoutId:c,layout:d,visualElement:u}=this.options;if(u&&!u.current&&u.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(d||c)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(o,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=hb(f,250),lr.hasAnimatedSinceResize&&(lr.hasAnimatedSinceResize=!1,this.nodes.forEach(Bl))})}c&&this.root.registerSharedNode(c,this),this.options.animate!==!1&&u&&(c||d)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:x,layout:b})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const p=this.options.transition||u.getDefaultTransition()||Ob,{onLayoutAnimationStart:g,onLayoutAnimationComplete:j}=u.getProps(),v=!this.targetLayout||!Ah(this.targetLayout,b)||x,w=!f&&x;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||w||f&&(v||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,w);const T={...La(p,"layout"),onPlay:g,onComplete:j};(u.shouldReduceMotion||this.options.layoutRoot)&&(T.delay=0,T.type=!1),this.startAnimation(T)}else f||Bl(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=b})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Yt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Ib),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ph(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:l,layout:c}=this.options;if(l===void 0&&!c)return;const d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Fl);return}this.isUpdating||this.nodes.forEach(_b),this.isUpdating=!1,this.nodes.forEach(Eb),this.nodes.forEach(Cb),this.nodes.forEach(kb),this.clearAllSnapshots();const l=vt.now();Ie.delta=Mt(0,1e3/60,l-Ie.timestamp),Ie.timestamp=l,Ie.isProcessing=!0,hi.update.process(Ie),hi.preRender.process(Ie),hi.render.process(Ie),Ie.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ta.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Pb),this.sharedNodes.forEach(Vb)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,be.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){be.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ke(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(l=!1),l){const c=n(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:c,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:c}}}resetTransform(){if(!r)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!Th(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,u=d!==this.prevTransformTemplateValue;o&&(l||as(this.latestValues)||u)&&(r(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const l=this.measurePageBox();let c=this.removeElementScroll(l);return o&&(c=this.removeTransform(c)),Fb(c),{animationId:this.root.animationId,measuredBox:l,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:l}=this.options;if(!l)return ke();const c=l.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(Bb))){const{scroll:u}=this.root;u&&(Os(c.x,u.offset.x),Os(c.y,u.offset.y))}return c}removeElementScroll(o){var l;const c=ke();if(Ye(c,o),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return c;for(let d=0;d<this.path.length;d++){const u=this.path[d],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&Ye(c,o),Os(c.x,h.offset.x),Os(c.y,h.offset.y))}return c}applyTransform(o,l=!1){const c=ke();Ye(c,o);for(let d=0;d<this.path.length;d++){const u=this.path[d];!l&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Fs(c,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),as(u.latestValues)&&Fs(c,u.latestValues)}return as(this.latestValues)&&Fs(c,this.latestValues),c}removeTransform(o){const l=ke();Ye(l,o);for(let c=0;c<this.path.length;c++){const d=this.path[c];if(!d.instance||!as(d.latestValues))continue;Ki(d.latestValues)&&d.updateSnapshot();const u=ke(),h=d.measurePageBox();Ye(u,h),Rl(l,d.latestValues,d.snapshot?d.snapshot.layoutBox:void 0,u)}return as(this.latestValues)&&Rl(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ie.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var l;const c=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=c.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=c.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=c.isSharedProjectionDirty);const d=!!this.resumingFrom||this!==c;if(!(o||d&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=Ie.timestamp,!this.targetDelta&&!this.relativeTarget){const x=this.getClosestProjectingParent();x&&x.layout&&this.animationProgress!==1?(this.relativeParent=x,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ke(),this.relativeTargetOrigin=ke(),Sn(this.relativeTargetOrigin,this.layout.layoutBox,x.layout.layoutBox),Ye(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ke(),this.targetWithTransforms=ke()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Wv(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ye(this.target,this.layout.layoutBox),wh(this.target,this.targetDelta)):Ye(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const x=this.getClosestProjectingParent();x&&!!x.resumingFrom==!!this.resumingFrom&&!x.options.layoutScroll&&x.target&&this.animationProgress!==1?(this.relativeParent=x,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ke(),this.relativeTargetOrigin=ke(),Sn(this.relativeTargetOrigin,this.target,x.target),Ye(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}xn&&os.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ki(this.parent.latestValues)||bh(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const l=this.getLead(),c=!!this.resumingFrom||this!==l;let d=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(d=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(d=!1),this.resolvedRelativeTargetAt===Ie.timestamp&&(d=!1),d)return;const{layout:u,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||h))return;Ye(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,x=this.treeScale.y;Qv(this.layoutCorrected,this.treeScale,this.path,c),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=ke());const{target:b}=l;if(!b){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Pl(this.prevProjectionDelta.x,this.projectionDelta.x),Pl(this.prevProjectionDelta.y,this.projectionDelta.y)),Nn(this.projectionDelta,this.layoutCorrected,b,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==x||!Ll(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ll(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",b)),xn&&os.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),o){const c=this.getStack();c&&c.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Ls(),this.projectionDelta=Ls(),this.projectionDeltaWithTransform=Ls()}setAnimationOrigin(o,l=!1){const c=this.snapshot,d=c?c.latestValues:{},u={...this.latestValues},h=Ls();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const f=ke(),x=c?c.source:void 0,b=this.layout?this.layout.source:void 0,p=x!==b,g=this.getStack(),j=!g||g.members.length<=1,v=!!(p&&!j&&this.options.crossfade===!0&&!this.path.some(Lb));this.animationProgress=0;let w;this.mixTargetDelta=T=>{const C=T/1e3;zl(h.x,o.x,C),zl(h.y,o.y,C),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Sn(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Db(this.relativeTarget,this.relativeTargetOrigin,f,C),w&&bb(this.relativeTarget,w)&&(this.isProjectionDirty=!1),w||(w=ke()),Ye(w,this.relativeTarget)),p&&(this.animationValues=u,mb(u,d,this.latestValues,C,v,j)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Yt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=be.update(()=>{lr.hasAnimatedSinceResize=!0,this.currentAnimation=lb(0,Ol,{...o,onUpdate:l=>{this.mixTargetDelta(l),o.onUpdate&&o.onUpdate(l)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Ol),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:l,target:c,layout:d,latestValues:u}=o;if(!(!l||!c||!d)){if(this!==o&&this.layout&&d&&Eh(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||ke();const h=Ke(this.layout.layoutBox.x);c.x.min=o.target.x.min,c.x.max=c.x.min+h;const f=Ke(this.layout.layoutBox.y);c.y.min=o.target.y.min,c.y.max=c.y.min+f}Ye(l,c),Fs(l,u),Nn(this.projectionDeltaWithTransform,this.layoutCorrected,l,u)}}registerSharedNode(o,l){this.sharedNodes.has(o)||this.sharedNodes.set(o,new wb),this.sharedNodes.get(o).add(l);const d=l.options.initialPromotionConfig;l.promote({transition:d?d.transition:void 0,preserveFollowOpacity:d&&d.shouldPreserveFollowOpacity?d.shouldPreserveFollowOpacity(l):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:l}=this.options;return l?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:l}=this.options;return l?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:l,preserveFollowOpacity:c}={}){const d=this.getStack();d&&d.promote(this,c),o&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let l=!1;const{latestValues:c}=o;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(l=!0),!l)return;const d={};c.z&&ji("z",o,d,this.animationValues);for(let u=0;u<wi.length;u++)ji(`rotate${wi[u]}`,o,d,this.animationValues),ji(`skew${wi[u]}`,o,d,this.animationValues);o.render();for(const u in d)o.setStaticValue(u,d[u]),this.animationValues&&(this.animationValues[u]=d[u]);o.scheduleRender()}getProjectionStyles(o){var l,c;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Nb;const d={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,d.opacity="",d.pointerEvents=ar(o==null?void 0:o.pointerEvents)||"",d.transform=u?u(this.latestValues,""):"none",d;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const p={};return this.options.layoutId&&(p.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,p.pointerEvents=ar(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!as(this.latestValues)&&(p.transform=u?u({},""):"none",this.hasProjected=!1),p}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),d.transform=jb(this.projectionDeltaWithTransform,this.treeScale,f),u&&(d.transform=u(f,d.transform));const{x,y:b}=this.projectionDelta;d.transformOrigin=`${x.origin*100}% ${b.origin*100}% 0`,h.animationValues?d.opacity=h===this?(c=(l=f.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&c!==void 0?c:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:d.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const p in pr){if(f[p]===void 0)continue;const{correct:g,applyTo:j}=pr[p],v=d.transform==="none"?f[p]:g(f[p],h);if(j){const w=j.length;for(let T=0;T<w;T++)d[j[T]]=v}else d[p]=v}return this.options.layoutId&&(d.pointerEvents=h===this?ar(o==null?void 0:o.pointerEvents)||"":"none"),d}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var l;return(l=o.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Fl),this.root.sharedNodes.clear()}}}function Cb(t){t.updateLayout()}function kb(t){var e;const s=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&s&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=t.layout,{animationType:a}=t.options,o=s.source!==t.layout.source;a==="size"?Xe(h=>{const f=o?s.measuredBox[h]:s.layoutBox[h],x=Ke(f);f.min=n[h].min,f.max=f.min+x}):Eh(a,s.layoutBox,n)&&Xe(h=>{const f=o?s.measuredBox[h]:s.layoutBox[h],x=Ke(n[h]);f.max=f.min+x,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+x)});const l=Ls();Nn(l,n,s.layoutBox);const c=Ls();o?Nn(c,t.applyTransform(r,!0),s.measuredBox):Nn(c,n,s.layoutBox);const d=!Th(l);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:x}=h;if(f&&x){const b=ke();Sn(b,s.layoutBox,f.layoutBox);const p=ke();Sn(p,n,x.layoutBox),Ah(b,p)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=p,t.relativeTargetOrigin=b,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:s,delta:c,layoutDelta:l,hasLayoutChanged:d,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:n}=t.options;n&&n()}t.options.transition=void 0}function Tb(t){xn&&os.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Ab(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Pb(t){t.clearSnapshot()}function Fl(t){t.clearMeasurements()}function _b(t){t.isLayoutDirty=!1}function Eb(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Bl(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Rb(t){t.resolveTargetDelta()}function Mb(t){t.calcProjection()}function Ib(t){t.resetSkewAndRotation()}function Vb(t){t.removeLeadSnapshot()}function zl(t,e,s){t.translate=je(e.translate,0,s),t.scale=je(e.scale,1,s),t.origin=e.origin,t.originPoint=e.originPoint}function Ul(t,e,s,n){t.min=je(e.min,s.min,n),t.max=je(e.max,s.max,n)}function Db(t,e,s,n){Ul(t.x,e.x,s.x,n),Ul(t.y,e.y,s.y,n)}function Lb(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Ob={duration:.45,ease:[.4,0,.1,1]},Wl=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),$l=Wl("applewebkit/")&&!Wl("chrome/")?Math.round:Ze;function Hl(t){t.min=$l(t.min),t.max=$l(t.max)}function Fb(t){Hl(t.x),Hl(t.y)}function Eh(t,e,s){return t==="position"||t==="preserve-aspect"&&!Uv(Dl(e),Dl(s),.2)}function Bb(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const zb=_h({attachResizeListener:(t,e)=>Rn(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ni={current:void 0},Rh=_h({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Ni.current){const t=new zb({});t.mount(window),t.setOptions({layoutScroll:!0}),Ni.current=t}return Ni.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Ub={pan:{Feature:rb},drag:{Feature:nb,ProjectionNode:Rh,MeasureLayout:Sh}};function Zl(t,e,s){const{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover",s==="Start");const r="onHover"+s,a=n[r];a&&be.postRender(()=>a(e,Zn(e)))}class Wb extends es{mount(){const{current:e}=this.node;e&&(this.unmount=U0(e,s=>(Zl(this.node,s,"Start"),n=>Zl(this.node,n,"End"))))}unmount(){}}class $b extends es{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Hn(Rn(this.node.current,"focus",()=>this.onFocus()),Rn(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Kl(t,e,s){const{props:n}=t;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap",s==="Start");const r="onTap"+(s==="End"?"":s),a=n[r];a&&be.postRender(()=>a(e,Zn(e)))}class Hb extends es{mount(){const{current:e}=this.node;e&&(this.unmount=Z0(e,s=>(Kl(this.node,s,"Start"),(n,{success:r})=>Kl(this.node,n,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Gi=new WeakMap,Si=new WeakMap,Zb=t=>{const e=Gi.get(t.target);e&&e(t)},Kb=t=>{t.forEach(Zb)};function qb({root:t,...e}){const s=t||document;Si.has(s)||Si.set(s,{});const n=Si.get(s),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(Kb,{root:t,...e})),n[r]}function Gb(t,e,s){const n=qb(e);return Gi.set(t,s),n.observe(t),()=>{Gi.delete(t),n.unobserve(t)}}const Yb={some:0,all:1};class Xb extends es{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:s,margin:n,amount:r="some",once:a}=e,o={root:s?s.current:void 0,rootMargin:n,threshold:typeof r=="number"?r:Yb[r]},l=c=>{const{isIntersecting:d}=c;if(this.isInView===d||(this.isInView=d,a&&!d&&this.hasEnteredView))return;d&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",d);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=d?u:h;f&&f(c)};return Gb(this.node.current,o,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:s}=this.node;["amount","margin","root"].some(Qb(e,s))&&this.startObserver()}unmount(){}}function Qb({viewport:t={}},{viewport:e={}}={}){return s=>t[s]!==e[s]}const Jb={inView:{Feature:Xb},tap:{Feature:Hb},focus:{Feature:$b},hover:{Feature:Wb}},ew={layout:{ProjectionNode:Rh,MeasureLayout:Sh}},Yi={current:null},Mh={current:!1};function tw(){if(Mh.current=!0,!!ja)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Yi.current=t.matches;t.addListener(e),e()}else Yi.current=!1}const sw=[...rh,Le,Xt],nw=t=>sw.find(nh(t)),ql=new WeakMap;function rw(t,e,s){for(const n in e){const r=e[n],a=s[n];if(Oe(r))t.addValue(n,r);else if(Oe(a))t.addValue(n,_n(r,{owner:t}));else if(a!==r)if(t.hasValue(n)){const o=t.getValue(n);o.liveStyle===!0?o.jump(r):o.hasAnimated||o.set(r)}else{const o=t.getStaticValue(n);t.addValue(n,_n(o!==void 0?o:r,{owner:t}))}}for(const n in s)e[n]===void 0&&t.removeValue(n);return e}const Gl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class iw{scrapeMotionValuesFromProps(e,s,n){return{}}constructor({parent:e,props:s,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:a,visualState:o},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ga,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const x=vt.now();this.renderScheduledAt<x&&(this.renderScheduledAt=x,be.render(this.render,!1,!0))};const{latestValues:c,renderState:d,onUpdate:u}=o;this.onUpdate=u,this.latestValues=c,this.baseTarget={...c},this.initialValues=s.initial?{...c}:{},this.renderState=d,this.parent=e,this.props=s,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=l,this.blockInitialAnimation=!!a,this.isControllingVariants=Zr(s),this.isVariantNode=hu(s),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:h,...f}=this.scrapeMotionValuesFromProps(s,{},this);for(const x in f){const b=f[x];c[x]!==void 0&&Oe(b)&&b.set(c[x],!1)}}mount(e){this.current=e,ql.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,n)=>this.bindToMotionValue(n,s)),Mh.current||tw(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Yi.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){ql.delete(this.current),this.projection&&this.projection.unmount(),Yt(this.notifyUpdate),Yt(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const s=this.features[e];s&&(s.unmount(),s.isMounted=!1)}this.current=null}bindToMotionValue(e,s){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const n=As.has(e),r=s.on("change",l=>{this.latestValues[e]=l,this.props.onUpdate&&be.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=s.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,e,s)),this.valueSubscriptions.set(e,()=>{r(),a(),o&&o(),s.owner&&s.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in Js){const s=Js[e];if(!s)continue;const{isEnabled:n,Feature:r}=s;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){const a=this.features[e];a.isMounted?a.update():(a.mount(),a.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ke()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,s){this.latestValues[e]=s}update(e,s){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=s;for(let n=0;n<Gl.length;n++){const r=Gl[n];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const a="on"+r,o=e[a];o&&(this.propEventSubscriptions[r]=this.on(r,o))}this.prevMotionValues=rw(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const s=this.getClosestVariantNode();if(s)return s.variantChildren&&s.variantChildren.add(e),()=>s.variantChildren.delete(e)}addValue(e,s){const n=this.values.get(e);s!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,s),this.values.set(e,s),this.latestValues[e]=s.get())}removeValue(e){this.values.delete(e);const s=this.valueSubscriptions.get(e);s&&(s(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,s){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return n===void 0&&s!==void 0&&(n=_n(s===null?void 0:s,{owner:this}),this.addValue(e,n)),n}readValue(e,s){var n;let r=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(n=this.getBaseTargetFromProps(this.props,e))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,e,this.options);return r!=null&&(typeof r=="string"&&(th(r)||Zu(r))?r=parseFloat(r):!nw(r)&&Xt.test(s)&&(r=Qu(e,s)),this.setBaseTarget(e,Oe(r)?r.get():r)),Oe(r)?r.get():r}setBaseTarget(e,s){this.baseTarget[e]=s}getBaseTarget(e){var s;const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const o=Pa(this.props,n,(s=this.presenceContext)===null||s===void 0?void 0:s.custom);o&&(r=o[e])}if(n&&r!==void 0)return r;const a=this.getBaseTargetFromProps(this.props,e);return a!==void 0&&!Oe(a)?a:this.initialValues[e]!==void 0&&r===void 0?void 0:this.baseTarget[e]}on(e,s){return this.events[e]||(this.events[e]=new Wa),this.events[e].add(s)}notify(e,...s){this.events[e]&&this.events[e].notify(...s)}}class Ih extends iw{constructor(){super(...arguments),this.KeyframeResolver=ih}sortInstanceNodePosition(e,s){return e.compareDocumentPosition(s)&2?1:-1}getBaseTargetFromProps(e,s){return e.style?e.style[s]:void 0}removeValueFromRenderState(e,{vars:s,style:n}){delete s[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;Oe(e)&&(this.childSubscription=e.on("change",s=>{this.current&&(this.current.textContent=`${s}`)}))}}function aw(t){return window.getComputedStyle(t)}class ow extends Ih{constructor(){super(...arguments),this.type="html",this.renderInstance=wu}readValueFromInstance(e,s){if(As.has(s)){const n=qa(s);return n&&n.default||0}else{const n=aw(e),r=(yu(s)?n.getPropertyValue(s):n[s])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:s}){return jh(e,s)}build(e,s,n){Ra(e,s,n.transformTemplate)}scrapeMotionValuesFromProps(e,s,n){return Da(e,s,n)}}class lw extends Ih{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ke}getBaseTargetFromProps(e,s){return e[s]}readValueFromInstance(e,s){if(As.has(s)){const n=qa(s);return n&&n.default||0}return s=ju.has(s)?s:ka(s),e.getAttribute(s)}scrapeMotionValuesFromProps(e,s,n){return Cu(e,s,n)}build(e,s,n){Ma(e,s,this.isSVGTag,n.transformTemplate)}renderInstance(e,s,n,r){Nu(e,s,n,r)}mount(e){this.isSVGTag=Va(e.tagName),super.mount(e)}}const cw=(t,e)=>Aa(t)?new lw(e):new ow(e,{allowProjection:t!==m.Fragment}),dw=V0({...Mv,...Jb,...Ub,...ew},cw),z=Yx(dw);var Gr="Tabs",[uw,Wj]=On(Gr,[Zc]),Vh=Zc(),[hw,Ja]=uw(Gr),Dh=m.forwardRef((t,e)=>{const{__scopeTabs:s,value:n,onValueChange:r,defaultValue:a,orientation:o="horizontal",dir:l,activationMode:c="automatic",...d}=t,u=ca(l),[h,f]=ur({prop:n,onChange:r,defaultProp:a??"",caller:Gr});return i.jsx(hw,{scope:s,baseId:Ir(),value:h,onValueChange:f,orientation:o,dir:u,activationMode:c,children:i.jsx(ue.div,{dir:u,"data-orientation":o,...d,ref:e})})});Dh.displayName=Gr;var Lh="TabsList",Oh=m.forwardRef((t,e)=>{const{__scopeTabs:s,loop:n=!0,...r}=t,a=Ja(Lh,s),o=Vh(s);return i.jsx(Im,{asChild:!0,...o,orientation:a.orientation,dir:a.dir,loop:n,children:i.jsx(ue.div,{role:"tablist","aria-orientation":a.orientation,...r,ref:e})})});Oh.displayName=Lh;var Fh="TabsTrigger",Bh=m.forwardRef((t,e)=>{const{__scopeTabs:s,value:n,disabled:r=!1,...a}=t,o=Ja(Fh,s),l=Vh(s),c=Wh(o.baseId,n),d=$h(o.baseId,n),u=n===o.value;return i.jsx(Vm,{asChild:!0,...l,focusable:!r,active:u,children:i.jsx(ue.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:c,...a,ref:e,onMouseDown:J(t.onMouseDown,h=>{!r&&h.button===0&&h.ctrlKey===!1?o.onValueChange(n):h.preventDefault()}),onKeyDown:J(t.onKeyDown,h=>{[" ","Enter"].includes(h.key)&&o.onValueChange(n)}),onFocus:J(t.onFocus,()=>{const h=o.activationMode!=="manual";!u&&!r&&h&&o.onValueChange(n)})})})});Bh.displayName=Fh;var zh="TabsContent",Uh=m.forwardRef((t,e)=>{const{__scopeTabs:s,value:n,forceMount:r,children:a,...o}=t,l=Ja(zh,s),c=Wh(l.baseId,n),d=$h(l.baseId,n),u=n===l.value,h=m.useRef(u);return m.useEffect(()=>{const f=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(f)},[]),i.jsx(Ts,{present:r||u,children:({present:f})=>i.jsx(ue.div,{"data-state":u?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!f,id:d,tabIndex:0,...o,ref:e,style:{...t.style,animationDuration:h.current?"0s":void 0},children:f&&a})})});Uh.displayName=zh;function Wh(t,e){return`${t}-trigger-${e}`}function $h(t,e){return`${t}-content-${e}`}var fw=Dh,Hh=Oh,Zh=Bh,Kh=Uh;const qh=fw,eo=m.forwardRef(({className:t,...e},s)=>i.jsx(Hh,{ref:s,className:ee("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...e}));eo.displayName=Hh.displayName;const ot=m.forwardRef(({className:t,...e},s)=>i.jsx(Zh,{ref:s,className:ee("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...e}));ot.displayName=Zh.displayName;const ps=m.forwardRef(({className:t,...e},s)=>i.jsx(Kh,{ref:s,className:ee("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...e}));ps.displayName=Kh.displayName;const mw=()=>i.jsxs("section",{className:"relative overflow-hidden bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 pt-28 pb-20 md:pt-36 md:pb-28",children:[i.jsxs("div",{className:"absolute inset-0 z-0",children:[i.jsx("div",{className:"absolute top-0 -left-4 w-72 h-72 bg-purple-200 dark:bg-purple-900/20 rounded-full mix-blend-multiply dark:mix-blend-lighten blur-3xl opacity-30"}),i.jsx("div",{className:"absolute bottom-0 right-0 w-96 h-96 bg-blue-200 dark:bg-blue-900/20 rounded-full mix-blend-multiply dark:mix-blend-lighten blur-3xl opacity-30"})]}),i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:i.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[i.jsxs("div",{className:"inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-full mb-6",children:[i.jsx(fr,{className:"h-4 w-4 mr-2"}),i.jsx("span",{className:"text-sm font-semibold text-primary company-name",children:"Advanced Business Solutions"})]}),i.jsxs("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6",children:["Transforming ",i.jsx("span",{className:"gradient-heading",children:"Retail & Wholesale"})," With Technology"]}),i.jsxs("p",{className:"text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-xl",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," delivers cutting-edge technology solutions to help your retail or wholesale business innovate, streamline operations, and drive growth."]}),i.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 mb-12",children:[i.jsx(ae,{href:"/contact",children:i.jsxs(me,{size:"lg",className:"w-full sm:w-auto",children:["Get Started",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})}),i.jsx(ae,{href:"/services",children:i.jsx(me,{variant:"outline",size:"lg",className:"w-full sm:w-auto",children:"Explore Solutions"})})]}),i.jsxs("div",{className:"flex flex-col md:flex-row gap-6",children:[i.jsxs("div",{className:"flex items-start",children:[i.jsx(ir,{className:"h-5 w-5 text-primary mr-2 mt-0.5"}),i.jsx("span",{className:"text-slate-600 dark:text-slate-300",children:"AI-Powered Business Solutions"})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx(ir,{className:"h-5 w-5 text-primary mr-2 mt-0.5"}),i.jsx("span",{className:"text-slate-600 dark:text-slate-300",children:"Retail & Wholesale Expertise"})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx(ir,{className:"h-5 w-5 text-primary mr-2 mt-0.5"}),i.jsx("span",{className:"text-slate-600 dark:text-slate-300",children:"Proven ROI & Results"})]})]})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"relative",children:[i.jsxs("div",{className:"relative z-10 bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg border border-border",children:[i.jsx("div",{className:"aspect-video rounded-lg overflow-hidden mb-6",children:i.jsx("img",{src:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",alt:"Advanced retail technology solutions",className:"w-full h-full object-cover"})}),i.jsxs("div",{className:"grid gap-4 grid-cols-2",children:[i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-xl",children:[i.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-3",children:i.jsx("svg",{className:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),i.jsx("h3",{className:"text-lg font-bold mb-1",children:"35%"}),i.jsx("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Average Sales Increase"})]}),i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-xl",children:[i.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full mb-3",children:i.jsx("svg",{className:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})})}),i.jsx("h3",{className:"text-lg font-bold mb-1",children:"100%"}),i.jsx("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Client Satisfaction"})]}),i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-xl",children:[i.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full mb-3",children:i.jsx("svg",{className:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),i.jsx("h3",{className:"text-lg font-bold mb-1",children:"40%"}),i.jsx("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Efficiency Improvement"})]}),i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-xl",children:[i.jsx("div",{className:"flex items-center justify-center w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-full mb-3",children:i.jsx("svg",{className:"w-5 h-5 text-amber-600 dark:text-amber-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})})}),i.jsx("h3",{className:"text-lg font-bold mb-1",children:"28%"}),i.jsx("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:"Cost Reduction"})]})]})]}),i.jsx("div",{className:"absolute -top-6 -right-6 w-24 h-24 bg-blue-200/50 dark:bg-blue-800/20 rounded-full -z-10"}),i.jsx("div",{className:"absolute -bottom-8 -left-8 w-40 h-40 bg-primary/10 rounded-full -z-10"})]})]})})]}),pw=({icon:t,iconColor:e="text-primary",title:s,description:n,link:r,index:a=0,className:o})=>i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:a*.1},viewport:{once:!0,margin:"-50px"},className:ee("bg-white dark:bg-slate-800 rounded-xl p-6 border border-border h-full flex flex-col shadow-sm hover:shadow-md transition-shadow",o),children:[i.jsx("div",{className:ee("p-3 rounded-xl w-12 h-12 flex items-center justify-center mb-6",`bg-opacity-15 bg-${e.split("-")[1]}-100 dark:bg-${e.split("-")[1]}-900/20`),children:i.jsx(t,{className:ee("h-6 w-6",e)})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:s}),i.jsx("p",{className:"text-muted-foreground mb-6 flex-grow",children:n}),i.jsx(ae,{href:r,children:i.jsxs(me,{variant:"link",className:"p-0 h-auto font-medium mt-auto justify-start",children:["Learn More",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),gw=[{icon:xa,iconColor:"text-blue-500",title:"AI Solutions",description:"Leverage advanced artificial intelligence to optimize operations, enhance customer experiences, and drive business growth.",link:"/ai-solutions"},{icon:Ud,iconColor:"text-green-500",title:"Ecommerce Solutions",description:"Build powerful online stores and marketplaces tailored to your retail or wholesale business needs.",link:"/services#ecommerce"},{icon:Bd,iconColor:"text-purple-500",title:"Digital Marketing",description:"Attract, engage, and convert customers with strategic digital marketing campaigns and analytics.",link:"/services#digital-marketing"},{icon:Wd,iconColor:"text-orange-500",title:"Leads Generation",description:"Generate qualified leads and nurture prospects through automated, intelligent funnels.",link:"/services#leads-generation"},{icon:qg,iconColor:"text-red-500",title:"Business Automation",description:"Automate repetitive tasks and streamline operations to increase efficiency and reduce costs.",link:"/services#business-automation"},{icon:tx,iconColor:"text-indigo-500",title:"Business Process Management",description:"Optimize workflows and business processes to enhance productivity and operational excellence.",link:"/services#business-process"},{icon:Qg,iconColor:"text-cyan-500",title:"Order Fulfillment",description:"Streamline your supply chain with efficient order processing, inventory management, and fulfillment solutions.",link:"/services#order-fulfillment"},{icon:Wg,iconColor:"text-amber-500",title:"Digital Signage",description:"Engage customers with dynamic digital displays and interactive signage solutions for retail environments.",link:"/services#digital-signage"}],xw=[{title:"AI-Powered Analytics for Retail Chain",category:"Artificial Intelligence",image:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",link:"/case-studies/retail-ai-analytics"},{title:"B2B Ecommerce Platform for Wholesale",category:"Ecommerce",image:"https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",link:"/case-studies/ecommerce-wholesale-platform"},{title:"Digital Marketing Campaign Results",category:"Digital Marketing",image:"https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",link:"/case-studies/digital-marketing-campaign"}];function yw(){return i.jsxs("div",{children:[i.jsx(mw,{}),i.jsx("section",{className:"py-20 bg-white dark:bg-slate-900",children:i.jsxs("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[i.jsxs("div",{className:"text-center mb-16",children:[i.jsx(z.span,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5},viewport:{once:!0},className:"text-primary font-semibold tracking-wide uppercase",children:"Our Services"}),i.jsx(z.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"mt-2 text-3xl md:text-4xl font-bold",children:"Business Technology Solutions"}),i.jsxs(z.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},className:"mt-4 text-xl text-muted-foreground max-w-3xl mx-auto",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," delivers comprehensive technology services tailored specifically for retail and wholesale businesses."]})]}),i.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:gw.map((t,e)=>i.jsx(pw,{icon:t.icon,iconColor:t.iconColor,title:t.title,description:t.description,link:t.link,index:e},t.title))}),i.jsx("div",{className:"mt-12 text-center",children:i.jsx(ae,{href:"/services",children:i.jsxs(me,{size:"lg",children:["View All Services",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})})]})}),i.jsx("section",{className:"py-20 bg-slate-50 dark:bg-slate-800",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},children:[i.jsx("span",{className:"text-primary font-semibold tracking-wide uppercase",children:"Why Choose Us"}),i.jsx("h2",{className:"mt-2 text-3xl md:text-4xl font-bold",children:"Expertise in Retail & Wholesale Technology"}),i.jsxs("p",{className:"mt-4 text-lg text-muted-foreground",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," brings specialized knowledge and experience to help retail and wholesale businesses navigate the digital landscape with confidence."]}),i.jsxs("div",{className:"mt-8 space-y-6",children:[i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary",children:i.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("h3",{className:"text-xl font-bold",children:"Specialized Industry Focus"}),i.jsx("p",{className:"mt-2 text-muted-foreground",children:"We exclusively serve retail and wholesale businesses, allowing us to develop deep industry expertise and tailored solutions."})]})]}),i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary",children:i.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("h3",{className:"text-xl font-bold",children:"End-to-End Solutions"}),i.jsx("p",{className:"mt-2 text-muted-foreground",children:"From strategy to implementation and ongoing support, we provide comprehensive services to address all your technology needs."})]})]}),i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary",children:i.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("h3",{className:"text-xl font-bold",children:"Expert Team"}),i.jsx("p",{className:"mt-2 text-muted-foreground",children:"Our team combines technical expertise with business acumen to deliver solutions that drive real results for your company."})]})]}),i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"flex-shrink-0",children:i.jsx("div",{className:"flex items-center justify-center h-12 w-12 rounded-md bg-primary/10 text-primary",children:i.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})})}),i.jsxs("div",{className:"ml-4",children:[i.jsx("h3",{className:"text-xl font-bold",children:"Measurable Results"}),i.jsx("p",{className:"mt-2 text-muted-foreground",children:"We focus on delivering quantifiable business outcomes that help you track and achieve your strategic objectives."})]})]})]})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},className:"relative",children:[i.jsx("div",{className:"bg-white dark:bg-slate-900 rounded-xl shadow-lg p-8 border border-border relative z-10",children:i.jsxs(qh,{defaultValue:"retail",children:[i.jsxs(eo,{className:"grid w-full grid-cols-2 mb-8",children:[i.jsx(ot,{value:"retail",children:"Retail"}),i.jsx(ot,{value:"wholesale",children:"Wholesale"})]}),i.jsxs(ps,{value:"retail",children:[i.jsx("div",{className:"relative aspect-video overflow-hidden rounded-lg mb-6",children:i.jsx("img",{src:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"Retail technology solutions",className:"w-full h-full object-cover"})}),i.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Retail Solutions"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"Our retail technology solutions drive in-store and online sales, streamline operations, and deliver exceptional customer experiences."}),i.jsxs("ul",{className:"space-y-3 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"Omnichannel shopping experiences"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"AI-powered customer insights"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"In-store digital innovations"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"Smart inventory management"})]})]}),i.jsx(ae,{href:"/services",children:i.jsxs(me,{children:["Learn More ",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),i.jsxs(ps,{value:"wholesale",children:[i.jsx("div",{className:"relative aspect-video overflow-hidden rounded-lg mb-6",children:i.jsx("img",{src:"https://images.unsplash.com/photo-1553413077-190dd305871c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"Wholesale technology solutions",className:"w-full h-full object-cover"})}),i.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Wholesale Solutions"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"Our wholesale solutions optimize your distribution operations, enhance B2B relationships, and drive efficiency across your business."}),i.jsxs("ul",{className:"space-y-3 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"B2B ecommerce platforms"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"Supply chain optimization"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"Order management systems"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx(Dt,{className:"h-5 w-5 text-primary mt-0.5 mr-2"}),i.jsx("span",{children:"Automated fulfillment solutions"})]})]}),i.jsx(ae,{href:"/services",children:i.jsxs(me,{children:["Learn More ",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]})]})}),i.jsx("div",{className:"absolute -top-6 -right-6 w-36 h-36 bg-primary/10 rounded-full -z-10"}),i.jsx("div",{className:"absolute -bottom-8 -left-8 w-48 h-48 bg-accent/10 rounded-full -z-10"})]})]})})}),i.jsx("section",{className:"py-20 bg-white dark:bg-slate-900",children:i.jsxs("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[i.jsxs("div",{className:"text-center mb-16",children:[i.jsx(z.span,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5},viewport:{once:!0},className:"text-primary font-semibold tracking-wide uppercase",children:"Success Stories"}),i.jsx(z.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"mt-2 text-3xl md:text-4xl font-bold",children:"Client Case Studies"}),i.jsxs(z.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},className:"mt-4 text-xl text-muted-foreground max-w-3xl mx-auto",children:["See how ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," has helped retail and wholesale businesses achieve remarkable results."]})]}),i.jsx("div",{className:"grid md:grid-cols-3 gap-8",children:xw.map((t,e)=>i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:e*.1},viewport:{once:!0},className:"group relative overflow-hidden rounded-xl border border-border",children:[i.jsx("div",{className:"aspect-[4/3] overflow-hidden",children:i.jsx("img",{src:t.image,alt:t.title,className:"w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"})}),i.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent",children:i.jsxs("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:[i.jsx("span",{className:"text-sm font-semibold text-primary mb-2 block",children:t.category}),i.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:t.title})]})})]},t.title))})]})}),i.jsx("section",{className:"py-20 bg-gradient-to-r from-primary to-accent text-white",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"max-w-3xl mx-auto text-center",children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Transform Your Business?"}),i.jsxs("p",{className:"text-lg text-white/90 mb-8",children:["Contact ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," today to discuss how our technology solutions can help your retail or wholesale business thrive in the digital age."]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{size:"lg",variant:"secondary",children:["Schedule a Consultation",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]})})})]})}function vw(){return i.jsxs("div",{children:[i.jsx("section",{className:"bg-slate-50 dark:bg-slate-900 py-16 md:py-24",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"text-center max-w-3xl mx-auto",children:[i.jsx(z.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-4xl md:text-5xl font-bold mb-6",children:"Our Business Solutions"}),i.jsxs(z.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"text-lg text-muted-foreground mb-8",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," offers comprehensive technology services tailored specifically for retail and wholesale businesses looking to innovate and grow."]})]})})}),i.jsx("section",{className:"py-12",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs(qh,{defaultValue:"ai-solutions",className:"w-full",children:[i.jsx("div",{className:"flex justify-center mb-8",children:i.jsxs(eo,{className:"grid grid-cols-2 md:grid-cols-4 w-full max-w-4xl",children:[i.jsx(ot,{value:"ai-solutions",children:"AI Solutions"}),i.jsx(ot,{value:"ecommerce",children:"Ecommerce"}),i.jsx(ot,{value:"digital-marketing",children:"Digital Marketing"}),i.jsx(ot,{value:"leads-generation",children:"Leads Generation"}),i.jsx(ot,{value:"business-automation",children:"Business Automation"}),i.jsx(ot,{value:"business-process",children:"Process Management"}),i.jsx(ot,{value:"order-fulfillment",children:"Order Fulfillment"}),i.jsx(ot,{value:"digital-signage",children:"Digital Signage"})]})}),i.jsx(ps,{value:"ai-solutions",className:"mt-8",children:i.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[i.jsx(xa,{className:"h-12 w-12 text-blue-500 mb-6"}),i.jsx("h2",{className:"text-3xl font-bold mb-4",children:"AI Solutions"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"Our AI solutions harness the power of artificial intelligence to help retail and wholesale businesses make smarter decisions, automate complex processes, and gain a competitive edge."}),i.jsxs("div",{className:"space-y-4 mb-8",children:[i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-blue-600 dark:text-blue-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Predictive Analytics"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Forecast trends, anticipate customer behaviors, and make data-driven inventory decisions."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-blue-600 dark:text-blue-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Recommendation Engines"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Increase cross-selling and upselling with AI-powered product recommendations."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-blue-600 dark:text-blue-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"AI-Powered Chatbots"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Provide 24/7 customer support and streamline customer interactions at scale."})]})]})]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{children:["Get Started with AI",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border",children:[i.jsx("img",{src:"https://images.unsplash.com/photo-1626863905121-3b0c0ed7b61c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"AI technology visualization",className:"w-full rounded-lg object-cover",style:{aspectRatio:"4/3"}}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"text-xl font-bold mb-3",children:"How Our AI Solutions Help"}),i.jsxs("ul",{className:"space-y-2",children:[i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"35% average increase in conversion rates"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Reduce operational costs by up to 25%"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Improve inventory forecasting accuracy by 40%"})]})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-blue-100/50 dark:bg-blue-900/20 rounded-full -z-10"})]})]})}),i.jsx(ps,{value:"ecommerce",className:"mt-8",children:i.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[i.jsx(Ud,{className:"h-12 w-12 text-green-500 mb-6"}),i.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Ecommerce Solutions"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"We build powerful ecommerce platforms that help retail and wholesale businesses establish a strong online presence and increase sales."}),i.jsxs("div",{className:"space-y-4 mb-8",children:[i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-green-600 dark:text-green-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Custom Ecommerce Websites"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Tailored online stores that reflect your brand identity and business needs."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-green-600 dark:text-green-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Multi-channel Integration"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Sell across marketplaces, social media, and your own website with unified inventory management."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-green-600 dark:text-green-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"B2B Ecommerce Platforms"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Specialized solutions for wholesale businesses with custom pricing, bulk ordering, and account management."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-green-600 dark:text-green-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Payment Processing"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Secure payment gateways and flexible payment options for customers."})]})]})]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{children:["Start Selling Online",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border",children:[i.jsx("img",{src:"https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"Ecommerce platform",className:"w-full rounded-lg object-cover",style:{aspectRatio:"4/3"}}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Why Choose Our Ecommerce Solutions"}),i.jsxs("ul",{className:"space-y-2",children:[i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Mobile-optimized shopping experiences"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Seamless integration with existing systems"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Scalable infrastructure that grows with your business"})]})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-green-100/50 dark:bg-green-900/20 rounded-full -z-10"})]})]})}),i.jsx(ps,{value:"digital-marketing",className:"mt-8",children:i.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[i.jsx(Bd,{className:"h-12 w-12 text-purple-500 mb-6"}),i.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Digital Marketing"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"Our digital marketing strategies help retail and wholesale businesses reach their target audience, build brand awareness, and drive conversions across digital channels."}),i.jsxs("div",{className:"space-y-4 mb-8",children:[i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-purple-600 dark:text-purple-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Search Engine Optimization (SEO)"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Improve your search engine rankings and drive organic traffic to your website."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-purple-600 dark:text-purple-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Pay-Per-Click Advertising"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Target potential customers with strategic ad campaigns on search engines and social media."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-purple-600 dark:text-purple-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Social Media Marketing"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Build and engage your community across social media platforms with strategic content."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-purple-600 dark:text-purple-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Email Marketing"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Nurture customer relationships and drive repeat business with personalized email campaigns."})]})]})]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{children:["Boost Your Digital Presence",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border",children:[i.jsx("img",{src:"https://images.unsplash.com/photo-1611926653458-09294b3142bf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"Digital marketing strategy",className:"w-full rounded-lg object-cover",style:{aspectRatio:"4/3"}}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Our Marketing Approach"}),i.jsxs("ul",{className:"space-y-2",children:[i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Data-driven strategy development"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Comprehensive analytics and reporting"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Continuous optimization for maximum ROI"})]})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-purple-100/50 dark:bg-purple-900/20 rounded-full -z-10"})]})]})}),i.jsx(ps,{value:"leads-generation",className:"mt-8",children:i.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[i.jsx(Wd,{className:"h-12 w-12 text-orange-500 mb-6"}),i.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Leads Generation"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"Our lead generation solutions help businesses identify and connect with potential customers, nurturing them through the sales pipeline to generate more revenue."}),i.jsxs("div",{className:"space-y-4 mb-8",children:[i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-orange-600 dark:text-orange-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Lead Capture Systems"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Strategic lead forms, landing pages, and conversion-optimized content."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-orange-600 dark:text-orange-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Lead Qualification"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"AI-powered scoring and segmentation to identify your most valuable prospects."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-orange-600 dark:text-orange-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"Lead Nurturing Campaigns"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Automated communication sequences that guide prospects toward purchase decisions."})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"flex-shrink-0 h-6 w-6 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center mr-3 mt-1",children:i.jsx("span",{className:"text-orange-600 dark:text-orange-300 text-sm font-semibold",children:"✓"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"font-semibold mb-1",children:"CRM Integration"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Seamless connection with your CRM system for efficient lead management."})]})]})]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{children:["Generate More Leads",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border",children:[i.jsx("img",{src:"https://images.unsplash.com/photo-1455849318743-b2233052fcff?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"Lead generation",className:"w-full rounded-lg object-cover",style:{aspectRatio:"4/3"}}),i.jsxs("div",{className:"mt-6",children:[i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Lead Generation Results"}),i.jsxs("ul",{className:"space-y-2",children:[i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Increase qualified leads by up to 70%"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Reduce cost per acquisition by 30-40%"})]}),i.jsxs("li",{className:"flex items-center",children:[i.jsx("span",{className:"bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 p-1 rounded-full mr-2",children:i.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),i.jsx("span",{children:"Shorten sales cycles with targeted nurturing"})]})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-orange-100/50 dark:bg-orange-900/20 rounded-full -z-10"})]})]})})]})})}),i.jsx("section",{className:"bg-gradient-to-r from-primary to-accent text-white py-16",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center",children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Accelerate Your Business Growth?"}),i.jsxs("p",{className:"text-white/90 max-w-2xl mx-auto mb-8",children:["Contact ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," today to discuss how our technology solutions can help your retail or wholesale business thrive in the digital age."]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{size:"lg",variant:"secondary",className:"font-medium",children:["Schedule a Consultation",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]})})})]})}const ls=m.forwardRef(({className:t,...e},s)=>i.jsx("div",{ref:s,className:ee("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));ls.displayName="Card";const bw=m.forwardRef(({className:t,...e},s)=>i.jsx("div",{ref:s,className:ee("flex flex-col space-y-1.5 p-6",t),...e}));bw.displayName="CardHeader";const ww=m.forwardRef(({className:t,...e},s)=>i.jsx("div",{ref:s,className:ee("text-2xl font-semibold leading-none tracking-tight",t),...e}));ww.displayName="CardTitle";const jw=m.forwardRef(({className:t,...e},s)=>i.jsx("div",{ref:s,className:ee("text-sm text-muted-foreground",t),...e}));jw.displayName="CardDescription";const cs=m.forwardRef(({className:t,...e},s)=>i.jsx("div",{ref:s,className:ee("p-6 pt-0",t),...e}));cs.displayName="CardContent";const Nw=m.forwardRef(({className:t,...e},s)=>i.jsx("div",{ref:s,className:ee("flex items-center p-6 pt-0",t),...e}));Nw.displayName="CardFooter";function Sw(){return i.jsxs("div",{children:[i.jsx("section",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 py-20 md:py-28",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[i.jsx("span",{className:"inline-block bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 px-4 py-1.5 rounded-full text-sm font-medium mb-6",children:"Artificial Intelligence"}),i.jsxs("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:["Transform Your Business with ",i.jsx("span",{className:"gradient-heading",children:"AI Solutions"})]}),i.jsxs("p",{className:"text-lg text-muted-foreground mb-8 max-w-xl",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," delivers cutting-edge AI technology that helps retail and wholesale businesses optimize operations, enhance customer experiences, and drive growth."]}),i.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[i.jsx(ae,{href:"/contact",children:i.jsxs(me,{size:"lg",className:"w-full sm:w-auto",children:["Get Started",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})}),i.jsx(ae,{href:"#ai-applications",children:i.jsx(me,{variant:"outline",size:"lg",className:"w-full sm:w-auto",children:"Explore Applications"})})]})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-border p-6 relative z-10",children:[i.jsx(xa,{className:"h-16 w-16 text-blue-500 mb-6"}),i.jsx("h2",{className:"text-2xl font-bold mb-4",children:"AI-Powered Innovation"}),i.jsx("p",{className:"text-muted-foreground mb-6",children:"Our AI solutions combine advanced machine learning, natural language processing, and computer vision to solve complex business challenges."}),i.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center",children:[i.jsx("h3",{className:"font-bold text-lg mb-1",children:"40%"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Operational Efficiency Boost"})]}),i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center",children:[i.jsx("h3",{className:"font-bold text-lg mb-1",children:"35%"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Sales Conversion Increase"})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-blue-200/50 dark:bg-blue-900/20 rounded-full -z-10"}),i.jsx("div",{className:"absolute top-10 -left-6 w-24 h-24 bg-indigo-200/50 dark:bg-indigo-900/20 rounded-full -z-10"})]})]})})}),i.jsx("section",{id:"ai-applications",className:"py-20",children:i.jsxs("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[i.jsxs("div",{className:"text-center mb-16",children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"AI Applications for Retail & Wholesale"}),i.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"Our specialized AI solutions are designed to address the specific challenges and opportunities in the retail and wholesale sectors."})]}),i.jsxs("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[i.jsx(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0,margin:"-50px"},children:i.jsx(ls,{className:"h-full",children:i.jsxs(cs,{className:"pt-6",children:[i.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Mg,{className:"h-6 w-6 text-blue-600 dark:text-blue-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Predictive Analytics"}),i.jsx("p",{className:"text-muted-foreground mb-5",children:"Leverage historical data to forecast trends, customer behaviors, and inventory needs with remarkable accuracy. Make smarter business decisions based on data-driven insights."}),i.jsxs("ul",{className:"space-y-2 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),i.jsx("span",{children:"Demand forecasting and inventory optimization"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),i.jsx("span",{children:"Sales prediction and trend analysis"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),i.jsx("span",{children:"Customer lifetime value estimation"})]})]})]})})}),i.jsx(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0,margin:"-50px"},children:i.jsx(ls,{className:"h-full",children:i.jsxs(cs,{className:"pt-6",children:[i.jsx("div",{className:"bg-purple-100 dark:bg-purple-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Gg,{className:"h-6 w-6 text-purple-600 dark:text-purple-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Personalized Recommendations"}),i.jsx("p",{className:"text-muted-foreground mb-5",children:"AI-powered recommendation engines that analyze customer behavior and preferences to suggest relevant products, increasing cross-selling and average order values."}),i.jsxs("ul",{className:"space-y-2 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),i.jsx("span",{children:"Product recommendation systems"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),i.jsx("span",{children:"Personalized shopping experiences"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),i.jsx("span",{children:"Dynamic pricing optimization"})]})]})]})})}),i.jsx(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0,margin:"-50px"},children:i.jsx(ls,{className:"h-full",children:i.jsxs(cs,{className:"pt-6",children:[i.jsx("div",{className:"bg-green-100 dark:bg-green-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Mi,{className:"h-6 w-6 text-green-600 dark:text-green-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Intelligent Chatbots"}),i.jsx("p",{className:"text-muted-foreground mb-5",children:"AI-powered conversational agents that provide instant customer support, process orders, and answer product questions 24/7, enhancing customer satisfaction while reducing costs."}),i.jsxs("ul",{className:"space-y-2 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-green-500 mr-2",children:"•"}),i.jsx("span",{children:"24/7 customer service automation"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-green-500 mr-2",children:"•"}),i.jsx("span",{children:"Order processing and tracking"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-green-500 mr-2",children:"•"}),i.jsx("span",{children:"Product information and recommendations"})]})]})]})})}),i.jsx(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0,margin:"-50px"},children:i.jsx(ls,{className:"h-full",children:i.jsxs(cs,{className:"pt-6",children:[i.jsx("div",{className:"bg-amber-100 dark:bg-amber-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Kg,{className:"h-6 w-6 text-amber-600 dark:text-amber-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Visual Search & Recognition"}),i.jsx("p",{className:"text-muted-foreground mb-5",children:"Computer vision technology that enables customers to search for products using images, enhancing the shopping experience and making product discovery intuitive."}),i.jsxs("ul",{className:"space-y-2 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-amber-500 mr-2",children:"•"}),i.jsx("span",{children:"Image-based product search"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-amber-500 mr-2",children:"•"}),i.jsx("span",{children:"Visual similarity recommendations"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-amber-500 mr-2",children:"•"}),i.jsx("span",{children:"Automated product categorization"})]})]})]})})}),i.jsx(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.4},viewport:{once:!0,margin:"-50px"},children:i.jsx(ls,{className:"h-full",children:i.jsxs(cs,{className:"pt-6",children:[i.jsx("div",{className:"bg-red-100 dark:bg-red-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Lg,{className:"h-6 w-6 text-red-600 dark:text-red-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Inventory Management"}),i.jsx("p",{className:"text-muted-foreground mb-5",children:"AI systems that optimize inventory levels across multiple locations, predict stockouts, and automate reordering to minimize costs while ensuring product availability."}),i.jsxs("ul",{className:"space-y-2 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-red-500 mr-2",children:"•"}),i.jsx("span",{children:"Smart inventory forecasting"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-red-500 mr-2",children:"•"}),i.jsx("span",{children:"Automated replenishment systems"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-red-500 mr-2",children:"•"}),i.jsx("span",{children:"Multi-location inventory optimization"})]})]})]})})}),i.jsx(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.5},viewport:{once:!0,margin:"-50px"},children:i.jsx(ls,{className:"h-full",children:i.jsxs(cs,{className:"pt-6",children:[i.jsx("div",{className:"bg-indigo-100 dark:bg-indigo-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Ug,{className:"h-6 w-6 text-indigo-600 dark:text-indigo-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Customer Sentiment Analysis"}),i.jsx("p",{className:"text-muted-foreground mb-5",children:"NLP technology that analyzes customer reviews, social media mentions, and feedback to understand sentiment, identify issues, and improve your products and services."}),i.jsxs("ul",{className:"space-y-2 mb-6",children:[i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-indigo-500 mr-2",children:"•"}),i.jsx("span",{children:"Review and feedback analysis"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-indigo-500 mr-2",children:"•"}),i.jsx("span",{children:"Social media monitoring"})]}),i.jsxs("li",{className:"flex items-start",children:[i.jsx("span",{className:"text-indigo-500 mr-2",children:"•"}),i.jsx("span",{children:"Brand reputation management"})]})]})]})})})]})]})}),i.jsx("section",{className:"bg-slate-50 dark:bg-slate-900 py-20",children:i.jsxs("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[i.jsxs("div",{className:"text-center mb-16",children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Our AI Implementation Process"}),i.jsx("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"We follow a proven methodology to ensure successful AI integration that delivers measurable business results for your retail or wholesale operation."})]}),i.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx("span",{className:"text-primary font-bold text-xl",children:"1"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Discovery & Analysis"}),i.jsx("p",{className:"text-muted-foreground",children:"We begin by deeply understanding your business challenges, data availability, and strategic objectives to identify the most impactful AI applications."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx("span",{className:"text-primary font-bold text-xl",children:"2"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Solution Design"}),i.jsx("p",{className:"text-muted-foreground",children:"Our experts design a customized AI solution tailored to your specific needs, creating a detailed roadmap for implementation and integration."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx("span",{className:"text-primary font-bold text-xl",children:"3"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Implementation"}),i.jsx("p",{className:"text-muted-foreground",children:"We develop and deploy your AI solution, ensuring seamless integration with your existing systems and thorough testing to validate performance."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border md:col-span-3 lg:col-span-1",children:[i.jsx("div",{className:"bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx("span",{className:"text-primary font-bold text-xl",children:"4"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Training & Adoption"}),i.jsx("p",{className:"text-muted-foreground",children:"We provide comprehensive training for your team to ensure successful adoption and maximum value from your new AI capabilities."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.4},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border md:col-start-2 lg:col-start-2",children:[i.jsx("div",{className:"bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx("span",{className:"text-primary font-bold text-xl",children:"5"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Continuous Optimization"}),i.jsx("p",{className:"text-muted-foreground",children:"Our ongoing support ensures your AI solution continuously improves, adapting to changing business conditions and leveraging new data."})]})]})]})}),i.jsx("section",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-20",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center",children:[i.jsx(Yg,{className:"h-12 w-12 mx-auto mb-6 text-white/80"}),i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Transform Your Business with AI?"}),i.jsxs("p",{className:"text-white/90 max-w-2xl mx-auto mb-8",children:["Contact ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," today to discuss how our AI solutions can help your retail or wholesale business stay competitive and drive growth."]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{size:"lg",variant:"secondary",className:"font-medium",children:["Schedule a Consultation",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]})})})]})}const Cw="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCA2NzAgODAiPgogIDwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyOS41LjEsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiAyLjEuMCBCdWlsZCAxNDEpICAtLT4KICA8ZGVmcz4KICAgIDxzdHlsZT4KICAgICAgLnN0MCB7CiAgICAgICAgZmlsbDogI2ZhZmFmYTsKICAgICAgICBmb250LWZhbWlseTogQmxlbmRhU2NyaXB0LCAnQmxlbmRhIFNjcmlwdCc7CiAgICAgICAgZm9udC1zaXplOiA3MnB4OwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8dGV4dCBjbGFzcz0ic3QwIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMC42NiA2Ny42NSkiPjx0c3BhbiB4PSIwIiB5PSIwIj5NUCBBZHZhbmNlIFNvbHV0aW9uczwvdHNwYW4+PC90ZXh0Pgo8L3N2Zz4=",kw=({className:t="h-8 w-auto",alt:e="MP Advance Solutions"})=>{const[s,n]=m.useState(!1),r=()=>{n(!0)};return i.jsx("img",{src:s?iu:Cw,alt:e,className:t,onError:r})};function Tw(){return i.jsxs("div",{children:[i.jsx("section",{className:"bg-slate-50 dark:bg-slate-900 py-16 md:py-24",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[i.jsx("span",{className:"inline-block bg-primary/10 text-primary px-4 py-1.5 rounded-full text-sm font-medium mb-6",children:"Our Story"}),i.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Creating Advanced Solutions for Modern Businesses"}),i.jsxs("p",{className:"text-lg text-muted-foreground mb-8",children:[i.jsx(kw,{className:"h-8 w-auto"})," is dedicated to helping retail and wholesale businesses thrive in the digital age with cutting-edge technology solutions."]})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-border p-6 relative z-10",children:[i.jsx("img",{src:"https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"MP Advance Solutions team",className:"w-full rounded-lg object-cover mb-6",style:{aspectRatio:"16/9"}}),i.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center",children:[i.jsx("h3",{className:"font-bold text-xl mb-1",children:"10+"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Years of Experience"})]}),i.jsxs("div",{className:"bg-slate-50 dark:bg-slate-900 p-4 rounded-lg text-center",children:[i.jsx("h3",{className:"font-bold text-xl mb-1",children:"500+"}),i.jsx("p",{className:"text-muted-foreground text-sm",children:"Projects Delivered"})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-primary/10 rounded-full -z-10"}),i.jsx("div",{className:"absolute top-10 -left-6 w-24 h-24 bg-accent/10 rounded-full -z-10"})]})]})})}),i.jsx("section",{className:"py-16",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"grid md:grid-cols-2 gap-12",children:[i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-8 border border-border",children:[i.jsx("div",{className:"bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx(_g,{className:"h-6 w-6 text-primary"})}),i.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Our Mission"}),i.jsx("p",{className:"text-muted-foreground mb-4",children:"To empower retail and wholesale businesses with cutting-edge technology solutions that drive growth, efficiency, and innovation in an increasingly digital marketplace."}),i.jsx("p",{className:"text-muted-foreground",children:"We are committed to delivering custom-tailored solutions that address the unique challenges and opportunities faced by our clients, ensuring they remain competitive and successful in their industries."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-8 border border-border",children:[i.jsx("div",{className:"bg-accent/10 w-12 h-12 rounded-full flex items-center justify-center mb-6",children:i.jsx(ir,{className:"h-6 w-6 text-accent"})}),i.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Our Vision"}),i.jsx("p",{className:"text-muted-foreground mb-4",children:"To be the leading provider of innovative business technology solutions, recognized for excellence in AI implementation, ecommerce development, and business process optimization for retail and wholesale sectors."}),i.jsx("p",{className:"text-muted-foreground",children:"We aim to create a future where every business, regardless of size, can leverage advanced technology to enhance customer experiences, streamline operations, and achieve sustainable growth."})]})]})})}),i.jsx("section",{className:"bg-slate-50 dark:bg-slate-900 py-16",children:i.jsxs("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[i.jsxs("div",{className:"text-center mb-16",children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Our Core Values"}),i.jsxs("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:["These principles guide everything we do at ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," and define our approach to serving clients."]})]}),i.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-blue-100 dark:bg-blue-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Rg,{className:"h-6 w-6 text-blue-600 dark:text-blue-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Innovation"}),i.jsx("p",{className:"text-muted-foreground",children:"We constantly explore new technologies and methodologies to provide our clients with cutting-edge solutions that give them a competitive advantage."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-green-100 dark:bg-green-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(ex,{className:"h-6 w-6 text-green-600 dark:text-green-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Client Partnership"}),i.jsx("p",{className:"text-muted-foreground",children:"We view ourselves as partners in our clients' success, taking the time to understand their business and working collaboratively to achieve their goals."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-purple-100 dark:bg-purple-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Eg,{className:"h-6 w-6 text-purple-600 dark:text-purple-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Excellence"}),i.jsx("p",{className:"text-muted-foreground",children:"We are committed to delivering solutions of the highest quality, with meticulous attention to detail and a focus on exceeding our clients' expectations."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border",children:[i.jsx("div",{className:"bg-amber-100 dark:bg-amber-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx(Dg,{className:"h-6 w-6 text-amber-600 dark:text-amber-300"})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Adaptability"}),i.jsx("p",{className:"text-muted-foreground",children:"We embrace change and remain flexible, allowing us to respond quickly to evolving technologies and market conditions to benefit our clients."})]}),i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.4},viewport:{once:!0},className:"bg-white dark:bg-slate-800 rounded-xl p-6 border border-border md:col-span-2 lg:col-span-1",children:[i.jsx("div",{className:"bg-red-100 dark:bg-red-900 w-12 h-12 rounded-lg flex items-center justify-center mb-6",children:i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-red-600 dark:text-red-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})})}),i.jsx("h3",{className:"text-xl font-bold mb-3",children:"Integrity"}),i.jsx("p",{className:"text-muted-foreground",children:"We conduct business with honesty, transparency, and ethical standards, building trust and long-lasting relationships with our clients and partners."})]})]})]})}),i.jsx("section",{className:"py-16",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},children:[i.jsx("h2",{className:"text-3xl font-bold mb-6",children:"Our Approach"}),i.jsxs("p",{className:"text-muted-foreground mb-6",children:["At ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"}),", we take a consultative and collaborative approach to every project, ensuring that our solutions are perfectly aligned with our clients' business objectives."]}),i.jsxs("div",{className:"space-y-6 mb-8",children:[i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("span",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold",children:"1"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Understand"}),i.jsx("p",{className:"text-muted-foreground",children:"We begin by thoroughly understanding your business, challenges, goals, and market position."})]})]}),i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("span",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold",children:"2"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Design"}),i.jsx("p",{className:"text-muted-foreground",children:"We create a customized solution design that addresses your specific needs and objectives."})]})]}),i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("span",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold",children:"3"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Implement"}),i.jsx("p",{className:"text-muted-foreground",children:"We execute the plan with precision, keeping you informed and involved throughout the process."})]})]}),i.jsxs("div",{className:"flex",children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("span",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold",children:"4"})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Support"}),i.jsx("p",{className:"text-muted-foreground",children:"We provide ongoing support and optimization to ensure your solution continues to deliver value."})]})]})]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{children:["Work With Us",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},className:"relative",children:[i.jsxs("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 border border-border",children:[i.jsx("img",{src:"https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",alt:"Team collaboration",className:"w-full rounded-lg object-cover mb-6",style:{aspectRatio:"4/3"}}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5",children:i.jsx("div",{className:"bg-primary h-2.5 rounded-full",style:{width:"95%"}})}),i.jsx("span",{className:"ml-4 text-sm font-medium",children:"Client Satisfaction"})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5",children:i.jsx("div",{className:"bg-primary h-2.5 rounded-full",style:{width:"90%"}})}),i.jsx("span",{className:"ml-4 text-sm font-medium",children:"Project Delivery"})]}),i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2.5",children:i.jsx("div",{className:"bg-primary h-2.5 rounded-full",style:{width:"98%"}})}),i.jsx("span",{className:"ml-4 text-sm font-medium",children:"Support Response"})]})]})]}),i.jsx("div",{className:"absolute -bottom-6 -right-6 w-32 h-32 bg-accent/10 rounded-full -z-10"})]})]})})}),i.jsx("section",{className:"bg-gradient-to-r from-primary to-accent text-white py-16",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs(z.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center",children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Transform Your Business?"}),i.jsxs("p",{className:"text-white/90 max-w-2xl mx-auto mb-8",children:["Contact ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," today to discuss how our technology solutions can help your retail or wholesale business thrive in the digital age."]}),i.jsx(ae,{href:"/contact",children:i.jsxs(me,{size:"lg",variant:"secondary",className:"font-medium",children:["Get in Touch",i.jsx(ze,{className:"ml-2 h-4 w-4"})]})})]})})})]})}var Kn=t=>t.type==="checkbox",gs=t=>t instanceof Date,Be=t=>t==null;const Gh=t=>typeof t=="object";var Ae=t=>!Be(t)&&!Array.isArray(t)&&Gh(t)&&!gs(t),Yh=t=>Ae(t)&&t.target?Kn(t.target)?t.target.checked:t.target.value:t,Aw=t=>t.substring(0,t.search(/\.\d+(\.|$)/))||t,Xh=(t,e)=>t.has(Aw(e)),Pw=t=>{const e=t.constructor&&t.constructor.prototype;return Ae(e)&&e.hasOwnProperty("isPrototypeOf")},to=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Me(t){let e;const s=Array.isArray(t),n=typeof FileList<"u"?t instanceof FileList:!1;if(t instanceof Date)e=new Date(t);else if(t instanceof Set)e=new Set(t);else if(!(to&&(t instanceof Blob||n))&&(s||Ae(t)))if(e=s?[]:{},!s&&!Pw(t))e=t;else for(const r in t)t.hasOwnProperty(r)&&(e[r]=Me(t[r]));else return t;return e}var Yr=t=>Array.isArray(t)?t.filter(Boolean):[],Te=t=>t===void 0,R=(t,e,s)=>{if(!e||!Ae(t))return s;const n=Yr(e.split(/[,[\].]+?/)).reduce((r,a)=>Be(r)?r:r[a],t);return Te(n)||n===t?Te(t[e])?s:t[e]:n},He=t=>typeof t=="boolean",so=t=>/^\w*$/.test(t),Qh=t=>Yr(t.replace(/["|']|\]/g,"").split(/\.|\[/)),he=(t,e,s)=>{let n=-1;const r=so(e)?[e]:Qh(e),a=r.length,o=a-1;for(;++n<a;){const l=r[n];let c=s;if(n!==o){const d=t[l];c=Ae(d)||Array.isArray(d)?d:isNaN(+r[n+1])?{}:[]}if(l==="__proto__"||l==="constructor"||l==="prototype")return;t[l]=c,t=t[l]}};const wr={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},dt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},kt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Jh=de.createContext(null),Xr=()=>de.useContext(Jh),_w=t=>{const{children:e,...s}=t;return de.createElement(Jh.Provider,{value:s},e)};var ef=(t,e,s,n=!0)=>{const r={defaultValues:e._defaultValues};for(const a in t)Object.defineProperty(r,a,{get:()=>{const o=a;return e._proxyFormState[o]!==dt.all&&(e._proxyFormState[o]=!n||dt.all),s&&(s[o]=!0),t[o]}});return r};const no=typeof window<"u"?m.useLayoutEffect:m.useEffect;function Ew(t){const e=Xr(),{control:s=e.control,disabled:n,name:r,exact:a}=t||{},[o,l]=de.useState(s._formState),c=de.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return no(()=>s._subscribe({name:r,formState:c.current,exact:a,callback:d=>{!n&&l({...s._formState,...d})}}),[r,n,a]),de.useEffect(()=>{c.current.isValid&&s._setValid(!0)},[s]),de.useMemo(()=>ef(o,s,c.current,!1),[o,s])}var xt=t=>typeof t=="string",tf=(t,e,s,n,r)=>xt(t)?(n&&e.watch.add(t),R(s,t,r)):Array.isArray(t)?t.map(a=>(n&&e.watch.add(a),R(s,a))):(n&&(e.watchAll=!0),s);function Rw(t){const e=Xr(),{control:s=e.control,name:n,defaultValue:r,disabled:a,exact:o}=t||{},l=de.useRef(r),[c,d]=de.useState(s._getWatch(n,l.current));return no(()=>s._subscribe({name:n,formState:{values:!0},exact:o,callback:u=>!a&&d(tf(n,s._names,u.values||s._formValues,!1,l.current))}),[n,s,a,o]),de.useEffect(()=>s._removeUnmounted()),c}function Mw(t){const e=Xr(),{name:s,disabled:n,control:r=e.control,shouldUnregister:a}=t,o=Xh(r._names.array,s),l=Rw({control:r,name:s,defaultValue:R(r._formValues,s,R(r._defaultValues,s,t.defaultValue)),exact:!0}),c=Ew({control:r,name:s,exact:!0}),d=de.useRef(t),u=de.useRef(r.register(s,{...t.rules,value:l,...He(t.disabled)?{disabled:t.disabled}:{}})),h=de.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!R(c.errors,s)},isDirty:{enumerable:!0,get:()=>!!R(c.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!R(c.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!R(c.validatingFields,s)},error:{enumerable:!0,get:()=>R(c.errors,s)}}),[c,s]),f=de.useCallback(g=>u.current.onChange({target:{value:Yh(g),name:s},type:wr.CHANGE}),[s]),x=de.useCallback(()=>u.current.onBlur({target:{value:R(r._formValues,s),name:s},type:wr.BLUR}),[s,r._formValues]),b=de.useCallback(g=>{const j=R(r._fields,s);j&&g&&(j._f.ref={focus:()=>g.focus&&g.focus(),select:()=>g.select&&g.select(),setCustomValidity:v=>g.setCustomValidity(v),reportValidity:()=>g.reportValidity()})},[r._fields,s]),p=de.useMemo(()=>({name:s,value:l,...He(n)||c.disabled?{disabled:c.disabled||n}:{},onChange:f,onBlur:x,ref:b}),[s,n,c.disabled,f,x,b,l]);return de.useEffect(()=>{const g=r._options.shouldUnregister||a;r.register(s,{...d.current.rules,...He(d.current.disabled)?{disabled:d.current.disabled}:{}});const j=(v,w)=>{const T=R(r._fields,v);T&&T._f&&(T._f.mount=w)};if(j(s,!0),g){const v=Me(R(r._options.defaultValues,s));he(r._defaultValues,s,v),Te(R(r._formValues,s))&&he(r._formValues,s,v)}return!o&&r.register(s),()=>{(o?g&&!r._state.action:g)?r.unregister(s):j(s,!1)}},[s,r,o,a]),de.useEffect(()=>{r._setDisabledField({disabled:n,name:s})},[n,s,r]),de.useMemo(()=>({field:p,formState:c,fieldState:h}),[p,c,h])}const Iw=t=>t.render(Mw(t));var sf=(t,e,s,n,r)=>e?{...s[t],types:{...s[t]&&s[t].types?s[t].types:{},[n]:r||!0}}:{},Cn=t=>Array.isArray(t)?t:[t],Yl=()=>{let t=[];return{get observers(){return t},next:r=>{for(const a of t)a.next&&a.next(r)},subscribe:r=>(t.push(r),{unsubscribe:()=>{t=t.filter(a=>a!==r)}}),unsubscribe:()=>{t=[]}}},Xi=t=>Be(t)||!Gh(t);function Bt(t,e){if(Xi(t)||Xi(e))return t===e;if(gs(t)&&gs(e))return t.getTime()===e.getTime();const s=Object.keys(t),n=Object.keys(e);if(s.length!==n.length)return!1;for(const r of s){const a=t[r];if(!n.includes(r))return!1;if(r!=="ref"){const o=e[r];if(gs(a)&&gs(o)||Ae(a)&&Ae(o)||Array.isArray(a)&&Array.isArray(o)?!Bt(a,o):a!==o)return!1}}return!0}var $e=t=>Ae(t)&&!Object.keys(t).length,ro=t=>t.type==="file",ut=t=>typeof t=="function",jr=t=>{if(!to)return!1;const e=t?t.ownerDocument:0;return t instanceof(e&&e.defaultView?e.defaultView.HTMLElement:HTMLElement)},nf=t=>t.type==="select-multiple",io=t=>t.type==="radio",Vw=t=>io(t)||Kn(t),Ci=t=>jr(t)&&t.isConnected;function Dw(t,e){const s=e.slice(0,-1).length;let n=0;for(;n<s;)t=Te(t)?n++:t[e[n++]];return t}function Lw(t){for(const e in t)if(t.hasOwnProperty(e)&&!Te(t[e]))return!1;return!0}function Ee(t,e){const s=Array.isArray(e)?e:so(e)?[e]:Qh(e),n=s.length===1?t:Dw(t,s),r=s.length-1,a=s[r];return n&&delete n[a],r!==0&&(Ae(n)&&$e(n)||Array.isArray(n)&&Lw(n))&&Ee(t,s.slice(0,-1)),t}var rf=t=>{for(const e in t)if(ut(t[e]))return!0;return!1};function Nr(t,e={}){const s=Array.isArray(t);if(Ae(t)||s)for(const n in t)Array.isArray(t[n])||Ae(t[n])&&!rf(t[n])?(e[n]=Array.isArray(t[n])?[]:{},Nr(t[n],e[n])):Be(t[n])||(e[n]=!0);return e}function af(t,e,s){const n=Array.isArray(t);if(Ae(t)||n)for(const r in t)Array.isArray(t[r])||Ae(t[r])&&!rf(t[r])?Te(e)||Xi(s[r])?s[r]=Array.isArray(t[r])?Nr(t[r],[]):{...Nr(t[r])}:af(t[r],Be(e)?{}:e[r],s[r]):s[r]=!Bt(t[r],e[r]);return s}var fn=(t,e)=>af(t,e,Nr(e));const Xl={value:!1,isValid:!1},Ql={value:!0,isValid:!0};var of=t=>{if(Array.isArray(t)){if(t.length>1){const e=t.filter(s=>s&&s.checked&&!s.disabled).map(s=>s.value);return{value:e,isValid:!!e.length}}return t[0].checked&&!t[0].disabled?t[0].attributes&&!Te(t[0].attributes.value)?Te(t[0].value)||t[0].value===""?Ql:{value:t[0].value,isValid:!0}:Ql:Xl}return Xl},lf=(t,{valueAsNumber:e,valueAsDate:s,setValueAs:n})=>Te(t)?t:e?t===""?NaN:t&&+t:s&&xt(t)?new Date(t):n?n(t):t;const Jl={isValid:!1,value:null};var cf=t=>Array.isArray(t)?t.reduce((e,s)=>s&&s.checked&&!s.disabled?{isValid:!0,value:s.value}:e,Jl):Jl;function ec(t){const e=t.ref;return ro(e)?e.files:io(e)?cf(t.refs).value:nf(e)?[...e.selectedOptions].map(({value:s})=>s):Kn(e)?of(t.refs).value:lf(Te(e.value)?t.ref.value:e.value,t)}var Ow=(t,e,s,n)=>{const r={};for(const a of t){const o=R(e,a);o&&he(r,a,o._f)}return{criteriaMode:s,names:[...t],fields:r,shouldUseNativeValidation:n}},Sr=t=>t instanceof RegExp,mn=t=>Te(t)?t:Sr(t)?t.source:Ae(t)?Sr(t.value)?t.value.source:t.value:t,tc=t=>({isOnSubmit:!t||t===dt.onSubmit,isOnBlur:t===dt.onBlur,isOnChange:t===dt.onChange,isOnAll:t===dt.all,isOnTouch:t===dt.onTouched});const sc="AsyncFunction";var Fw=t=>!!t&&!!t.validate&&!!(ut(t.validate)&&t.validate.constructor.name===sc||Ae(t.validate)&&Object.values(t.validate).find(e=>e.constructor.name===sc)),Bw=t=>t.mount&&(t.required||t.min||t.max||t.maxLength||t.minLength||t.pattern||t.validate),nc=(t,e,s)=>!s&&(e.watchAll||e.watch.has(t)||[...e.watch].some(n=>t.startsWith(n)&&/^\.\w+/.test(t.slice(n.length))));const kn=(t,e,s,n)=>{for(const r of s||Object.keys(t)){const a=R(t,r);if(a){const{_f:o,...l}=a;if(o){if(o.refs&&o.refs[0]&&e(o.refs[0],r)&&!n)return!0;if(o.ref&&e(o.ref,o.name)&&!n)return!0;if(kn(l,e))break}else if(Ae(l)&&kn(l,e))break}}};function rc(t,e,s){const n=R(t,s);if(n||so(s))return{error:n,name:s};const r=s.split(".");for(;r.length;){const a=r.join("."),o=R(e,a),l=R(t,a);if(o&&!Array.isArray(o)&&s!==a)return{name:s};if(l&&l.type)return{name:a,error:l};if(l&&l.root&&l.root.type)return{name:`${a}.root`,error:l.root};r.pop()}return{name:s}}var zw=(t,e,s,n)=>{s(t);const{name:r,...a}=t;return $e(a)||Object.keys(a).length>=Object.keys(e).length||Object.keys(a).find(o=>e[o]===(!n||dt.all))},Uw=(t,e,s)=>!t||!e||t===e||Cn(t).some(n=>n&&(s?n===e:n.startsWith(e)||e.startsWith(n))),Ww=(t,e,s,n,r)=>r.isOnAll?!1:!s&&r.isOnTouch?!(e||t):(s?n.isOnBlur:r.isOnBlur)?!t:(s?n.isOnChange:r.isOnChange)?t:!0,$w=(t,e)=>!Yr(R(t,e)).length&&Ee(t,e),Hw=(t,e,s)=>{const n=Cn(R(t,s));return he(n,"root",e[s]),he(t,s,n),t},cr=t=>xt(t);function ic(t,e,s="validate"){if(cr(t)||Array.isArray(t)&&t.every(cr)||He(t)&&!t)return{type:s,message:cr(t)?t:"",ref:e}}var Es=t=>Ae(t)&&!Sr(t)?t:{value:t,message:""},ac=async(t,e,s,n,r,a)=>{const{ref:o,refs:l,required:c,maxLength:d,minLength:u,min:h,max:f,pattern:x,validate:b,name:p,valueAsNumber:g,mount:j}=t._f,v=R(s,p);if(!j||e.has(p))return{};const w=l?l[0]:o,T=U=>{r&&w.reportValidity&&(w.setCustomValidity(He(U)?"":U||""),w.reportValidity())},C={},M=io(o),O=Kn(o),I=M||O,_=(g||ro(o))&&Te(o.value)&&Te(v)||jr(o)&&o.value===""||v===""||Array.isArray(v)&&!v.length,$=sf.bind(null,p,n,C),q=(U,G,V,te=kt.maxLength,se=kt.minLength)=>{const Q=U?G:V;C[p]={type:U?te:se,message:Q,ref:o,...$(U?te:se,Q)}};if(a?!Array.isArray(v)||!v.length:c&&(!I&&(_||Be(v))||He(v)&&!v||O&&!of(l).isValid||M&&!cf(l).isValid)){const{value:U,message:G}=cr(c)?{value:!!c,message:c}:Es(c);if(U&&(C[p]={type:kt.required,message:G,ref:w,...$(kt.required,G)},!n))return T(G),C}if(!_&&(!Be(h)||!Be(f))){let U,G;const V=Es(f),te=Es(h);if(!Be(v)&&!isNaN(v)){const se=o.valueAsNumber||v&&+v;Be(V.value)||(U=se>V.value),Be(te.value)||(G=se<te.value)}else{const se=o.valueAsDate||new Date(v),Q=Ue=>new Date(new Date().toDateString()+" "+Ue),ce=o.type=="time",we=o.type=="week";xt(V.value)&&v&&(U=ce?Q(v)>Q(V.value):we?v>V.value:se>new Date(V.value)),xt(te.value)&&v&&(G=ce?Q(v)<Q(te.value):we?v<te.value:se<new Date(te.value))}if((U||G)&&(q(!!U,V.message,te.message,kt.max,kt.min),!n))return T(C[p].message),C}if((d||u)&&!_&&(xt(v)||a&&Array.isArray(v))){const U=Es(d),G=Es(u),V=!Be(U.value)&&v.length>+U.value,te=!Be(G.value)&&v.length<+G.value;if((V||te)&&(q(V,U.message,G.message),!n))return T(C[p].message),C}if(x&&!_&&xt(v)){const{value:U,message:G}=Es(x);if(Sr(U)&&!v.match(U)&&(C[p]={type:kt.pattern,message:G,ref:o,...$(kt.pattern,G)},!n))return T(G),C}if(b){if(ut(b)){const U=await b(v,s),G=ic(U,w);if(G&&(C[p]={...G,...$(kt.validate,G.message)},!n))return T(G.message),C}else if(Ae(b)){let U={};for(const G in b){if(!$e(U)&&!n)break;const V=ic(await b[G](v,s),w,G);V&&(U={...V,...$(G,V.message)},T(V.message),n&&(C[p]=U))}if(!$e(U)&&(C[p]={ref:w,...U},!n))return C}}return T(!0),C};const Zw={mode:dt.onSubmit,reValidateMode:dt.onChange,shouldFocusError:!0};function Kw(t={}){let e={...Zw,...t},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:ut(e.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1};const n={};let r=Ae(e.defaultValues)||Ae(e.values)?Me(e.defaultValues||e.values)||{}:{},a=e.shouldUnregister?{}:Me(r),o={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c,d=0;const u={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let h={...u};const f={array:Yl(),state:Yl()},x=e.criteriaMode===dt.all,b=y=>N=>{clearTimeout(d),d=setTimeout(y,N)},p=async y=>{if(!e.disabled&&(u.isValid||h.isValid||y)){const N=e.resolver?$e((await O()).errors):await _(n,!0);N!==s.isValid&&f.state.next({isValid:N})}},g=(y,N)=>{!e.disabled&&(u.isValidating||u.validatingFields||h.isValidating||h.validatingFields)&&((y||Array.from(l.mount)).forEach(k=>{k&&(N?he(s.validatingFields,k,N):Ee(s.validatingFields,k))}),f.state.next({validatingFields:s.validatingFields,isValidating:!$e(s.validatingFields)}))},j=(y,N=[],k,L,E=!0,P=!0)=>{if(L&&k&&!e.disabled){if(o.action=!0,P&&Array.isArray(R(n,y))){const W=k(R(n,y),L.argA,L.argB);E&&he(n,y,W)}if(P&&Array.isArray(R(s.errors,y))){const W=k(R(s.errors,y),L.argA,L.argB);E&&he(s.errors,y,W),$w(s.errors,y)}if((u.touchedFields||h.touchedFields)&&P&&Array.isArray(R(s.touchedFields,y))){const W=k(R(s.touchedFields,y),L.argA,L.argB);E&&he(s.touchedFields,y,W)}(u.dirtyFields||h.dirtyFields)&&(s.dirtyFields=fn(r,a)),f.state.next({name:y,isDirty:q(y,N),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else he(a,y,N)},v=(y,N)=>{he(s.errors,y,N),f.state.next({errors:s.errors})},w=y=>{s.errors=y,f.state.next({errors:s.errors,isValid:!1})},T=(y,N,k,L)=>{const E=R(n,y);if(E){const P=R(a,y,Te(k)?R(r,y):k);Te(P)||L&&L.defaultChecked||N?he(a,y,N?P:ec(E._f)):V(y,P),o.mount&&p()}},C=(y,N,k,L,E)=>{let P=!1,W=!1;const fe={name:y};if(!e.disabled){if(!k||L){(u.isDirty||h.isDirty)&&(W=s.isDirty,s.isDirty=fe.isDirty=q(),P=W!==fe.isDirty);const ye=Bt(R(r,y),N);W=!!R(s.dirtyFields,y),ye?Ee(s.dirtyFields,y):he(s.dirtyFields,y,!0),fe.dirtyFields=s.dirtyFields,P=P||(u.dirtyFields||h.dirtyFields)&&W!==!ye}if(k){const ye=R(s.touchedFields,y);ye||(he(s.touchedFields,y,k),fe.touchedFields=s.touchedFields,P=P||(u.touchedFields||h.touchedFields)&&ye!==k)}P&&E&&f.state.next(fe)}return P?fe:{}},M=(y,N,k,L)=>{const E=R(s.errors,y),P=(u.isValid||h.isValid)&&He(N)&&s.isValid!==N;if(e.delayError&&k?(c=b(()=>v(y,k)),c(e.delayError)):(clearTimeout(d),c=null,k?he(s.errors,y,k):Ee(s.errors,y)),(k?!Bt(E,k):E)||!$e(L)||P){const W={...L,...P&&He(N)?{isValid:N}:{},errors:s.errors,name:y};s={...s,...W},f.state.next(W)}},O=async y=>{g(y,!0);const N=await e.resolver(a,e.context,Ow(y||l.mount,n,e.criteriaMode,e.shouldUseNativeValidation));return g(y),N},I=async y=>{const{errors:N}=await O(y);if(y)for(const k of y){const L=R(N,k);L?he(s.errors,k,L):Ee(s.errors,k)}else s.errors=N;return N},_=async(y,N,k={valid:!0})=>{for(const L in y){const E=y[L];if(E){const{_f:P,...W}=E;if(P){const fe=l.array.has(P.name),ye=E._f&&Fw(E._f);ye&&u.validatingFields&&g([L],!0);const Ge=await ac(E,l.disabled,a,x,e.shouldUseNativeValidation&&!N,fe);if(ye&&u.validatingFields&&g([L]),Ge[P.name]&&(k.valid=!1,N))break;!N&&(R(Ge,P.name)?fe?Hw(s.errors,Ge,P.name):he(s.errors,P.name,Ge[P.name]):Ee(s.errors,P.name))}!$e(W)&&await _(W,N,k)}}return k.valid},$=()=>{for(const y of l.unMount){const N=R(n,y);N&&(N._f.refs?N._f.refs.every(k=>!Ci(k)):!Ci(N._f.ref))&&K(y)}l.unMount=new Set},q=(y,N)=>!e.disabled&&(y&&N&&he(a,y,N),!Bt(Ue(),r)),U=(y,N,k)=>tf(y,l,{...o.mount?a:Te(N)?r:xt(y)?{[y]:N}:N},k,N),G=y=>Yr(R(o.mount?a:r,y,e.shouldUnregister?R(r,y,[]):[])),V=(y,N,k={})=>{const L=R(n,y);let E=N;if(L){const P=L._f;P&&(!P.disabled&&he(a,y,lf(N,P)),E=jr(P.ref)&&Be(N)?"":N,nf(P.ref)?[...P.ref.options].forEach(W=>W.selected=E.includes(W.value)):P.refs?Kn(P.ref)?P.refs.forEach(W=>{(!W.defaultChecked||!W.disabled)&&(Array.isArray(E)?W.checked=!!E.find(fe=>fe===W.value):W.checked=E===W.value||!!E)}):P.refs.forEach(W=>W.checked=W.value===E):ro(P.ref)?P.ref.value="":(P.ref.value=E,P.ref.type||f.state.next({name:y,values:Me(a)})))}(k.shouldDirty||k.shouldTouch)&&C(y,E,k.shouldTouch,k.shouldDirty,!0),k.shouldValidate&&we(y)},te=(y,N,k)=>{for(const L in N){if(!N.hasOwnProperty(L))return;const E=N[L],P=y+"."+L,W=R(n,P);(l.array.has(y)||Ae(E)||W&&!W._f)&&!gs(E)?te(P,E,k):V(P,E,k)}},se=(y,N,k={})=>{const L=R(n,y),E=l.array.has(y),P=Me(N);he(a,y,P),E?(f.array.next({name:y,values:Me(a)}),(u.isDirty||u.dirtyFields||h.isDirty||h.dirtyFields)&&k.shouldDirty&&f.state.next({name:y,dirtyFields:fn(r,a),isDirty:q(y,P)})):L&&!L._f&&!Be(P)?te(y,P,k):V(y,P,k),nc(y,l)&&f.state.next({...s}),f.state.next({name:o.mount?y:void 0,values:Me(a)})},Q=async y=>{o.mount=!0;const N=y.target;let k=N.name,L=!0;const E=R(n,k),P=ye=>{L=Number.isNaN(ye)||gs(ye)&&isNaN(ye.getTime())||Bt(ye,R(a,k,ye))},W=tc(e.mode),fe=tc(e.reValidateMode);if(E){let ye,Ge;const qn=N.type?ec(E._f):Yh(y),Vt=y.type===wr.BLUR||y.type===wr.FOCUS_OUT,Nm=!Bw(E._f)&&!e.resolver&&!R(s.errors,k)&&!E._f.deps||Ww(Vt,R(s.touchedFields,k),s.isSubmitted,fe,W),ri=nc(k,l,Vt);he(a,k,qn),Vt?(E._f.onBlur&&E._f.onBlur(y),c&&c(0)):E._f.onChange&&E._f.onChange(y);const ii=C(k,qn,Vt),Sm=!$e(ii)||ri;if(!Vt&&f.state.next({name:k,type:y.type,values:Me(a)}),Nm)return(u.isValid||h.isValid)&&(e.mode==="onBlur"?Vt&&p():Vt||p()),Sm&&f.state.next({name:k,...ri?{}:ii});if(!Vt&&ri&&f.state.next({...s}),e.resolver){const{errors:mo}=await O([k]);if(P(qn),L){const Cm=rc(s.errors,n,k),po=rc(mo,n,Cm.name||k);ye=po.error,k=po.name,Ge=$e(mo)}}else g([k],!0),ye=(await ac(E,l.disabled,a,x,e.shouldUseNativeValidation))[k],g([k]),P(qn),L&&(ye?Ge=!1:(u.isValid||h.isValid)&&(Ge=await _(n,!0)));L&&(E._f.deps&&we(E._f.deps),M(k,Ge,ye,ii))}},ce=(y,N)=>{if(R(s.errors,N)&&y.focus)return y.focus(),1},we=async(y,N={})=>{let k,L;const E=Cn(y);if(e.resolver){const P=await I(Te(y)?y:E);k=$e(P),L=y?!E.some(W=>R(P,W)):k}else y?(L=(await Promise.all(E.map(async P=>{const W=R(n,P);return await _(W&&W._f?{[P]:W}:W)}))).every(Boolean),!(!L&&!s.isValid)&&p()):L=k=await _(n);return f.state.next({...!xt(y)||(u.isValid||h.isValid)&&k!==s.isValid?{}:{name:y},...e.resolver||!y?{isValid:k}:{},errors:s.errors}),N.shouldFocus&&!L&&kn(n,ce,y?E:l.mount),L},Ue=y=>{const N={...o.mount?a:r};return Te(y)?N:xt(y)?R(N,y):y.map(k=>R(N,k))},Nt=(y,N)=>({invalid:!!R((N||s).errors,y),isDirty:!!R((N||s).dirtyFields,y),error:R((N||s).errors,y),isValidating:!!R(s.validatingFields,y),isTouched:!!R((N||s).touchedFields,y)}),St=y=>{y&&Cn(y).forEach(N=>Ee(s.errors,N)),f.state.next({errors:y?s.errors:{}})},nt=(y,N,k)=>{const L=(R(n,y,{_f:{}})._f||{}).ref,E=R(s.errors,y)||{},{ref:P,message:W,type:fe,...ye}=E;he(s.errors,y,{...ye,...N,ref:L}),f.state.next({name:y,errors:s.errors,isValid:!1}),k&&k.shouldFocus&&L&&L.focus&&L.focus()},rt=(y,N)=>ut(y)?f.state.subscribe({next:k=>y(U(void 0,N),k)}):U(y,N,!0),ns=y=>f.state.subscribe({next:N=>{Uw(y.name,N.name,y.exact)&&zw(N,y.formState||u,un,y.reRenderRoot)&&y.callback({values:{...a},...s,...N})}}).unsubscribe,rs=y=>(o.mount=!0,h={...h,...y.formState},ns({...y,formState:h})),K=(y,N={})=>{for(const k of y?Cn(y):l.mount)l.mount.delete(k),l.array.delete(k),N.keepValue||(Ee(n,k),Ee(a,k)),!N.keepError&&Ee(s.errors,k),!N.keepDirty&&Ee(s.dirtyFields,k),!N.keepTouched&&Ee(s.touchedFields,k),!N.keepIsValidating&&Ee(s.validatingFields,k),!e.shouldUnregister&&!N.keepDefaultValue&&Ee(r,k);f.state.next({values:Me(a)}),f.state.next({...s,...N.keepDirty?{isDirty:q()}:{}}),!N.keepIsValid&&p()},ve=({disabled:y,name:N})=>{(He(y)&&o.mount||y||l.disabled.has(N))&&(y?l.disabled.add(N):l.disabled.delete(N))},Re=(y,N={})=>{let k=R(n,y);const L=He(N.disabled)||He(e.disabled);return he(n,y,{...k||{},_f:{...k&&k._f?k._f:{ref:{name:y}},name:y,mount:!0,...N}}),l.mount.add(y),k?ve({disabled:He(N.disabled)?N.disabled:e.disabled,name:y}):T(y,!0,N.value),{...L?{disabled:N.disabled||e.disabled}:{},...e.progressive?{required:!!N.required,min:mn(N.min),max:mn(N.max),minLength:mn(N.minLength),maxLength:mn(N.maxLength),pattern:mn(N.pattern)}:{},name:y,onChange:Q,onBlur:Q,ref:E=>{if(E){Re(y,N),k=R(n,y);const P=Te(E.value)&&E.querySelectorAll&&E.querySelectorAll("input,select,textarea")[0]||E,W=Vw(P),fe=k._f.refs||[];if(W?fe.find(ye=>ye===P):P===k._f.ref)return;he(n,y,{_f:{...k._f,...W?{refs:[...fe.filter(Ci),P,...Array.isArray(R(r,y))?[{}]:[]],ref:{type:P.type,name:y}}:{ref:P}}}),T(y,!1,void 0,P)}else k=R(n,y,{}),k._f&&(k._f.mount=!1),(e.shouldUnregister||N.shouldUnregister)&&!(Xh(l.array,y)&&o.action)&&l.unMount.add(y)}}},ge=()=>e.shouldFocusError&&kn(n,ce,l.mount),xe=y=>{He(y)&&(f.state.next({disabled:y}),kn(n,(N,k)=>{const L=R(n,k);L&&(N.disabled=L._f.disabled||y,Array.isArray(L._f.refs)&&L._f.refs.forEach(E=>{E.disabled=L._f.disabled||y}))},0,!1))},pe=(y,N)=>async k=>{let L;k&&(k.preventDefault&&k.preventDefault(),k.persist&&k.persist());let E=Me(a);if(f.state.next({isSubmitting:!0}),e.resolver){const{errors:P,values:W}=await O();s.errors=P,E=W}else await _(n);if(l.disabled.size)for(const P of l.disabled)he(E,P,void 0);if(Ee(s.errors,"root"),$e(s.errors)){f.state.next({errors:{}});try{await y(E,k)}catch(P){L=P}}else N&&await N({...s.errors},k),ge(),setTimeout(ge);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:$e(s.errors)&&!L,submitCount:s.submitCount+1,errors:s.errors}),L)throw L},Ct=(y,N={})=>{R(n,y)&&(Te(N.defaultValue)?se(y,Me(R(r,y))):(se(y,N.defaultValue),he(r,y,Me(N.defaultValue))),N.keepTouched||Ee(s.touchedFields,y),N.keepDirty||(Ee(s.dirtyFields,y),s.isDirty=N.defaultValue?q(y,Me(R(r,y))):q()),N.keepError||(Ee(s.errors,y),u.isValid&&p()),f.state.next({...s}))},ht=(y,N={})=>{const k=y?Me(y):r,L=Me(k),E=$e(y),P=E?r:L;if(N.keepDefaultValues||(r=k),!N.keepValues){if(N.keepDirtyValues){const W=new Set([...l.mount,...Object.keys(fn(r,a))]);for(const fe of Array.from(W))R(s.dirtyFields,fe)?he(P,fe,R(a,fe)):se(fe,R(P,fe))}else{if(to&&Te(y))for(const W of l.mount){const fe=R(n,W);if(fe&&fe._f){const ye=Array.isArray(fe._f.refs)?fe._f.refs[0]:fe._f.ref;if(jr(ye)){const Ge=ye.closest("form");if(Ge){Ge.reset();break}}}}for(const W of l.mount)se(W,R(P,W))}a=Me(P),f.array.next({values:{...P}}),f.state.next({values:{...P}})}l={mount:N.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!u.isValid||!!N.keepIsValid||!!N.keepDirtyValues,o.watch=!!e.shouldUnregister,f.state.next({submitCount:N.keepSubmitCount?s.submitCount:0,isDirty:E?!1:N.keepDirty?s.isDirty:!!(N.keepDefaultValues&&!Bt(y,r)),isSubmitted:N.keepIsSubmitted?s.isSubmitted:!1,dirtyFields:E?{}:N.keepDirtyValues?N.keepDefaultValues&&a?fn(r,a):s.dirtyFields:N.keepDefaultValues&&y?fn(r,y):N.keepDirty?s.dirtyFields:{},touchedFields:N.keepTouched?s.touchedFields:{},errors:N.keepErrors?s.errors:{},isSubmitSuccessful:N.keepIsSubmitSuccessful?s.isSubmitSuccessful:!1,isSubmitting:!1})},Ps=(y,N)=>ht(ut(y)?y(a):y,N),dn=(y,N={})=>{const k=R(n,y),L=k&&k._f;if(L){const E=L.refs?L.refs[0]:L.ref;E.focus&&(E.focus(),N.shouldSelect&&ut(E.select)&&E.select())}},un=y=>{s={...s,...y}},fo={control:{register:Re,unregister:K,getFieldState:Nt,handleSubmit:pe,setError:nt,_subscribe:ns,_runSchema:O,_focusError:ge,_getWatch:U,_getDirty:q,_setValid:p,_setFieldArray:j,_setDisabledField:ve,_setErrors:w,_getFieldArray:G,_reset:ht,_resetDefaultValues:()=>ut(e.defaultValues)&&e.defaultValues().then(y=>{Ps(y,e.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:$,_disableForm:xe,_subjects:f,_proxyFormState:u,get _fields(){return n},get _formValues(){return a},get _state(){return o},set _state(y){o=y},get _defaultValues(){return r},get _names(){return l},set _names(y){l=y},get _formState(){return s},get _options(){return e},set _options(y){e={...e,...y}}},subscribe:rs,trigger:we,register:Re,handleSubmit:pe,watch:rt,setValue:se,getValues:Ue,reset:Ps,resetField:Ct,clearErrors:St,unregister:K,setError:nt,setFocus:dn,getFieldState:Nt};return{...fo,formControl:fo}}function qw(t={}){const e=de.useRef(void 0),s=de.useRef(void 0),[n,r]=de.useState({isDirty:!1,isValidating:!1,isLoading:ut(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,isReady:!1,defaultValues:ut(t.defaultValues)?void 0:t.defaultValues});e.current||(e.current={...t.formControl?t.formControl:Kw(t),formState:n},t.formControl&&t.defaultValues&&!ut(t.defaultValues)&&t.formControl.reset(t.defaultValues,t.resetOptions));const a=e.current.control;return a._options=t,no(()=>{const o=a._subscribe({formState:a._proxyFormState,callback:()=>r({...a._formState}),reRenderRoot:!0});return r(l=>({...l,isReady:!0})),a._formState.isReady=!0,o},[a]),de.useEffect(()=>a._disableForm(t.disabled),[a,t.disabled]),de.useEffect(()=>{t.mode&&(a._options.mode=t.mode),t.reValidateMode&&(a._options.reValidateMode=t.reValidateMode)},[a,t.mode,t.reValidateMode]),de.useEffect(()=>{t.errors&&(a._setErrors(t.errors),a._focusError())},[a,t.errors]),de.useEffect(()=>{t.shouldUnregister&&a._subjects.state.next({values:a._getWatch()})},[a,t.shouldUnregister]),de.useEffect(()=>{if(a._proxyFormState.isDirty){const o=a._getDirty();o!==n.isDirty&&a._subjects.state.next({isDirty:o})}},[a,n.isDirty]),de.useEffect(()=>{t.values&&!Bt(t.values,s.current)?(a._reset(t.values,a._options.resetOptions),s.current=t.values,r(o=>({...o}))):a._resetDefaultValues()},[a,t.values]),de.useEffect(()=>{a._state.mount||(a._setValid(),a._state.mount=!0),a._state.watch&&(a._state.watch=!1,a._subjects.state.next({...a._formState})),a._removeUnmounted()}),e.current.formState=ef(n,a),e.current}var Gw="Label",df=m.forwardRef((t,e)=>i.jsx(ue.label,{...t,ref:e,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||((r=t.onMouseDown)==null||r.call(t,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));df.displayName=Gw;var uf=df;const Yw=Br("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),hf=m.forwardRef(({className:t,...e},s)=>i.jsx(uf,{ref:s,className:ee(Yw(),t),...e}));hf.displayName=uf.displayName;const Xw=_w,ff=m.createContext({}),Rs=({...t})=>i.jsx(ff.Provider,{value:{name:t.name},children:i.jsx(Iw,{...t})}),Qr=()=>{const t=m.useContext(ff),e=m.useContext(mf),{getFieldState:s,formState:n}=Xr(),r=s(t.name,n);if(!t)throw new Error("useFormField should be used within <FormField>");const{id:a}=e;return{id:a,name:t.name,formItemId:`${a}-form-item`,formDescriptionId:`${a}-form-item-description`,formMessageId:`${a}-form-item-message`,...r}},mf=m.createContext({}),ds=m.forwardRef(({className:t,...e},s)=>{const n=m.useId();return i.jsx(mf.Provider,{value:{id:n},children:i.jsx("div",{ref:s,className:ee("space-y-2",t),...e})})});ds.displayName="FormItem";const us=m.forwardRef(({className:t,...e},s)=>{const{error:n,formItemId:r}=Qr();return i.jsx(hf,{ref:s,className:ee(n&&"text-destructive",t),htmlFor:r,...e})});us.displayName="FormLabel";const hs=m.forwardRef(({...t},e)=>{const{error:s,formItemId:n,formDescriptionId:r,formMessageId:a}=Qr();return i.jsx(zc,{ref:e,id:n,"aria-describedby":s?`${r} ${a}`:`${r}`,"aria-invalid":!!s,...t})});hs.displayName="FormControl";const Qw=m.forwardRef(({className:t,...e},s)=>{const{formDescriptionId:n}=Qr();return i.jsx("p",{ref:s,id:n,className:ee("text-sm text-muted-foreground",t),...e})});Qw.displayName="FormDescription";const fs=m.forwardRef(({className:t,children:e,...s},n)=>{const{error:r,formMessageId:a}=Qr(),o=r?String((r==null?void 0:r.message)??""):e;return o?i.jsx("p",{ref:n,id:a,className:ee("text-sm font-medium text-destructive",t),...s,children:o}):null});fs.displayName="FormMessage";const Bs=m.forwardRef(({className:t,type:e,...s},n)=>i.jsx("input",{type:e,className:ee("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:n,...s}));Bs.displayName="Input";const pf=m.forwardRef(({className:t,...e},s)=>i.jsx("textarea",{className:ee("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...e}));pf.displayName="Textarea";const oc=(t,e,s)=>{if(t&&"reportValidity"in t){const n=R(s,e);t.setCustomValidity(n&&n.message||""),t.reportValidity()}},gf=(t,e)=>{for(const s in e.fields){const n=e.fields[s];n&&n.ref&&"reportValidity"in n.ref?oc(n.ref,s,t):n.refs&&n.refs.forEach(r=>oc(r,s,t))}},Jw=(t,e)=>{e.shouldUseNativeValidation&&gf(t,e);const s={};for(const n in t){const r=R(e.fields,n),a=Object.assign(t[n]||{},{ref:r&&r.ref});if(e1(e.names||Object.keys(t),n)){const o=Object.assign({},R(s,n));he(o,"root",a),he(s,n,o)}else he(s,n,a)}return s},e1=(t,e)=>t.some(s=>s.startsWith(e+"."));var t1=function(t,e){for(var s={};t.length;){var n=t[0],r=n.code,a=n.message,o=n.path.join(".");if(!s[o])if("unionErrors"in n){var l=n.unionErrors[0].errors[0];s[o]={message:l.message,type:l.code}}else s[o]={message:a,type:r};if("unionErrors"in n&&n.unionErrors.forEach(function(u){return u.errors.forEach(function(h){return t.push(h)})}),e){var c=s[o].types,d=c&&c[n.code];s[o]=sf(o,e,s,r,d?[].concat(d,n.message):n.message)}t.shift()}return s},s1=function(t,e,s){return s===void 0&&(s={}),function(n,r,a){try{return Promise.resolve(function(o,l){try{var c=Promise.resolve(t[s.mode==="sync"?"parse":"parseAsync"](n,e)).then(function(d){return a.shouldUseNativeValidation&&gf({},a),{errors:{},values:s.raw?n:d}})}catch(d){return l(d)}return c&&c.then?c.then(void 0,l):c}(0,function(o){if(function(l){return Array.isArray(l==null?void 0:l.errors)}(o))return{values:{},errors:Jw(t1(o.errors,!a.shouldUseNativeValidation&&a.criteriaMode==="all"),a)};throw o}))}catch(o){return Promise.reject(o)}}},le;(function(t){t.assertEqual=r=>{};function e(r){}t.assertIs=e;function s(r){throw new Error}t.assertNever=s,t.arrayToEnum=r=>{const a={};for(const o of r)a[o]=o;return a},t.getValidEnumValues=r=>{const a=t.objectKeys(r).filter(l=>typeof r[r[l]]!="number"),o={};for(const l of a)o[l]=r[l];return t.objectValues(o)},t.objectValues=r=>t.objectKeys(r).map(function(a){return r[a]}),t.objectKeys=typeof Object.keys=="function"?r=>Object.keys(r):r=>{const a=[];for(const o in r)Object.prototype.hasOwnProperty.call(r,o)&&a.push(o);return a},t.find=(r,a)=>{for(const o of r)if(a(o))return o},t.isInteger=typeof Number.isInteger=="function"?r=>Number.isInteger(r):r=>typeof r=="number"&&Number.isFinite(r)&&Math.floor(r)===r;function n(r,a=" | "){return r.map(o=>typeof o=="string"?`'${o}'`:o).join(a)}t.joinValues=n,t.jsonStringifyReplacer=(r,a)=>typeof a=="bigint"?a.toString():a})(le||(le={}));var lc;(function(t){t.mergeShapes=(e,s)=>({...e,...s})})(lc||(lc={}));const F=le.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Ft=t=>{switch(typeof t){case"undefined":return F.undefined;case"string":return F.string;case"number":return Number.isNaN(t)?F.nan:F.number;case"boolean":return F.boolean;case"function":return F.function;case"bigint":return F.bigint;case"symbol":return F.symbol;case"object":return Array.isArray(t)?F.array:t===null?F.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?F.promise:typeof Map<"u"&&t instanceof Map?F.map:typeof Set<"u"&&t instanceof Set?F.set:typeof Date<"u"&&t instanceof Date?F.date:F.object;default:return F.unknown}},A=le.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class It extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const s=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,s):this.__proto__=s,this.name="ZodError",this.issues=e}format(e){const s=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const o of a.issues)if(o.code==="invalid_union")o.unionErrors.map(r);else if(o.code==="invalid_return_type")r(o.returnTypeError);else if(o.code==="invalid_arguments")r(o.argumentsError);else if(o.path.length===0)n._errors.push(s(o));else{let l=n,c=0;for(;c<o.path.length;){const d=o.path[c];c===o.path.length-1?(l[d]=l[d]||{_errors:[]},l[d]._errors.push(s(o))):l[d]=l[d]||{_errors:[]},l=l[d],c++}}};return r(this),n}static assert(e){if(!(e instanceof It))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,le.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=s=>s.message){const s={},n=[];for(const r of this.issues)r.path.length>0?(s[r.path[0]]=s[r.path[0]]||[],s[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:s}}get formErrors(){return this.flatten()}}It.create=t=>new It(t);const Qi=(t,e)=>{let s;switch(t.code){case A.invalid_type:t.received===F.undefined?s="Required":s=`Expected ${t.expected}, received ${t.received}`;break;case A.invalid_literal:s=`Invalid literal value, expected ${JSON.stringify(t.expected,le.jsonStringifyReplacer)}`;break;case A.unrecognized_keys:s=`Unrecognized key(s) in object: ${le.joinValues(t.keys,", ")}`;break;case A.invalid_union:s="Invalid input";break;case A.invalid_union_discriminator:s=`Invalid discriminator value. Expected ${le.joinValues(t.options)}`;break;case A.invalid_enum_value:s=`Invalid enum value. Expected ${le.joinValues(t.options)}, received '${t.received}'`;break;case A.invalid_arguments:s="Invalid function arguments";break;case A.invalid_return_type:s="Invalid function return type";break;case A.invalid_date:s="Invalid date";break;case A.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(s=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(s=`${s} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?s=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?s=`Invalid input: must end with "${t.validation.endsWith}"`:le.assertNever(t.validation):t.validation!=="regex"?s=`Invalid ${t.validation}`:s="Invalid";break;case A.too_small:t.type==="array"?s=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?s=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?s=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?s=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:s="Invalid input";break;case A.too_big:t.type==="array"?s=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?s=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?s=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?s=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?s=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:s="Invalid input";break;case A.custom:s="Invalid input";break;case A.invalid_intersection_types:s="Intersection results could not be merged";break;case A.not_multiple_of:s=`Number must be a multiple of ${t.multipleOf}`;break;case A.not_finite:s="Number must be finite";break;default:s=e.defaultError,le.assertNever(t)}return{message:s}};let n1=Qi;function r1(){return n1}const i1=t=>{const{data:e,path:s,errorMaps:n,issueData:r}=t,a=[...s,...r.path||[]],o={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let l="";const c=n.filter(d=>!!d).slice().reverse();for(const d of c)l=d(o,{data:e,defaultError:l}).message;return{...r,path:a,message:l}};function D(t,e){const s=r1(),n=i1({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,s,s===Qi?void 0:Qi].filter(r=>!!r)});t.common.issues.push(n)}class qe{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,s){const n=[];for(const r of s){if(r.status==="aborted")return Y;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,s){const n=[];for(const r of s){const a=await r.key,o=await r.value;n.push({key:a,value:o})}return qe.mergeObjectSync(e,n)}static mergeObjectSync(e,s){const n={};for(const r of s){const{key:a,value:o}=r;if(a.status==="aborted"||o.status==="aborted")return Y;a.status==="dirty"&&e.dirty(),o.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof o.value<"u"||r.alwaysSet)&&(n[a.value]=o.value)}return{status:e.value,value:n}}}const Y=Object.freeze({status:"aborted"}),yn=t=>({status:"dirty",value:t}),tt=t=>({status:"valid",value:t}),cc=t=>t.status==="aborted",dc=t=>t.status==="dirty",tn=t=>t.status==="valid",Cr=t=>typeof Promise<"u"&&t instanceof Promise;var B;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(B||(B={}));class Qt{constructor(e,s,n,r){this._cachedPath=[],this.parent=e,this.data=s,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const uc=(t,e)=>{if(tn(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const s=new It(t.common.issues);return this._error=s,this._error}}};function ne(t){if(!t)return{};const{errorMap:e,invalid_type_error:s,required_error:n,description:r}=t;if(e&&(s||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(o,l)=>{const{message:c}=t;return o.code==="invalid_enum_value"?{message:c??l.defaultError}:typeof l.data>"u"?{message:c??n??l.defaultError}:o.code!=="invalid_type"?{message:l.defaultError}:{message:c??s??l.defaultError}},description:r}}class oe{get description(){return this._def.description}_getType(e){return Ft(e.data)}_getOrReturnCtx(e,s){return s||{common:e.parent.common,data:e.data,parsedType:Ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new qe,ctx:{common:e.parent.common,data:e.data,parsedType:Ft(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const s=this._parse(e);if(Cr(s))throw new Error("Synchronous parse encountered promise.");return s}_parseAsync(e){const s=this._parse(e);return Promise.resolve(s)}parse(e,s){const n=this.safeParse(e,s);if(n.success)return n.data;throw n.error}safeParse(e,s){const n={common:{issues:[],async:(s==null?void 0:s.async)??!1,contextualErrorMap:s==null?void 0:s.errorMap},path:(s==null?void 0:s.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)},r=this._parseSync({data:e,path:n.path,parent:n});return uc(n,r)}"~validate"(e){var n,r;const s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:s});return tn(a)?{value:a.value}:{issues:s.common.issues}}catch(a){(r=(n=a==null?void 0:a.message)==null?void 0:n.toLowerCase())!=null&&r.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:s}).then(a=>tn(a)?{value:a.value}:{issues:s.common.issues})}async parseAsync(e,s){const n=await this.safeParseAsync(e,s);if(n.success)return n.data;throw n.error}async safeParseAsync(e,s){const n={common:{issues:[],contextualErrorMap:s==null?void 0:s.errorMap,async:!0},path:(s==null?void 0:s.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ft(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(Cr(r)?r:Promise.resolve(r));return uc(n,a)}refine(e,s){const n=r=>typeof s=="string"||typeof s>"u"?{message:s}:typeof s=="function"?s(r):s;return this._refinement((r,a)=>{const o=e(r),l=()=>a.addIssue({code:A.custom,...n(r)});return typeof Promise<"u"&&o instanceof Promise?o.then(c=>c?!0:(l(),!1)):o?!0:(l(),!1)})}refinement(e,s){return this._refinement((n,r)=>e(n)?!0:(r.addIssue(typeof s=="function"?s(n,r):s),!1))}_refinement(e){return new nn({schema:this,typeName:X.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:s=>this["~validate"](s)}}optional(){return Gt.create(this,this._def)}nullable(){return rn.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return bt.create(this)}promise(){return Pr.create(this,this._def)}or(e){return Tr.create([this,e],this._def)}and(e){return Ar.create(this,e,this._def)}transform(e){return new nn({...ne(this._def),schema:this,typeName:X.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const s=typeof e=="function"?e:()=>e;return new ea({...ne(this._def),innerType:this,defaultValue:s,typeName:X.ZodDefault})}brand(){return new A1({typeName:X.ZodBranded,type:this,...ne(this._def)})}catch(e){const s=typeof e=="function"?e:()=>e;return new ta({...ne(this._def),innerType:this,catchValue:s,typeName:X.ZodCatch})}describe(e){const s=this.constructor;return new s({...this._def,description:e})}pipe(e){return ao.create(this,e)}readonly(){return sa.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const a1=/^c[^\s-]{8,}$/i,o1=/^[0-9a-z]+$/,l1=/^[0-9A-HJKMNP-TV-Z]{26}$/i,c1=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,d1=/^[a-z0-9_-]{21}$/i,u1=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,h1=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,f1=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,m1="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let ki;const p1=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,g1=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,x1=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,y1=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,v1=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,b1=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,xf="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",w1=new RegExp(`^${xf}$`);function yf(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const s=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${s}`}function j1(t){return new RegExp(`^${yf(t)}$`)}function N1(t){let e=`${xf}T${yf(t)}`;const s=[];return s.push(t.local?"Z?":"Z"),t.offset&&s.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${s.join("|")})`,new RegExp(`^${e}$`)}function S1(t,e){return!!((e==="v4"||!e)&&p1.test(t)||(e==="v6"||!e)&&x1.test(t))}function C1(t,e){if(!u1.test(t))return!1;try{const[s]=t.split("."),n=s.replace(/-/g,"+").replace(/_/g,"/").padEnd(s.length+(4-s.length%4)%4,"="),r=JSON.parse(atob(n));return!(typeof r!="object"||r===null||"typ"in r&&(r==null?void 0:r.typ)!=="JWT"||!r.alg||e&&r.alg!==e)}catch{return!1}}function k1(t,e){return!!((e==="v4"||!e)&&g1.test(t)||(e==="v6"||!e)&&y1.test(t))}class qt extends oe{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==F.string){const a=this._getOrReturnCtx(e);return D(a,{code:A.invalid_type,expected:F.string,received:a.parsedType}),Y}const n=new qe;let r;for(const a of this._def.checks)if(a.kind==="min")e.data.length<a.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:A.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),n.dirty());else if(a.kind==="max")e.data.length>a.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:A.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),n.dirty());else if(a.kind==="length"){const o=e.data.length>a.value,l=e.data.length<a.value;(o||l)&&(r=this._getOrReturnCtx(e,r),o?D(r,{code:A.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):l&&D(r,{code:A.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),n.dirty())}else if(a.kind==="email")f1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"email",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="emoji")ki||(ki=new RegExp(m1,"u")),ki.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"emoji",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="uuid")c1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"uuid",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="nanoid")d1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"nanoid",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="cuid")a1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"cuid",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="cuid2")o1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"cuid2",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="ulid")l1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"ulid",code:A.invalid_string,message:a.message}),n.dirty());else if(a.kind==="url")try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),D(r,{validation:"url",code:A.invalid_string,message:a.message}),n.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"regex",code:A.invalid_string,message:a.message}),n.dirty())):a.kind==="trim"?e.data=e.data.trim():a.kind==="includes"?e.data.includes(a.value,a.position)||(r=this._getOrReturnCtx(e,r),D(r,{code:A.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),n.dirty()):a.kind==="toLowerCase"?e.data=e.data.toLowerCase():a.kind==="toUpperCase"?e.data=e.data.toUpperCase():a.kind==="startsWith"?e.data.startsWith(a.value)||(r=this._getOrReturnCtx(e,r),D(r,{code:A.invalid_string,validation:{startsWith:a.value},message:a.message}),n.dirty()):a.kind==="endsWith"?e.data.endsWith(a.value)||(r=this._getOrReturnCtx(e,r),D(r,{code:A.invalid_string,validation:{endsWith:a.value},message:a.message}),n.dirty()):a.kind==="datetime"?N1(a).test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{code:A.invalid_string,validation:"datetime",message:a.message}),n.dirty()):a.kind==="date"?w1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{code:A.invalid_string,validation:"date",message:a.message}),n.dirty()):a.kind==="time"?j1(a).test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{code:A.invalid_string,validation:"time",message:a.message}),n.dirty()):a.kind==="duration"?h1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"duration",code:A.invalid_string,message:a.message}),n.dirty()):a.kind==="ip"?S1(e.data,a.version)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"ip",code:A.invalid_string,message:a.message}),n.dirty()):a.kind==="jwt"?C1(e.data,a.alg)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"jwt",code:A.invalid_string,message:a.message}),n.dirty()):a.kind==="cidr"?k1(e.data,a.version)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"cidr",code:A.invalid_string,message:a.message}),n.dirty()):a.kind==="base64"?v1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"base64",code:A.invalid_string,message:a.message}),n.dirty()):a.kind==="base64url"?b1.test(e.data)||(r=this._getOrReturnCtx(e,r),D(r,{validation:"base64url",code:A.invalid_string,message:a.message}),n.dirty()):le.assertNever(a);return{status:n.value,value:e.data}}_regex(e,s,n){return this.refinement(r=>e.test(r),{validation:s,code:A.invalid_string,...B.errToObj(n)})}_addCheck(e){return new qt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...B.errToObj(e)})}url(e){return this._addCheck({kind:"url",...B.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...B.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...B.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...B.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...B.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...B.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...B.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...B.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...B.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...B.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...B.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...B.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(e==null?void 0:e.offset)??!1,local:(e==null?void 0:e.local)??!1,...B.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...B.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...B.errToObj(e)})}regex(e,s){return this._addCheck({kind:"regex",regex:e,...B.errToObj(s)})}includes(e,s){return this._addCheck({kind:"includes",value:e,position:s==null?void 0:s.position,...B.errToObj(s==null?void 0:s.message)})}startsWith(e,s){return this._addCheck({kind:"startsWith",value:e,...B.errToObj(s)})}endsWith(e,s){return this._addCheck({kind:"endsWith",value:e,...B.errToObj(s)})}min(e,s){return this._addCheck({kind:"min",value:e,...B.errToObj(s)})}max(e,s){return this._addCheck({kind:"max",value:e,...B.errToObj(s)})}length(e,s){return this._addCheck({kind:"length",value:e,...B.errToObj(s)})}nonempty(e){return this.min(1,B.errToObj(e))}trim(){return new qt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new qt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new qt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const s of this._def.checks)s.kind==="min"&&(e===null||s.value>e)&&(e=s.value);return e}get maxLength(){let e=null;for(const s of this._def.checks)s.kind==="max"&&(e===null||s.value<e)&&(e=s.value);return e}}qt.create=t=>new qt({checks:[],typeName:X.ZodString,coerce:(t==null?void 0:t.coerce)??!1,...ne(t)});function T1(t,e){const s=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=s>n?s:n,a=Number.parseInt(t.toFixed(r).replace(".","")),o=Number.parseInt(e.toFixed(r).replace(".",""));return a%o/10**r}class Mn extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==F.number){const a=this._getOrReturnCtx(e);return D(a,{code:A.invalid_type,expected:F.number,received:a.parsedType}),Y}let n;const r=new qe;for(const a of this._def.checks)a.kind==="int"?le.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:A.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:A.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:A.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):a.kind==="multipleOf"?T1(e.data,a.value)!==0&&(n=this._getOrReturnCtx(e,n),D(n,{code:A.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),D(n,{code:A.not_finite,message:a.message}),r.dirty()):le.assertNever(a);return{status:r.value,value:e.data}}gte(e,s){return this.setLimit("min",e,!0,B.toString(s))}gt(e,s){return this.setLimit("min",e,!1,B.toString(s))}lte(e,s){return this.setLimit("max",e,!0,B.toString(s))}lt(e,s){return this.setLimit("max",e,!1,B.toString(s))}setLimit(e,s,n,r){return new Mn({...this._def,checks:[...this._def.checks,{kind:e,value:s,inclusive:n,message:B.toString(r)}]})}_addCheck(e){return new Mn({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:B.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:B.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:B.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:B.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:B.toString(e)})}multipleOf(e,s){return this._addCheck({kind:"multipleOf",value:e,message:B.toString(s)})}finite(e){return this._addCheck({kind:"finite",message:B.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:B.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:B.toString(e)})}get minValue(){let e=null;for(const s of this._def.checks)s.kind==="min"&&(e===null||s.value>e)&&(e=s.value);return e}get maxValue(){let e=null;for(const s of this._def.checks)s.kind==="max"&&(e===null||s.value<e)&&(e=s.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&le.isInteger(e.value))}get isFinite(){let e=null,s=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(s===null||n.value>s)&&(s=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(s)&&Number.isFinite(e)}}Mn.create=t=>new Mn({checks:[],typeName:X.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...ne(t)});class In extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==F.bigint)return this._getInvalidInput(e);let n;const r=new qe;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:A.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(n=this._getOrReturnCtx(e,n),D(n,{code:A.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),D(n,{code:A.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):le.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){const s=this._getOrReturnCtx(e);return D(s,{code:A.invalid_type,expected:F.bigint,received:s.parsedType}),Y}gte(e,s){return this.setLimit("min",e,!0,B.toString(s))}gt(e,s){return this.setLimit("min",e,!1,B.toString(s))}lte(e,s){return this.setLimit("max",e,!0,B.toString(s))}lt(e,s){return this.setLimit("max",e,!1,B.toString(s))}setLimit(e,s,n,r){return new In({...this._def,checks:[...this._def.checks,{kind:e,value:s,inclusive:n,message:B.toString(r)}]})}_addCheck(e){return new In({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:B.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:B.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:B.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:B.toString(e)})}multipleOf(e,s){return this._addCheck({kind:"multipleOf",value:e,message:B.toString(s)})}get minValue(){let e=null;for(const s of this._def.checks)s.kind==="min"&&(e===null||s.value>e)&&(e=s.value);return e}get maxValue(){let e=null;for(const s of this._def.checks)s.kind==="max"&&(e===null||s.value<e)&&(e=s.value);return e}}In.create=t=>new In({checks:[],typeName:X.ZodBigInt,coerce:(t==null?void 0:t.coerce)??!1,...ne(t)});class hc extends oe{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==F.boolean){const n=this._getOrReturnCtx(e);return D(n,{code:A.invalid_type,expected:F.boolean,received:n.parsedType}),Y}return tt(e.data)}}hc.create=t=>new hc({typeName:X.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...ne(t)});class kr extends oe{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==F.date){const a=this._getOrReturnCtx(e);return D(a,{code:A.invalid_type,expected:F.date,received:a.parsedType}),Y}if(Number.isNaN(e.data.getTime())){const a=this._getOrReturnCtx(e);return D(a,{code:A.invalid_date}),Y}const n=new qe;let r;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:A.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),n.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(r=this._getOrReturnCtx(e,r),D(r,{code:A.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),n.dirty()):le.assertNever(a);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new kr({...this._def,checks:[...this._def.checks,e]})}min(e,s){return this._addCheck({kind:"min",value:e.getTime(),message:B.toString(s)})}max(e,s){return this._addCheck({kind:"max",value:e.getTime(),message:B.toString(s)})}get minDate(){let e=null;for(const s of this._def.checks)s.kind==="min"&&(e===null||s.value>e)&&(e=s.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const s of this._def.checks)s.kind==="max"&&(e===null||s.value<e)&&(e=s.value);return e!=null?new Date(e):null}}kr.create=t=>new kr({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:X.ZodDate,...ne(t)});class fc extends oe{_parse(e){if(this._getType(e)!==F.symbol){const n=this._getOrReturnCtx(e);return D(n,{code:A.invalid_type,expected:F.symbol,received:n.parsedType}),Y}return tt(e.data)}}fc.create=t=>new fc({typeName:X.ZodSymbol,...ne(t)});class mc extends oe{_parse(e){if(this._getType(e)!==F.undefined){const n=this._getOrReturnCtx(e);return D(n,{code:A.invalid_type,expected:F.undefined,received:n.parsedType}),Y}return tt(e.data)}}mc.create=t=>new mc({typeName:X.ZodUndefined,...ne(t)});class pc extends oe{_parse(e){if(this._getType(e)!==F.null){const n=this._getOrReturnCtx(e);return D(n,{code:A.invalid_type,expected:F.null,received:n.parsedType}),Y}return tt(e.data)}}pc.create=t=>new pc({typeName:X.ZodNull,...ne(t)});class gc extends oe{constructor(){super(...arguments),this._any=!0}_parse(e){return tt(e.data)}}gc.create=t=>new gc({typeName:X.ZodAny,...ne(t)});class xc extends oe{constructor(){super(...arguments),this._unknown=!0}_parse(e){return tt(e.data)}}xc.create=t=>new xc({typeName:X.ZodUnknown,...ne(t)});class Jt extends oe{_parse(e){const s=this._getOrReturnCtx(e);return D(s,{code:A.invalid_type,expected:F.never,received:s.parsedType}),Y}}Jt.create=t=>new Jt({typeName:X.ZodNever,...ne(t)});class yc extends oe{_parse(e){if(this._getType(e)!==F.undefined){const n=this._getOrReturnCtx(e);return D(n,{code:A.invalid_type,expected:F.void,received:n.parsedType}),Y}return tt(e.data)}}yc.create=t=>new yc({typeName:X.ZodVoid,...ne(t)});class bt extends oe{_parse(e){const{ctx:s,status:n}=this._processInputParams(e),r=this._def;if(s.parsedType!==F.array)return D(s,{code:A.invalid_type,expected:F.array,received:s.parsedType}),Y;if(r.exactLength!==null){const o=s.data.length>r.exactLength.value,l=s.data.length<r.exactLength.value;(o||l)&&(D(s,{code:o?A.too_big:A.too_small,minimum:l?r.exactLength.value:void 0,maximum:o?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&s.data.length<r.minLength.value&&(D(s,{code:A.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&s.data.length>r.maxLength.value&&(D(s,{code:A.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),s.common.async)return Promise.all([...s.data].map((o,l)=>r.type._parseAsync(new Qt(s,o,s.path,l)))).then(o=>qe.mergeArray(n,o));const a=[...s.data].map((o,l)=>r.type._parseSync(new Qt(s,o,s.path,l)));return qe.mergeArray(n,a)}get element(){return this._def.type}min(e,s){return new bt({...this._def,minLength:{value:e,message:B.toString(s)}})}max(e,s){return new bt({...this._def,maxLength:{value:e,message:B.toString(s)}})}length(e,s){return new bt({...this._def,exactLength:{value:e,message:B.toString(s)}})}nonempty(e){return this.min(1,e)}}bt.create=(t,e)=>new bt({type:t,minLength:null,maxLength:null,exactLength:null,typeName:X.ZodArray,...ne(e)});function Is(t){if(t instanceof Pe){const e={};for(const s in t.shape){const n=t.shape[s];e[s]=Gt.create(Is(n))}return new Pe({...t._def,shape:()=>e})}else return t instanceof bt?new bt({...t._def,type:Is(t.element)}):t instanceof Gt?Gt.create(Is(t.unwrap())):t instanceof rn?rn.create(Is(t.unwrap())):t instanceof Ss?Ss.create(t.items.map(e=>Is(e))):t}class Pe extends oe{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),s=le.objectKeys(e);return this._cached={shape:e,keys:s},this._cached}_parse(e){if(this._getType(e)!==F.object){const d=this._getOrReturnCtx(e);return D(d,{code:A.invalid_type,expected:F.object,received:d.parsedType}),Y}const{status:n,ctx:r}=this._processInputParams(e),{shape:a,keys:o}=this._getCached(),l=[];if(!(this._def.catchall instanceof Jt&&this._def.unknownKeys==="strip"))for(const d in r.data)o.includes(d)||l.push(d);const c=[];for(const d of o){const u=a[d],h=r.data[d];c.push({key:{status:"valid",value:d},value:u._parse(new Qt(r,h,r.path,d)),alwaysSet:d in r.data})}if(this._def.catchall instanceof Jt){const d=this._def.unknownKeys;if(d==="passthrough")for(const u of l)c.push({key:{status:"valid",value:u},value:{status:"valid",value:r.data[u]}});else if(d==="strict")l.length>0&&(D(r,{code:A.unrecognized_keys,keys:l}),n.dirty());else if(d!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const d=this._def.catchall;for(const u of l){const h=r.data[u];c.push({key:{status:"valid",value:u},value:d._parse(new Qt(r,h,r.path,u)),alwaysSet:u in r.data})}}return r.common.async?Promise.resolve().then(async()=>{const d=[];for(const u of c){const h=await u.key,f=await u.value;d.push({key:h,value:f,alwaysSet:u.alwaysSet})}return d}).then(d=>qe.mergeObjectSync(n,d)):qe.mergeObjectSync(n,c)}get shape(){return this._def.shape()}strict(e){return B.errToObj,new Pe({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(s,n)=>{var a,o;const r=((o=(a=this._def).errorMap)==null?void 0:o.call(a,s,n).message)??n.defaultError;return s.code==="unrecognized_keys"?{message:B.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new Pe({...this._def,unknownKeys:"strip"})}passthrough(){return new Pe({...this._def,unknownKeys:"passthrough"})}extend(e){return new Pe({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Pe({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:X.ZodObject})}setKey(e,s){return this.augment({[e]:s})}catchall(e){return new Pe({...this._def,catchall:e})}pick(e){const s={};for(const n of le.objectKeys(e))e[n]&&this.shape[n]&&(s[n]=this.shape[n]);return new Pe({...this._def,shape:()=>s})}omit(e){const s={};for(const n of le.objectKeys(this.shape))e[n]||(s[n]=this.shape[n]);return new Pe({...this._def,shape:()=>s})}deepPartial(){return Is(this)}partial(e){const s={};for(const n of le.objectKeys(this.shape)){const r=this.shape[n];e&&!e[n]?s[n]=r:s[n]=r.optional()}return new Pe({...this._def,shape:()=>s})}required(e){const s={};for(const n of le.objectKeys(this.shape))if(e&&!e[n])s[n]=this.shape[n];else{let a=this.shape[n];for(;a instanceof Gt;)a=a._def.innerType;s[n]=a}return new Pe({...this._def,shape:()=>s})}keyof(){return vf(le.objectKeys(this.shape))}}Pe.create=(t,e)=>new Pe({shape:()=>t,unknownKeys:"strip",catchall:Jt.create(),typeName:X.ZodObject,...ne(e)});Pe.strictCreate=(t,e)=>new Pe({shape:()=>t,unknownKeys:"strict",catchall:Jt.create(),typeName:X.ZodObject,...ne(e)});Pe.lazycreate=(t,e)=>new Pe({shape:t,unknownKeys:"strip",catchall:Jt.create(),typeName:X.ZodObject,...ne(e)});class Tr extends oe{_parse(e){const{ctx:s}=this._processInputParams(e),n=this._def.options;function r(a){for(const l of a)if(l.result.status==="valid")return l.result;for(const l of a)if(l.result.status==="dirty")return s.common.issues.push(...l.ctx.common.issues),l.result;const o=a.map(l=>new It(l.ctx.common.issues));return D(s,{code:A.invalid_union,unionErrors:o}),Y}if(s.common.async)return Promise.all(n.map(async a=>{const o={...s,common:{...s.common,issues:[]},parent:null};return{result:await a._parseAsync({data:s.data,path:s.path,parent:o}),ctx:o}})).then(r);{let a;const o=[];for(const c of n){const d={...s,common:{...s.common,issues:[]},parent:null},u=c._parseSync({data:s.data,path:s.path,parent:d});if(u.status==="valid")return u;u.status==="dirty"&&!a&&(a={result:u,ctx:d}),d.common.issues.length&&o.push(d.common.issues)}if(a)return s.common.issues.push(...a.ctx.common.issues),a.result;const l=o.map(c=>new It(c));return D(s,{code:A.invalid_union,unionErrors:l}),Y}}get options(){return this._def.options}}Tr.create=(t,e)=>new Tr({options:t,typeName:X.ZodUnion,...ne(e)});function Ji(t,e){const s=Ft(t),n=Ft(e);if(t===e)return{valid:!0,data:t};if(s===F.object&&n===F.object){const r=le.objectKeys(e),a=le.objectKeys(t).filter(l=>r.indexOf(l)!==-1),o={...t,...e};for(const l of a){const c=Ji(t[l],e[l]);if(!c.valid)return{valid:!1};o[l]=c.data}return{valid:!0,data:o}}else if(s===F.array&&n===F.array){if(t.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<t.length;a++){const o=t[a],l=e[a],c=Ji(o,l);if(!c.valid)return{valid:!1};r.push(c.data)}return{valid:!0,data:r}}else return s===F.date&&n===F.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class Ar extends oe{_parse(e){const{status:s,ctx:n}=this._processInputParams(e),r=(a,o)=>{if(cc(a)||cc(o))return Y;const l=Ji(a.value,o.value);return l.valid?((dc(a)||dc(o))&&s.dirty(),{status:s.value,value:l.data}):(D(n,{code:A.invalid_intersection_types}),Y)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,o])=>r(a,o)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Ar.create=(t,e,s)=>new Ar({left:t,right:e,typeName:X.ZodIntersection,...ne(s)});class Ss extends oe{_parse(e){const{status:s,ctx:n}=this._processInputParams(e);if(n.parsedType!==F.array)return D(n,{code:A.invalid_type,expected:F.array,received:n.parsedType}),Y;if(n.data.length<this._def.items.length)return D(n,{code:A.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Y;!this._def.rest&&n.data.length>this._def.items.length&&(D(n,{code:A.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),s.dirty());const a=[...n.data].map((o,l)=>{const c=this._def.items[l]||this._def.rest;return c?c._parse(new Qt(n,o,n.path,l)):null}).filter(o=>!!o);return n.common.async?Promise.all(a).then(o=>qe.mergeArray(s,o)):qe.mergeArray(s,a)}get items(){return this._def.items}rest(e){return new Ss({...this._def,rest:e})}}Ss.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ss({items:t,typeName:X.ZodTuple,rest:null,...ne(e)})};class vc extends oe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:s,ctx:n}=this._processInputParams(e);if(n.parsedType!==F.map)return D(n,{code:A.invalid_type,expected:F.map,received:n.parsedType}),Y;const r=this._def.keyType,a=this._def.valueType,o=[...n.data.entries()].map(([l,c],d)=>({key:r._parse(new Qt(n,l,n.path,[d,"key"])),value:a._parse(new Qt(n,c,n.path,[d,"value"]))}));if(n.common.async){const l=new Map;return Promise.resolve().then(async()=>{for(const c of o){const d=await c.key,u=await c.value;if(d.status==="aborted"||u.status==="aborted")return Y;(d.status==="dirty"||u.status==="dirty")&&s.dirty(),l.set(d.value,u.value)}return{status:s.value,value:l}})}else{const l=new Map;for(const c of o){const d=c.key,u=c.value;if(d.status==="aborted"||u.status==="aborted")return Y;(d.status==="dirty"||u.status==="dirty")&&s.dirty(),l.set(d.value,u.value)}return{status:s.value,value:l}}}}vc.create=(t,e,s)=>new vc({valueType:e,keyType:t,typeName:X.ZodMap,...ne(s)});class Vn extends oe{_parse(e){const{status:s,ctx:n}=this._processInputParams(e);if(n.parsedType!==F.set)return D(n,{code:A.invalid_type,expected:F.set,received:n.parsedType}),Y;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(D(n,{code:A.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),s.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(D(n,{code:A.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),s.dirty());const a=this._def.valueType;function o(c){const d=new Set;for(const u of c){if(u.status==="aborted")return Y;u.status==="dirty"&&s.dirty(),d.add(u.value)}return{status:s.value,value:d}}const l=[...n.data.values()].map((c,d)=>a._parse(new Qt(n,c,n.path,d)));return n.common.async?Promise.all(l).then(c=>o(c)):o(l)}min(e,s){return new Vn({...this._def,minSize:{value:e,message:B.toString(s)}})}max(e,s){return new Vn({...this._def,maxSize:{value:e,message:B.toString(s)}})}size(e,s){return this.min(e,s).max(e,s)}nonempty(e){return this.min(1,e)}}Vn.create=(t,e)=>new Vn({valueType:t,minSize:null,maxSize:null,typeName:X.ZodSet,...ne(e)});class bc extends oe{get schema(){return this._def.getter()}_parse(e){const{ctx:s}=this._processInputParams(e);return this._def.getter()._parse({data:s.data,path:s.path,parent:s})}}bc.create=(t,e)=>new bc({getter:t,typeName:X.ZodLazy,...ne(e)});class wc extends oe{_parse(e){if(e.data!==this._def.value){const s=this._getOrReturnCtx(e);return D(s,{received:s.data,code:A.invalid_literal,expected:this._def.value}),Y}return{status:"valid",value:e.data}}get value(){return this._def.value}}wc.create=(t,e)=>new wc({value:t,typeName:X.ZodLiteral,...ne(e)});function vf(t,e){return new sn({values:t,typeName:X.ZodEnum,...ne(e)})}class sn extends oe{_parse(e){if(typeof e.data!="string"){const s=this._getOrReturnCtx(e),n=this._def.values;return D(s,{expected:le.joinValues(n),received:s.parsedType,code:A.invalid_type}),Y}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const s=this._getOrReturnCtx(e),n=this._def.values;return D(s,{received:s.data,code:A.invalid_enum_value,options:n}),Y}return tt(e.data)}get options(){return this._def.values}get enum(){const e={};for(const s of this._def.values)e[s]=s;return e}get Values(){const e={};for(const s of this._def.values)e[s]=s;return e}get Enum(){const e={};for(const s of this._def.values)e[s]=s;return e}extract(e,s=this._def){return sn.create(e,{...this._def,...s})}exclude(e,s=this._def){return sn.create(this.options.filter(n=>!e.includes(n)),{...this._def,...s})}}sn.create=vf;class jc extends oe{_parse(e){const s=le.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==F.string&&n.parsedType!==F.number){const r=le.objectValues(s);return D(n,{expected:le.joinValues(r),received:n.parsedType,code:A.invalid_type}),Y}if(this._cache||(this._cache=new Set(le.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const r=le.objectValues(s);return D(n,{received:n.data,code:A.invalid_enum_value,options:r}),Y}return tt(e.data)}get enum(){return this._def.values}}jc.create=(t,e)=>new jc({values:t,typeName:X.ZodNativeEnum,...ne(e)});class Pr extends oe{unwrap(){return this._def.type}_parse(e){const{ctx:s}=this._processInputParams(e);if(s.parsedType!==F.promise&&s.common.async===!1)return D(s,{code:A.invalid_type,expected:F.promise,received:s.parsedType}),Y;const n=s.parsedType===F.promise?s.data:Promise.resolve(s.data);return tt(n.then(r=>this._def.type.parseAsync(r,{path:s.path,errorMap:s.common.contextualErrorMap})))}}Pr.create=(t,e)=>new Pr({type:t,typeName:X.ZodPromise,...ne(e)});class nn extends oe{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===X.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:s,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:o=>{D(n,o),o.fatal?s.abort():s.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const o=r.transform(n.data,a);if(n.common.async)return Promise.resolve(o).then(async l=>{if(s.value==="aborted")return Y;const c=await this._def.schema._parseAsync({data:l,path:n.path,parent:n});return c.status==="aborted"?Y:c.status==="dirty"||s.value==="dirty"?yn(c.value):c});{if(s.value==="aborted")return Y;const l=this._def.schema._parseSync({data:o,path:n.path,parent:n});return l.status==="aborted"?Y:l.status==="dirty"||s.value==="dirty"?yn(l.value):l}}if(r.type==="refinement"){const o=l=>{const c=r.refinement(l,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return l};if(n.common.async===!1){const l=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return l.status==="aborted"?Y:(l.status==="dirty"&&s.dirty(),o(l.value),{status:s.value,value:l.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(l=>l.status==="aborted"?Y:(l.status==="dirty"&&s.dirty(),o(l.value).then(()=>({status:s.value,value:l.value}))))}if(r.type==="transform")if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!tn(o))return Y;const l=r.transform(o.value,a);if(l instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:s.value,value:l}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>tn(o)?Promise.resolve(r.transform(o.value,a)).then(l=>({status:s.value,value:l})):Y);le.assertNever(r)}}nn.create=(t,e,s)=>new nn({schema:t,typeName:X.ZodEffects,effect:e,...ne(s)});nn.createWithPreprocess=(t,e,s)=>new nn({schema:e,effect:{type:"preprocess",transform:t},typeName:X.ZodEffects,...ne(s)});class Gt extends oe{_parse(e){return this._getType(e)===F.undefined?tt(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Gt.create=(t,e)=>new Gt({innerType:t,typeName:X.ZodOptional,...ne(e)});class rn extends oe{_parse(e){return this._getType(e)===F.null?tt(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}rn.create=(t,e)=>new rn({innerType:t,typeName:X.ZodNullable,...ne(e)});class ea extends oe{_parse(e){const{ctx:s}=this._processInputParams(e);let n=s.data;return s.parsedType===F.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:s.path,parent:s})}removeDefault(){return this._def.innerType}}ea.create=(t,e)=>new ea({innerType:t,typeName:X.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ne(e)});class ta extends oe{_parse(e){const{ctx:s}=this._processInputParams(e),n={...s,common:{...s.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Cr(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new It(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new It(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}ta.create=(t,e)=>new ta({innerType:t,typeName:X.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ne(e)});class Nc extends oe{_parse(e){if(this._getType(e)!==F.nan){const n=this._getOrReturnCtx(e);return D(n,{code:A.invalid_type,expected:F.nan,received:n.parsedType}),Y}return{status:"valid",value:e.data}}}Nc.create=t=>new Nc({typeName:X.ZodNaN,...ne(t)});class A1 extends oe{_parse(e){const{ctx:s}=this._processInputParams(e),n=s.data;return this._def.type._parse({data:n,path:s.path,parent:s})}unwrap(){return this._def.type}}class ao extends oe{_parse(e){const{status:s,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?Y:a.status==="dirty"?(s.dirty(),yn(a.value)):this._def.out._parseAsync({data:a.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?Y:r.status==="dirty"?(s.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,s){return new ao({in:e,out:s,typeName:X.ZodPipeline})}}class sa extends oe{_parse(e){const s=this._def.innerType._parse(e),n=r=>(tn(r)&&(r.value=Object.freeze(r.value)),r);return Cr(s)?s.then(r=>n(r)):n(s)}unwrap(){return this._def.innerType}}sa.create=(t,e)=>new sa({innerType:t,typeName:X.ZodReadonly,...ne(e)});var X;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(X||(X={}));const Ms=qt.create;Jt.create;bt.create;const P1=Pe.create;Tr.create;Ar.create;Ss.create;sn.create;Pr.create;Gt.create;rn.create;function na(t,[e,s]){return Math.min(s,Math.max(e,t))}function _1(t){const e=m.useRef({value:t,previous:t});return m.useMemo(()=>(e.current.value!==t&&(e.current.previous=e.current.value,e.current.value=t),e.current.previous),[t])}var E1=[" ","Enter","ArrowUp","ArrowDown"],R1=[" ","Enter"],Cs="Select",[Jr,ei,M1]=Vc(Cs),[cn,$j]=On(Cs,[M1,Mr]),ti=Mr(),[I1,ts]=cn(Cs),[V1,D1]=cn(Cs),bf=t=>{const{__scopeSelect:e,children:s,open:n,defaultOpen:r,onOpenChange:a,value:o,defaultValue:l,onValueChange:c,dir:d,name:u,autoComplete:h,disabled:f,required:x,form:b}=t,p=ti(e),[g,j]=m.useState(null),[v,w]=m.useState(null),[T,C]=m.useState(!1),M=ca(d),[O,I]=ur({prop:n,defaultProp:r??!1,onChange:a,caller:Cs}),[_,$]=ur({prop:o,defaultProp:l,onChange:c,caller:Cs}),q=m.useRef(null),U=g?b||!!g.closest("form"):!0,[G,V]=m.useState(new Set),te=Array.from(G).map(se=>se.props.value).join(";");return i.jsx(zm,{...p,children:i.jsxs(I1,{required:x,scope:e,trigger:g,onTriggerChange:j,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:T,onValueNodeHasChildrenChange:C,contentId:Ir(),value:_,onValueChange:$,open:O,onOpenChange:I,dir:M,triggerPointerDownPosRef:q,disabled:f,children:[i.jsx(Jr.Provider,{scope:e,children:i.jsx(V1,{scope:t.__scopeSelect,onNativeOptionAdd:m.useCallback(se=>{V(Q=>new Set(Q).add(se))},[]),onNativeOptionRemove:m.useCallback(se=>{V(Q=>{const ce=new Set(Q);return ce.delete(se),ce})},[]),children:s})}),U?i.jsxs($f,{"aria-hidden":!0,required:x,tabIndex:-1,name:u,autoComplete:h,value:_,onChange:se=>$(se.target.value),disabled:f,form:b,children:[_===void 0?i.jsx("option",{value:""}):null,Array.from(G)]},te):null]})})};bf.displayName=Cs;var wf="SelectTrigger",jf=m.forwardRef((t,e)=>{const{__scopeSelect:s,disabled:n=!1,...r}=t,a=ti(s),o=ts(wf,s),l=o.disabled||n,c=_e(e,o.onTriggerChange),d=ei(s),u=m.useRef("touch"),[h,f,x]=Zf(p=>{const g=d().filter(w=>!w.disabled),j=g.find(w=>w.value===o.value),v=Kf(g,p,j);v!==void 0&&o.onValueChange(v.value)}),b=p=>{l||(o.onOpenChange(!0),x()),p&&(o.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return i.jsx(Fc,{asChild:!0,...a,children:i.jsx(ue.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Hf(o.value)?"":void 0,...r,ref:c,onClick:J(r.onClick,p=>{p.currentTarget.focus(),u.current!=="mouse"&&b(p)}),onPointerDown:J(r.onPointerDown,p=>{u.current=p.pointerType;const g=p.target;g.hasPointerCapture(p.pointerId)&&g.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(b(p),p.preventDefault())}),onKeyDown:J(r.onKeyDown,p=>{const g=h.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&f(p.key),!(g&&p.key===" ")&&E1.includes(p.key)&&(b(),p.preventDefault())})})})});jf.displayName=wf;var Nf="SelectValue",Sf=m.forwardRef((t,e)=>{const{__scopeSelect:s,className:n,style:r,children:a,placeholder:o="",...l}=t,c=ts(Nf,s),{onValueNodeHasChildrenChange:d}=c,u=a!==void 0,h=_e(e,c.onValueNodeChange);return wt(()=>{d(u)},[d,u]),i.jsx(ue.span,{...l,ref:h,style:{pointerEvents:"none"},children:Hf(c.value)?i.jsx(i.Fragment,{children:o}):a})});Sf.displayName=Nf;var L1="SelectIcon",Cf=m.forwardRef((t,e)=>{const{__scopeSelect:s,children:n,...r}=t;return i.jsx(ue.span,{"aria-hidden":!0,...r,ref:e,children:n||"▼"})});Cf.displayName=L1;var O1="SelectPortal",kf=t=>i.jsx(Dc,{asChild:!0,...t});kf.displayName=O1;var ks="SelectContent",Tf=m.forwardRef((t,e)=>{const s=ts(ks,t.__scopeSelect),[n,r]=m.useState();if(wt(()=>{r(new DocumentFragment)},[]),!s.open){const a=n;return a?Vr.createPortal(i.jsx(Af,{scope:t.__scopeSelect,children:i.jsx(Jr.Slot,{scope:t.__scopeSelect,children:i.jsx("div",{children:t.children})})}),a):null}return i.jsx(Pf,{...t,ref:e})});Tf.displayName=ks;var at=10,[Af,ss]=cn(ks),F1="SelectContentImpl",B1=Fm("SelectContent.RemoveScroll"),Pf=m.forwardRef((t,e)=>{const{__scopeSelect:s,position:n="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:a,onPointerDownOutside:o,side:l,sideOffset:c,align:d,alignOffset:u,arrowPadding:h,collisionBoundary:f,collisionPadding:x,sticky:b,hideWhenDetached:p,avoidCollisions:g,...j}=t,v=ts(ks,s),[w,T]=m.useState(null),[C,M]=m.useState(null),O=_e(e,K=>T(K)),[I,_]=m.useState(null),[$,q]=m.useState(null),U=ei(s),[G,V]=m.useState(!1),te=m.useRef(!1);m.useEffect(()=>{if(w)return Dm(w)},[w]),Lm();const se=m.useCallback(K=>{const[ve,...Re]=U().map(pe=>pe.ref.current),[ge]=Re.slice(-1),xe=document.activeElement;for(const pe of K)if(pe===xe||(pe==null||pe.scrollIntoView({block:"nearest"}),pe===ve&&C&&(C.scrollTop=0),pe===ge&&C&&(C.scrollTop=C.scrollHeight),pe==null||pe.focus(),document.activeElement!==xe))return},[U,C]),Q=m.useCallback(()=>se([I,w]),[se,I,w]);m.useEffect(()=>{G&&Q()},[G,Q]);const{onOpenChange:ce,triggerPointerDownPosRef:we}=v;m.useEffect(()=>{if(w){let K={x:0,y:0};const ve=ge=>{var xe,pe;K={x:Math.abs(Math.round(ge.pageX)-(((xe=we.current)==null?void 0:xe.x)??0)),y:Math.abs(Math.round(ge.pageY)-(((pe=we.current)==null?void 0:pe.y)??0))}},Re=ge=>{K.x<=10&&K.y<=10?ge.preventDefault():w.contains(ge.target)||ce(!1),document.removeEventListener("pointermove",ve),we.current=null};return we.current!==null&&(document.addEventListener("pointermove",ve),document.addEventListener("pointerup",Re,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ve),document.removeEventListener("pointerup",Re,{capture:!0})}}},[w,ce,we]),m.useEffect(()=>{const K=()=>ce(!1);return window.addEventListener("blur",K),window.addEventListener("resize",K),()=>{window.removeEventListener("blur",K),window.removeEventListener("resize",K)}},[ce]);const[Ue,Nt]=Zf(K=>{const ve=U().filter(xe=>!xe.disabled),Re=ve.find(xe=>xe.ref.current===document.activeElement),ge=Kf(ve,K,Re);ge&&setTimeout(()=>ge.ref.current.focus())}),St=m.useCallback((K,ve,Re)=>{const ge=!te.current&&!Re;(v.value!==void 0&&v.value===ve||ge)&&(_(K),ge&&(te.current=!0))},[v.value]),nt=m.useCallback(()=>w==null?void 0:w.focus(),[w]),rt=m.useCallback((K,ve,Re)=>{const ge=!te.current&&!Re;(v.value!==void 0&&v.value===ve||ge)&&q(K)},[v.value]),ns=n==="popper"?ra:_f,rs=ns===ra?{side:l,sideOffset:c,align:d,alignOffset:u,arrowPadding:h,collisionBoundary:f,collisionPadding:x,sticky:b,hideWhenDetached:p,avoidCollisions:g}:{};return i.jsx(Af,{scope:s,content:w,viewport:C,onViewportChange:M,itemRefCallback:St,selectedItem:I,onItemLeave:nt,itemTextRefCallback:rt,focusSelectedItem:Q,selectedItemText:$,position:n,isPositioned:G,searchRef:Ue,children:i.jsx(Om,{as:B1,allowPinchZoom:!0,children:i.jsx(Bm,{asChild:!0,trapped:v.open,onMountAutoFocus:K=>{K.preventDefault()},onUnmountAutoFocus:J(r,K=>{var ve;(ve=v.trigger)==null||ve.focus({preventScroll:!0}),K.preventDefault()}),children:i.jsx(Lc,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:o,onFocusOutside:K=>K.preventDefault(),onDismiss:()=>v.onOpenChange(!1),children:i.jsx(ns,{role:"listbox",id:v.contentId,"data-state":v.open?"open":"closed",dir:v.dir,onContextMenu:K=>K.preventDefault(),...j,...rs,onPlaced:()=>V(!0),ref:O,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:J(j.onKeyDown,K=>{const ve=K.ctrlKey||K.altKey||K.metaKey;if(K.key==="Tab"&&K.preventDefault(),!ve&&K.key.length===1&&Nt(K.key),["ArrowUp","ArrowDown","Home","End"].includes(K.key)){let ge=U().filter(xe=>!xe.disabled).map(xe=>xe.ref.current);if(["ArrowUp","End"].includes(K.key)&&(ge=ge.slice().reverse()),["ArrowUp","ArrowDown"].includes(K.key)){const xe=K.target,pe=ge.indexOf(xe);ge=ge.slice(pe+1)}setTimeout(()=>se(ge)),K.preventDefault()}})})})})})})});Pf.displayName=F1;var z1="SelectItemAlignedPosition",_f=m.forwardRef((t,e)=>{const{__scopeSelect:s,onPlaced:n,...r}=t,a=ts(ks,s),o=ss(ks,s),[l,c]=m.useState(null),[d,u]=m.useState(null),h=_e(e,O=>u(O)),f=ei(s),x=m.useRef(!1),b=m.useRef(!0),{viewport:p,selectedItem:g,selectedItemText:j,focusSelectedItem:v}=o,w=m.useCallback(()=>{if(a.trigger&&a.valueNode&&l&&d&&p&&g&&j){const O=a.trigger.getBoundingClientRect(),I=d.getBoundingClientRect(),_=a.valueNode.getBoundingClientRect(),$=j.getBoundingClientRect();if(a.dir!=="rtl"){const xe=$.left-I.left,pe=_.left-xe,Ct=O.left-pe,ht=O.width+Ct,Ps=Math.max(ht,I.width),dn=window.innerWidth-at,un=na(pe,[at,Math.max(at,dn-Ps)]);l.style.minWidth=ht+"px",l.style.left=un+"px"}else{const xe=I.right-$.right,pe=window.innerWidth-_.right-xe,Ct=window.innerWidth-O.right-pe,ht=O.width+Ct,Ps=Math.max(ht,I.width),dn=window.innerWidth-at,un=na(pe,[at,Math.max(at,dn-Ps)]);l.style.minWidth=ht+"px",l.style.right=un+"px"}const q=f(),U=window.innerHeight-at*2,G=p.scrollHeight,V=window.getComputedStyle(d),te=parseInt(V.borderTopWidth,10),se=parseInt(V.paddingTop,10),Q=parseInt(V.borderBottomWidth,10),ce=parseInt(V.paddingBottom,10),we=te+se+G+ce+Q,Ue=Math.min(g.offsetHeight*5,we),Nt=window.getComputedStyle(p),St=parseInt(Nt.paddingTop,10),nt=parseInt(Nt.paddingBottom,10),rt=O.top+O.height/2-at,ns=U-rt,rs=g.offsetHeight/2,K=g.offsetTop+rs,ve=te+se+K,Re=we-ve;if(ve<=rt){const xe=q.length>0&&g===q[q.length-1].ref.current;l.style.bottom="0px";const pe=d.clientHeight-p.offsetTop-p.offsetHeight,Ct=Math.max(ns,rs+(xe?nt:0)+pe+Q),ht=ve+Ct;l.style.height=ht+"px"}else{const xe=q.length>0&&g===q[0].ref.current;l.style.top="0px";const Ct=Math.max(rt,te+p.offsetTop+(xe?St:0)+rs)+Re;l.style.height=Ct+"px",p.scrollTop=ve-rt+p.offsetTop}l.style.margin=`${at}px 0`,l.style.minHeight=Ue+"px",l.style.maxHeight=U+"px",n==null||n(),requestAnimationFrame(()=>x.current=!0)}},[f,a.trigger,a.valueNode,l,d,p,g,j,a.dir,n]);wt(()=>w(),[w]);const[T,C]=m.useState();wt(()=>{d&&C(window.getComputedStyle(d).zIndex)},[d]);const M=m.useCallback(O=>{O&&b.current===!0&&(w(),v==null||v(),b.current=!1)},[w,v]);return i.jsx(W1,{scope:s,contentWrapper:l,shouldExpandOnScrollRef:x,onScrollButtonChange:M,children:i.jsx("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:i.jsx(ue.div,{...r,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});_f.displayName=z1;var U1="SelectPopperPosition",ra=m.forwardRef((t,e)=>{const{__scopeSelect:s,align:n="start",collisionPadding:r=at,...a}=t,o=ti(s);return i.jsx(Oc,{...o,...a,ref:e,align:n,collisionPadding:r,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ra.displayName=U1;var[W1,oo]=cn(ks,{}),ia="SelectViewport",Ef=m.forwardRef((t,e)=>{const{__scopeSelect:s,nonce:n,...r}=t,a=ss(ia,s),o=oo(ia,s),l=_e(e,a.onViewportChange),c=m.useRef(0);return i.jsxs(i.Fragment,{children:[i.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),i.jsx(Jr.Slot,{scope:s,children:i.jsx(ue.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:J(r.onScroll,d=>{const u=d.currentTarget,{contentWrapper:h,shouldExpandOnScrollRef:f}=o;if(f!=null&&f.current&&h){const x=Math.abs(c.current-u.scrollTop);if(x>0){const b=window.innerHeight-at*2,p=parseFloat(h.style.minHeight),g=parseFloat(h.style.height),j=Math.max(p,g);if(j<b){const v=j+x,w=Math.min(b,v),T=v-w;h.style.height=w+"px",h.style.bottom==="0px"&&(u.scrollTop=T>0?T:0,h.style.justifyContent="flex-end")}}}c.current=u.scrollTop})})})]})});Ef.displayName=ia;var Rf="SelectGroup",[$1,H1]=cn(Rf),Z1=m.forwardRef((t,e)=>{const{__scopeSelect:s,...n}=t,r=Ir();return i.jsx($1,{scope:s,id:r,children:i.jsx(ue.div,{role:"group","aria-labelledby":r,...n,ref:e})})});Z1.displayName=Rf;var Mf="SelectLabel",If=m.forwardRef((t,e)=>{const{__scopeSelect:s,...n}=t,r=H1(Mf,s);return i.jsx(ue.div,{id:r.id,...n,ref:e})});If.displayName=Mf;var _r="SelectItem",[K1,Vf]=cn(_r),Df=m.forwardRef((t,e)=>{const{__scopeSelect:s,value:n,disabled:r=!1,textValue:a,...o}=t,l=ts(_r,s),c=ss(_r,s),d=l.value===n,[u,h]=m.useState(a??""),[f,x]=m.useState(!1),b=_e(e,v=>{var w;return(w=c.itemRefCallback)==null?void 0:w.call(c,v,n,r)}),p=Ir(),g=m.useRef("touch"),j=()=>{r||(l.onValueChange(n),l.onOpenChange(!1))};if(n==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return i.jsx(K1,{scope:s,value:n,disabled:r,textId:p,isSelected:d,onItemTextChange:m.useCallback(v=>{h(w=>w||((v==null?void 0:v.textContent)??"").trim())},[]),children:i.jsx(Jr.ItemSlot,{scope:s,value:n,disabled:r,textValue:u,children:i.jsx(ue.div,{role:"option","aria-labelledby":p,"data-highlighted":f?"":void 0,"aria-selected":d&&f,"data-state":d?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...o,ref:b,onFocus:J(o.onFocus,()=>x(!0)),onBlur:J(o.onBlur,()=>x(!1)),onClick:J(o.onClick,()=>{g.current!=="mouse"&&j()}),onPointerUp:J(o.onPointerUp,()=>{g.current==="mouse"&&j()}),onPointerDown:J(o.onPointerDown,v=>{g.current=v.pointerType}),onPointerMove:J(o.onPointerMove,v=>{var w;g.current=v.pointerType,r?(w=c.onItemLeave)==null||w.call(c):g.current==="mouse"&&v.currentTarget.focus({preventScroll:!0})}),onPointerLeave:J(o.onPointerLeave,v=>{var w;v.currentTarget===document.activeElement&&((w=c.onItemLeave)==null||w.call(c))}),onKeyDown:J(o.onKeyDown,v=>{var T;((T=c.searchRef)==null?void 0:T.current)!==""&&v.key===" "||(R1.includes(v.key)&&j(),v.key===" "&&v.preventDefault())})})})})});Df.displayName=_r;var vn="SelectItemText",Lf=m.forwardRef((t,e)=>{const{__scopeSelect:s,className:n,style:r,...a}=t,o=ts(vn,s),l=ss(vn,s),c=Vf(vn,s),d=D1(vn,s),[u,h]=m.useState(null),f=_e(e,j=>h(j),c.onItemTextChange,j=>{var v;return(v=l.itemTextRefCallback)==null?void 0:v.call(l,j,c.value,c.disabled)}),x=u==null?void 0:u.textContent,b=m.useMemo(()=>i.jsx("option",{value:c.value,disabled:c.disabled,children:x},c.value),[c.disabled,c.value,x]),{onNativeOptionAdd:p,onNativeOptionRemove:g}=d;return wt(()=>(p(b),()=>g(b)),[p,g,b]),i.jsxs(i.Fragment,{children:[i.jsx(ue.span,{id:c.textId,...a,ref:f}),c.isSelected&&o.valueNode&&!o.valueNodeHasChildren?Vr.createPortal(a.children,o.valueNode):null]})});Lf.displayName=vn;var Of="SelectItemIndicator",Ff=m.forwardRef((t,e)=>{const{__scopeSelect:s,...n}=t;return Vf(Of,s).isSelected?i.jsx(ue.span,{"aria-hidden":!0,...n,ref:e}):null});Ff.displayName=Of;var aa="SelectScrollUpButton",Bf=m.forwardRef((t,e)=>{const s=ss(aa,t.__scopeSelect),n=oo(aa,t.__scopeSelect),[r,a]=m.useState(!1),o=_e(e,n.onScrollButtonChange);return wt(()=>{if(s.viewport&&s.isPositioned){let l=function(){const d=c.scrollTop>0;a(d)};const c=s.viewport;return l(),c.addEventListener("scroll",l),()=>c.removeEventListener("scroll",l)}},[s.viewport,s.isPositioned]),r?i.jsx(Uf,{...t,ref:o,onAutoScroll:()=>{const{viewport:l,selectedItem:c}=s;l&&c&&(l.scrollTop=l.scrollTop-c.offsetHeight)}}):null});Bf.displayName=aa;var oa="SelectScrollDownButton",zf=m.forwardRef((t,e)=>{const s=ss(oa,t.__scopeSelect),n=oo(oa,t.__scopeSelect),[r,a]=m.useState(!1),o=_e(e,n.onScrollButtonChange);return wt(()=>{if(s.viewport&&s.isPositioned){let l=function(){const d=c.scrollHeight-c.clientHeight,u=Math.ceil(c.scrollTop)<d;a(u)};const c=s.viewport;return l(),c.addEventListener("scroll",l),()=>c.removeEventListener("scroll",l)}},[s.viewport,s.isPositioned]),r?i.jsx(Uf,{...t,ref:o,onAutoScroll:()=>{const{viewport:l,selectedItem:c}=s;l&&c&&(l.scrollTop=l.scrollTop+c.offsetHeight)}}):null});zf.displayName=oa;var Uf=m.forwardRef((t,e)=>{const{__scopeSelect:s,onAutoScroll:n,...r}=t,a=ss("SelectScrollButton",s),o=m.useRef(null),l=ei(s),c=m.useCallback(()=>{o.current!==null&&(window.clearInterval(o.current),o.current=null)},[]);return m.useEffect(()=>()=>c(),[c]),wt(()=>{var u;const d=l().find(h=>h.ref.current===document.activeElement);(u=d==null?void 0:d.ref.current)==null||u.scrollIntoView({block:"nearest"})},[l]),i.jsx(ue.div,{"aria-hidden":!0,...r,ref:e,style:{flexShrink:0,...r.style},onPointerDown:J(r.onPointerDown,()=>{o.current===null&&(o.current=window.setInterval(n,50))}),onPointerMove:J(r.onPointerMove,()=>{var d;(d=a.onItemLeave)==null||d.call(a),o.current===null&&(o.current=window.setInterval(n,50))}),onPointerLeave:J(r.onPointerLeave,()=>{c()})})}),q1="SelectSeparator",Wf=m.forwardRef((t,e)=>{const{__scopeSelect:s,...n}=t;return i.jsx(ue.div,{"aria-hidden":!0,...n,ref:e})});Wf.displayName=q1;var la="SelectArrow",G1=m.forwardRef((t,e)=>{const{__scopeSelect:s,...n}=t,r=ti(s),a=ts(la,s),o=ss(la,s);return a.open&&o.position==="popper"?i.jsx(Bc,{...r,...n,ref:e}):null});G1.displayName=la;var Y1="SelectBubbleInput",$f=m.forwardRef(({__scopeSelect:t,value:e,...s},n)=>{const r=m.useRef(null),a=_e(n,r),o=_1(e);return m.useEffect(()=>{const l=r.current;if(!l)return;const c=window.HTMLSelectElement.prototype,u=Object.getOwnPropertyDescriptor(c,"value").set;if(o!==e&&u){const h=new Event("change",{bubbles:!0});u.call(l,e),l.dispatchEvent(h)}},[o,e]),i.jsx(ue.select,{...s,style:{...yd,...s.style},ref:a,defaultValue:e})});$f.displayName=Y1;function Hf(t){return t===""||t===void 0}function Zf(t){const e=et(t),s=m.useRef(""),n=m.useRef(0),r=m.useCallback(o=>{const l=s.current+o;e(l),function c(d){s.current=d,window.clearTimeout(n.current),d!==""&&(n.current=window.setTimeout(()=>c(""),1e3))}(l)},[e]),a=m.useCallback(()=>{s.current="",window.clearTimeout(n.current)},[]);return m.useEffect(()=>()=>window.clearTimeout(n.current),[]),[s,r,a]}function Kf(t,e,s){const r=e.length>1&&Array.from(e).every(d=>d===e[0])?e[0]:e,a=s?t.indexOf(s):-1;let o=X1(t,Math.max(a,0));r.length===1&&(o=o.filter(d=>d!==s));const c=o.find(d=>d.textValue.toLowerCase().startsWith(r.toLowerCase()));return c!==s?c:void 0}function X1(t,e){return t.map((s,n)=>t[(e+n)%t.length])}var Q1=bf,qf=jf,J1=Sf,ej=Cf,tj=kf,Gf=Tf,sj=Ef,Yf=If,Xf=Df,nj=Lf,rj=Ff,Qf=Bf,Jf=zf,em=Wf;const ij=Q1,aj=J1,tm=m.forwardRef(({className:t,children:e,...s},n)=>i.jsxs(qf,{ref:n,className:ee("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...s,children:[e,i.jsx(ej,{asChild:!0,children:i.jsx(Fd,{className:"h-4 w-4 opacity-50"})})]}));tm.displayName=qf.displayName;const sm=m.forwardRef(({className:t,...e},s)=>i.jsx(Qf,{ref:s,className:ee("flex cursor-default items-center justify-center py-1",t),...e,children:i.jsx(Vg,{className:"h-4 w-4"})}));sm.displayName=Qf.displayName;const nm=m.forwardRef(({className:t,...e},s)=>i.jsx(Jf,{ref:s,className:ee("flex cursor-default items-center justify-center py-1",t),...e,children:i.jsx(Fd,{className:"h-4 w-4"})}));nm.displayName=Jf.displayName;const rm=m.forwardRef(({className:t,children:e,position:s="popper",...n},r)=>i.jsx(tj,{children:i.jsxs(Gf,{ref:r,className:ee("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]",s==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,...n,children:[i.jsx(sm,{}),i.jsx(sj,{className:ee("p-1",s==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),i.jsx(nm,{})]})}));rm.displayName=Gf.displayName;const oj=m.forwardRef(({className:t,...e},s)=>i.jsx(Yf,{ref:s,className:ee("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...e}));oj.displayName=Yf.displayName;const ft=m.forwardRef(({className:t,children:e,...s},n)=>i.jsxs(Xf,{ref:n,className:ee("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...s,children:[i.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:i.jsx(rj,{children:i.jsx(Ig,{className:"h-4 w-4"})})}),i.jsx(nj,{children:e})]}));ft.displayName=Xf.displayName;const lj=m.forwardRef(({className:t,...e},s)=>i.jsx(em,{ref:s,className:ee("-mx-1 my-1 h-px bg-muted",t),...e}));lj.displayName=em.displayName;const cj=P1({name:Ms().min(2,{message:"Name must be at least 2 characters."}),email:Ms().email({message:"Please enter a valid email address."}),phone:Ms().min(10,{message:"Please enter a valid phone number."}).optional(),company:Ms().optional(),subject:Ms().min(1,{message:"Please select a subject."}),message:Ms().min(10,{message:"Message must be at least 10 characters."})});function dj(){const{toast:t}=xd(),e=qw({resolver:s1(cj),defaultValues:{name:"",email:"",phone:"",company:"",subject:"",message:""}}),s=xp({mutationFn:r=>new Promise(a=>{setTimeout(()=>a({success:!0}),1e3)}),onSuccess:()=>{t({title:"Message sent!",description:"We'll get back to you as soon as possible."}),e.reset()},onError:()=>{t({title:"Something went wrong",description:"Your message couldn't be sent. Please try again later.",variant:"destructive"})}}),n=r=>{s.mutate(r)};return i.jsxs("div",{children:[i.jsx("section",{className:"bg-slate-50 dark:bg-slate-900 py-16 md:py-24",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"text-center max-w-3xl mx-auto",children:[i.jsx(z.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-4xl md:text-5xl font-bold mb-6",children:"Get in Touch"}),i.jsxs(z.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"text-lg text-muted-foreground mb-8",children:["Have questions about our services or want to discuss how ",i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," can help your business? Reach out to our team."]})]})})}),i.jsx("section",{className:"py-16",children:i.jsx("div",{className:"container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"grid lg:grid-cols-2 gap-12",children:[i.jsxs(z.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},children:[i.jsx("h2",{className:"text-3xl font-bold mb-6",children:"Send Us a Message"}),i.jsx("p",{className:"text-muted-foreground mb-8",children:"Fill out the form below and our team will get back to you as soon as possible."}),i.jsx(Xw,{...e,children:i.jsxs("form",{onSubmit:e.handleSubmit(n),className:"space-y-6",children:[i.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[i.jsx(Rs,{control:e.control,name:"name",render:({field:r})=>i.jsxs(ds,{children:[i.jsx(us,{children:"Name*"}),i.jsx(hs,{children:i.jsx(Bs,{placeholder:"Your name",...r})}),i.jsx(fs,{})]})}),i.jsx(Rs,{control:e.control,name:"email",render:({field:r})=>i.jsxs(ds,{children:[i.jsx(us,{children:"Email*"}),i.jsx(hs,{children:i.jsx(Bs,{placeholder:"Your email",...r})}),i.jsx(fs,{})]})})]}),i.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[i.jsx(Rs,{control:e.control,name:"phone",render:({field:r})=>i.jsxs(ds,{children:[i.jsx(us,{children:"Phone"}),i.jsx(hs,{children:i.jsx(Bs,{placeholder:"Your phone number",...r})}),i.jsx(fs,{})]})}),i.jsx(Rs,{control:e.control,name:"company",render:({field:r})=>i.jsxs(ds,{children:[i.jsx(us,{children:"Company"}),i.jsx(hs,{children:i.jsx(Bs,{placeholder:"Your company",...r})}),i.jsx(fs,{})]})})]}),i.jsx(Rs,{control:e.control,name:"subject",render:({field:r})=>i.jsxs(ds,{children:[i.jsx(us,{children:"Subject*"}),i.jsxs(ij,{onValueChange:r.onChange,defaultValue:r.value,children:[i.jsx(hs,{children:i.jsx(tm,{children:i.jsx(aj,{placeholder:"Select a subject"})})}),i.jsxs(rm,{children:[i.jsx(ft,{value:"ai-solutions",children:"AI Solutions"}),i.jsx(ft,{value:"ecommerce",children:"Ecommerce"}),i.jsx(ft,{value:"digital-marketing",children:"Digital Marketing"}),i.jsx(ft,{value:"leads-generation",children:"Leads Generation"}),i.jsx(ft,{value:"business-automation",children:"Business Automation"}),i.jsx(ft,{value:"business-process",children:"Business Process Management"}),i.jsx(ft,{value:"order-fulfillment",children:"Order Fulfillment"}),i.jsx(ft,{value:"digital-signage",children:"Digital Signage"}),i.jsx(ft,{value:"general-inquiry",children:"General Inquiry"})]})]}),i.jsx(fs,{})]})}),i.jsx(Rs,{control:e.control,name:"message",render:({field:r})=>i.jsxs(ds,{children:[i.jsx(us,{children:"Message*"}),i.jsx(hs,{children:i.jsx(pf,{placeholder:"Let us know what you're looking for",className:"min-h-[150px]",...r})}),i.jsx(fs,{})]})}),i.jsx(me,{type:"submit",className:"w-full sm:w-auto",disabled:s.isPending,children:s.isPending?i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"animate-spin mr-2 h-4 w-4 border-2 border-white border-opacity-50 border-t-transparent rounded-full"}),"Sending..."]}):i.jsxs("div",{className:"flex items-center",children:[i.jsx(zd,{className:"mr-2 h-4 w-4"}),"Send Message"]})})]})})]}),i.jsxs(z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.5},children:[i.jsx("h2",{className:"text-3xl font-bold mb-6",children:"Contact Information"}),i.jsx("p",{className:"text-muted-foreground mb-8",children:"You can also reach us through these channels:"}),i.jsxs("div",{className:"space-y-6 mb-12",children:[i.jsxs("div",{className:"flex items-start",style:{display:"none"},children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("div",{className:"bg-primary/10 p-2 rounded-full",children:i.jsx(Fg,{className:"h-5 w-5 text-primary"})})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-lg font-semibold mb-1",children:"Address"}),i.jsxs("p",{className:"text-muted-foreground",children:["123 Business Avenue, ",i.jsx("br",{}),"Suite 456, ",i.jsx("br",{}),"New York, NY 10001"]})]})]}),i.jsxs("div",{className:"flex items-start",children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("div",{className:"bg-primary/10 p-2 rounded-full",children:i.jsx(Og,{className:"h-5 w-5 text-primary"})})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-lg font-semibold mb-1",children:"Email"}),i.jsxs("p",{className:"text-muted-foreground",children:["<EMAIL> ",i.jsx("br",{}),"<EMAIL>"]})]})]}),i.jsxs("div",{className:"flex items-start",style:{display:"none"},children:[i.jsx("div",{className:"mr-4 mt-1",children:i.jsx("div",{className:"bg-primary/10 p-2 rounded-full",children:i.jsx(Hg,{className:"h-5 w-5 text-primary"})})}),i.jsxs("div",{children:[i.jsx("h3",{className:"text-lg font-semibold mb-1",children:"Phone"}),i.jsxs("p",{className:"text-muted-foreground",children:["+**************** ",i.jsx("br",{}),"+****************"]})]})]})]}),i.jsxs("div",{style:{display:"none"},children:[i.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Business Hours"}),i.jsx("div",{className:"bg-slate-50 dark:bg-slate-800 rounded-lg p-6 border border-border",children:i.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"font-medium mb-2",children:"Monday - Friday"}),i.jsx("p",{className:"text-muted-foreground",children:"9:00 AM - 6:00 PM"})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"font-medium mb-2",children:"Saturday"}),i.jsx("p",{className:"text-muted-foreground",children:"10:00 AM - 4:00 PM"})]}),i.jsxs("div",{className:"col-span-2",children:[i.jsx("h4",{className:"font-medium mb-2",children:"Sunday"}),i.jsx("p",{className:"text-muted-foreground",children:"Closed"})]})]})})]})]})]})})})]})}const im=m.createContext(void 0),dr=()=>`msg_${Math.random().toString(36).substr(2,9)}`,Sc={id:dr(),role:"assistant",content:`Hi there! I'm the <span class="company-name">MP Advance Solutions</span> virtual assistant. How can I help with your business technology needs today?`,timestamp:new Date},uj=({children:t})=>{const[e,s]=m.useState([Sc]),[n,r]=m.useState(!1),[a,o]=m.useState(!1),l=async h=>{if(!h.trim())return;const f={id:dr(),role:"user",content:h,timestamp:new Date};s(x=>[...x,f]),r(!0);try{const x=e.filter(g=>g.role!=="system").map(g=>({role:g.role,content:g.content})),b=await yp("/api/chatbot",{method:"POST",data:{message:h,chatHistory:x}}),p={id:dr(),role:"assistant",content:b.response,timestamp:new Date,category:b.category};s(g=>[...g,p])}catch(x){console.error("Error sending message:",x);const b={id:dr(),role:"assistant",content:"Sorry, I encountered an error processing your request. Please try again later or contact our support team.",timestamp:new Date};s(p=>[...p,b])}finally{r(!1)}},c=()=>o(!0),d=()=>o(!1),u=()=>{s([Sc])};return m.useEffect(()=>{const h=localStorage.getItem("mp_advance_chat");if(h)try{const x=JSON.parse(h).map(b=>({...b,timestamp:new Date(b.timestamp)}));s(x)}catch(f){console.error("Error loading chat from storage:",f)}},[]),m.useEffect(()=>{e.length>0&&localStorage.setItem("mp_advance_chat",JSON.stringify(e))},[e]),i.jsx(im.Provider,{value:{messages:e,isLoading:n,isChatOpen:a,sendMessage:l,openChat:c,closeChat:d,resetChat:u},children:t})},am=()=>{const t=m.useContext(im);if(t===void 0)throw new Error("useChat must be used within a ChatProvider");return t},hj=()=>{const{isChatOpen:t,openChat:e,closeChat:s}=am();return i.jsxs(z.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:260,damping:20},className:"fixed bottom-6 right-6 z-50",children:[i.jsxs(me,{onClick:t?s:e,className:"h-14 w-14 rounded-full shadow-lg",size:"lg",children:[t?i.jsx(Un,{className:"h-6 w-6"}):i.jsx(zg,{className:"h-6 w-6"}),i.jsx("span",{className:"sr-only",children:t?"Close chat":"Open chat"})]}),!t&&i.jsxs(z.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"absolute top-0 right-16 bg-white dark:bg-slate-800 text-sm px-3 py-1.5 rounded-md shadow-sm border border-border whitespace-nowrap",children:["Chat with our AI assistant",i.jsx("div",{className:"absolute right-0 top-1/2 -translate-y-1/2 -mr-1.5 w-3 h-3 bg-white dark:bg-slate-800 border-t border-r border-border rotate-45"})]})]})};function fj(t,e){return m.useReducer((s,n)=>e[s][n]??s,t)}var lo="ScrollArea",[om,Hj]=On(lo),[mj,st]=om(lo),lm=m.forwardRef((t,e)=>{const{__scopeScrollArea:s,type:n="hover",dir:r,scrollHideDelay:a=600,...o}=t,[l,c]=m.useState(null),[d,u]=m.useState(null),[h,f]=m.useState(null),[x,b]=m.useState(null),[p,g]=m.useState(null),[j,v]=m.useState(0),[w,T]=m.useState(0),[C,M]=m.useState(!1),[O,I]=m.useState(!1),_=_e(e,q=>c(q)),$=ca(r);return i.jsx(mj,{scope:s,type:n,dir:$,scrollHideDelay:a,scrollArea:l,viewport:d,onViewportChange:u,content:h,onContentChange:f,scrollbarX:x,onScrollbarXChange:b,scrollbarXEnabled:C,onScrollbarXEnabledChange:M,scrollbarY:p,onScrollbarYChange:g,scrollbarYEnabled:O,onScrollbarYEnabledChange:I,onCornerWidthChange:v,onCornerHeightChange:T,children:i.jsx(ue.div,{dir:$,...o,ref:_,style:{position:"relative","--radix-scroll-area-corner-width":j+"px","--radix-scroll-area-corner-height":w+"px",...t.style}})})});lm.displayName=lo;var cm="ScrollAreaViewport",dm=m.forwardRef((t,e)=>{const{__scopeScrollArea:s,children:n,nonce:r,...a}=t,o=st(cm,s),l=m.useRef(null),c=_e(e,l,o.onViewportChange);return i.jsxs(i.Fragment,{children:[i.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),i.jsx(ue.div,{"data-radix-scroll-area-viewport":"",...a,ref:c,style:{overflowX:o.scrollbarXEnabled?"scroll":"hidden",overflowY:o.scrollbarYEnabled?"scroll":"hidden",...t.style},children:i.jsx("div",{ref:o.onContentChange,style:{minWidth:"100%",display:"table"},children:n})})]})});dm.displayName=cm;var jt="ScrollAreaScrollbar",co=m.forwardRef((t,e)=>{const{forceMount:s,...n}=t,r=st(jt,t.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:o}=r,l=t.orientation==="horizontal";return m.useEffect(()=>(l?a(!0):o(!0),()=>{l?a(!1):o(!1)}),[l,a,o]),r.type==="hover"?i.jsx(pj,{...n,ref:e,forceMount:s}):r.type==="scroll"?i.jsx(gj,{...n,ref:e,forceMount:s}):r.type==="auto"?i.jsx(um,{...n,ref:e,forceMount:s}):r.type==="always"?i.jsx(uo,{...n,ref:e}):null});co.displayName=jt;var pj=m.forwardRef((t,e)=>{const{forceMount:s,...n}=t,r=st(jt,t.__scopeScrollArea),[a,o]=m.useState(!1);return m.useEffect(()=>{const l=r.scrollArea;let c=0;if(l){const d=()=>{window.clearTimeout(c),o(!0)},u=()=>{c=window.setTimeout(()=>o(!1),r.scrollHideDelay)};return l.addEventListener("pointerenter",d),l.addEventListener("pointerleave",u),()=>{window.clearTimeout(c),l.removeEventListener("pointerenter",d),l.removeEventListener("pointerleave",u)}}},[r.scrollArea,r.scrollHideDelay]),i.jsx(Ts,{present:s||a,children:i.jsx(um,{"data-state":a?"visible":"hidden",...n,ref:e})})}),gj=m.forwardRef((t,e)=>{const{forceMount:s,...n}=t,r=st(jt,t.__scopeScrollArea),a=t.orientation==="horizontal",o=ni(()=>c("SCROLL_END"),100),[l,c]=fj("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return m.useEffect(()=>{if(l==="idle"){const d=window.setTimeout(()=>c("HIDE"),r.scrollHideDelay);return()=>window.clearTimeout(d)}},[l,r.scrollHideDelay,c]),m.useEffect(()=>{const d=r.viewport,u=a?"scrollLeft":"scrollTop";if(d){let h=d[u];const f=()=>{const x=d[u];h!==x&&(c("SCROLL"),o()),h=x};return d.addEventListener("scroll",f),()=>d.removeEventListener("scroll",f)}},[r.viewport,a,c,o]),i.jsx(Ts,{present:s||l!=="hidden",children:i.jsx(uo,{"data-state":l==="hidden"?"hidden":"visible",...n,ref:e,onPointerEnter:J(t.onPointerEnter,()=>c("POINTER_ENTER")),onPointerLeave:J(t.onPointerLeave,()=>c("POINTER_LEAVE"))})})}),um=m.forwardRef((t,e)=>{const s=st(jt,t.__scopeScrollArea),{forceMount:n,...r}=t,[a,o]=m.useState(!1),l=t.orientation==="horizontal",c=ni(()=>{if(s.viewport){const d=s.viewport.offsetWidth<s.viewport.scrollWidth,u=s.viewport.offsetHeight<s.viewport.scrollHeight;o(l?d:u)}},10);return an(s.viewport,c),an(s.content,c),i.jsx(Ts,{present:n||a,children:i.jsx(uo,{"data-state":a?"visible":"hidden",...r,ref:e})})}),uo=m.forwardRef((t,e)=>{const{orientation:s="vertical",...n}=t,r=st(jt,t.__scopeScrollArea),a=m.useRef(null),o=m.useRef(0),[l,c]=m.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=gm(l.viewport,l.content),u={...n,sizes:l,onSizesChange:c,hasThumb:d>0&&d<1,onThumbChange:f=>a.current=f,onThumbPointerUp:()=>o.current=0,onThumbPointerDown:f=>o.current=f};function h(f,x){return jj(f,o.current,l,x)}return s==="horizontal"?i.jsx(xj,{...u,ref:e,onThumbPositionChange:()=>{if(r.viewport&&a.current){const f=r.viewport.scrollLeft,x=Cc(f,l,r.dir);a.current.style.transform=`translate3d(${x}px, 0, 0)`}},onWheelScroll:f=>{r.viewport&&(r.viewport.scrollLeft=f)},onDragScroll:f=>{r.viewport&&(r.viewport.scrollLeft=h(f,r.dir))}}):s==="vertical"?i.jsx(yj,{...u,ref:e,onThumbPositionChange:()=>{if(r.viewport&&a.current){const f=r.viewport.scrollTop,x=Cc(f,l);a.current.style.transform=`translate3d(0, ${x}px, 0)`}},onWheelScroll:f=>{r.viewport&&(r.viewport.scrollTop=f)},onDragScroll:f=>{r.viewport&&(r.viewport.scrollTop=h(f))}}):null}),xj=m.forwardRef((t,e)=>{const{sizes:s,onSizesChange:n,...r}=t,a=st(jt,t.__scopeScrollArea),[o,l]=m.useState(),c=m.useRef(null),d=_e(e,c,a.onScrollbarXChange);return m.useEffect(()=>{c.current&&l(getComputedStyle(c.current))},[c]),i.jsx(fm,{"data-orientation":"horizontal",...r,ref:d,sizes:s,style:{bottom:0,left:a.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:a.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":si(s)+"px",...t.style},onThumbPointerDown:u=>t.onThumbPointerDown(u.x),onDragScroll:u=>t.onDragScroll(u.x),onWheelScroll:(u,h)=>{if(a.viewport){const f=a.viewport.scrollLeft+u.deltaX;t.onWheelScroll(f),ym(f,h)&&u.preventDefault()}},onResize:()=>{c.current&&a.viewport&&o&&n({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:Rr(o.paddingLeft),paddingEnd:Rr(o.paddingRight)}})}})}),yj=m.forwardRef((t,e)=>{const{sizes:s,onSizesChange:n,...r}=t,a=st(jt,t.__scopeScrollArea),[o,l]=m.useState(),c=m.useRef(null),d=_e(e,c,a.onScrollbarYChange);return m.useEffect(()=>{c.current&&l(getComputedStyle(c.current))},[c]),i.jsx(fm,{"data-orientation":"vertical",...r,ref:d,sizes:s,style:{top:0,right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":si(s)+"px",...t.style},onThumbPointerDown:u=>t.onThumbPointerDown(u.y),onDragScroll:u=>t.onDragScroll(u.y),onWheelScroll:(u,h)=>{if(a.viewport){const f=a.viewport.scrollTop+u.deltaY;t.onWheelScroll(f),ym(f,h)&&u.preventDefault()}},onResize:()=>{c.current&&a.viewport&&o&&n({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:Rr(o.paddingTop),paddingEnd:Rr(o.paddingBottom)}})}})}),[vj,hm]=om(jt),fm=m.forwardRef((t,e)=>{const{__scopeScrollArea:s,sizes:n,hasThumb:r,onThumbChange:a,onThumbPointerUp:o,onThumbPointerDown:l,onThumbPositionChange:c,onDragScroll:d,onWheelScroll:u,onResize:h,...f}=t,x=st(jt,s),[b,p]=m.useState(null),g=_e(e,_=>p(_)),j=m.useRef(null),v=m.useRef(""),w=x.viewport,T=n.content-n.viewport,C=et(u),M=et(c),O=ni(h,10);function I(_){if(j.current){const $=_.clientX-j.current.left,q=_.clientY-j.current.top;d({x:$,y:q})}}return m.useEffect(()=>{const _=$=>{const q=$.target;(b==null?void 0:b.contains(q))&&C($,T)};return document.addEventListener("wheel",_,{passive:!1}),()=>document.removeEventListener("wheel",_,{passive:!1})},[w,b,T,C]),m.useEffect(M,[n,M]),an(b,O),an(x.content,O),i.jsx(vj,{scope:s,scrollbar:b,hasThumb:r,onThumbChange:et(a),onThumbPointerUp:et(o),onThumbPositionChange:M,onThumbPointerDown:et(l),children:i.jsx(ue.div,{...f,ref:g,style:{position:"absolute",...f.style},onPointerDown:J(t.onPointerDown,_=>{_.button===0&&(_.target.setPointerCapture(_.pointerId),j.current=b.getBoundingClientRect(),v.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),I(_))}),onPointerMove:J(t.onPointerMove,I),onPointerUp:J(t.onPointerUp,_=>{const $=_.target;$.hasPointerCapture(_.pointerId)&&$.releasePointerCapture(_.pointerId),document.body.style.webkitUserSelect=v.current,x.viewport&&(x.viewport.style.scrollBehavior=""),j.current=null})})})}),Er="ScrollAreaThumb",mm=m.forwardRef((t,e)=>{const{forceMount:s,...n}=t,r=hm(Er,t.__scopeScrollArea);return i.jsx(Ts,{present:s||r.hasThumb,children:i.jsx(bj,{ref:e,...n})})}),bj=m.forwardRef((t,e)=>{const{__scopeScrollArea:s,style:n,...r}=t,a=st(Er,s),o=hm(Er,s),{onThumbPositionChange:l}=o,c=_e(e,h=>o.onThumbChange(h)),d=m.useRef(void 0),u=ni(()=>{d.current&&(d.current(),d.current=void 0)},100);return m.useEffect(()=>{const h=a.viewport;if(h){const f=()=>{if(u(),!d.current){const x=Nj(h,l);d.current=x,l()}};return l(),h.addEventListener("scroll",f),()=>h.removeEventListener("scroll",f)}},[a.viewport,u,l]),i.jsx(ue.div,{"data-state":o.hasThumb?"visible":"hidden",...r,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...n},onPointerDownCapture:J(t.onPointerDownCapture,h=>{const x=h.target.getBoundingClientRect(),b=h.clientX-x.left,p=h.clientY-x.top;o.onThumbPointerDown({x:b,y:p})}),onPointerUp:J(t.onPointerUp,o.onThumbPointerUp)})});mm.displayName=Er;var ho="ScrollAreaCorner",pm=m.forwardRef((t,e)=>{const s=st(ho,t.__scopeScrollArea),n=!!(s.scrollbarX&&s.scrollbarY);return s.type!=="scroll"&&n?i.jsx(wj,{...t,ref:e}):null});pm.displayName=ho;var wj=m.forwardRef((t,e)=>{const{__scopeScrollArea:s,...n}=t,r=st(ho,s),[a,o]=m.useState(0),[l,c]=m.useState(0),d=!!(a&&l);return an(r.scrollbarX,()=>{var h;const u=((h=r.scrollbarX)==null?void 0:h.offsetHeight)||0;r.onCornerHeightChange(u),c(u)}),an(r.scrollbarY,()=>{var h;const u=((h=r.scrollbarY)==null?void 0:h.offsetWidth)||0;r.onCornerWidthChange(u),o(u)}),d?i.jsx(ue.div,{...n,ref:e,style:{width:a,height:l,position:"absolute",right:r.dir==="ltr"?0:void 0,left:r.dir==="rtl"?0:void 0,bottom:0,...t.style}}):null});function Rr(t){return t?parseInt(t,10):0}function gm(t,e){const s=t/e;return isNaN(s)?0:s}function si(t){const e=gm(t.viewport,t.content),s=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,n=(t.scrollbar.size-s)*e;return Math.max(n,18)}function jj(t,e,s,n="ltr"){const r=si(s),a=r/2,o=e||a,l=r-o,c=s.scrollbar.paddingStart+o,d=s.scrollbar.size-s.scrollbar.paddingEnd-l,u=s.content-s.viewport,h=n==="ltr"?[0,u]:[u*-1,0];return xm([c,d],h)(t)}function Cc(t,e,s="ltr"){const n=si(e),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,a=e.scrollbar.size-r,o=e.content-e.viewport,l=a-n,c=s==="ltr"?[0,o]:[o*-1,0],d=na(t,c);return xm([0,o],[0,l])(d)}function xm(t,e){return s=>{if(t[0]===t[1]||e[0]===e[1])return e[0];const n=(e[1]-e[0])/(t[1]-t[0]);return e[0]+n*(s-t[0])}}function ym(t,e){return t>0&&t<e}var Nj=(t,e=()=>{})=>{let s={left:t.scrollLeft,top:t.scrollTop},n=0;return function r(){const a={left:t.scrollLeft,top:t.scrollTop},o=s.left!==a.left,l=s.top!==a.top;(o||l)&&e(),s=a,n=window.requestAnimationFrame(r)}(),()=>window.cancelAnimationFrame(n)};function ni(t,e){const s=et(t),n=m.useRef(0);return m.useEffect(()=>()=>window.clearTimeout(n.current),[]),m.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(s,e)},[s,e])}function an(t,e){const s=et(e);wt(()=>{let n=0;if(t){const r=new ResizeObserver(()=>{cancelAnimationFrame(n),n=window.requestAnimationFrame(s)});return r.observe(t),()=>{window.cancelAnimationFrame(n),r.unobserve(t)}}},[t,s])}var vm=lm,Sj=dm,Cj=pm;const bm=m.forwardRef(({className:t,children:e,...s},n)=>i.jsxs(vm,{ref:n,className:ee("relative overflow-hidden",t),...s,children:[i.jsx(Sj,{className:"h-full w-full rounded-[inherit]",children:e}),i.jsx(wm,{}),i.jsx(Cj,{})]}));bm.displayName=vm.displayName;const wm=m.forwardRef(({className:t,orientation:e="vertical",...s},n)=>i.jsx(co,{ref:n,orientation:e,className:ee("flex touch-none select-none transition-colors",e==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",e==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...s,children:i.jsx(mm,{className:"relative flex-1 rounded-full bg-border"})}));wm.displayName=co.displayName;const kj=t=>{let e=t.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>");return e=e.replace(/\*(.*?)\*/g,"<em>$1</em>"),e=e.replace(/\n/g,"<br />"),i.jsx("span",{dangerouslySetInnerHTML:{__html:e}})},Tj=()=>{const{messages:t,isLoading:e,isChatOpen:s,sendMessage:n,closeChat:r,resetChat:a}=am(),[o,l]=de.useState(""),c=m.useRef(null),d=m.useRef(null);m.useEffect(()=>{var f;(f=c.current)==null||f.scrollIntoView({behavior:"smooth"})},[t]),m.useEffect(()=>{var f;s&&((f=d.current)==null||f.focus())},[s]);const u=()=>{o.trim()&&!e&&(n(o),l(""))},h=f=>{f.key==="Enter"&&!f.shiftKey&&(f.preventDefault(),u())};return s?i.jsx(Ux,{children:i.jsxs(z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},className:"fixed bottom-20 right-6 w-96 h-[500px] bg-white dark:bg-slate-900 rounded-xl shadow-lg border border-border flex flex-col z-50",children:[i.jsxs("div",{className:"p-3 border-b border-border flex items-center justify-between bg-primary text-white rounded-t-xl",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(Mi,{className:"h-5 w-5"}),i.jsx("span",{className:"font-semibold",children:"MP Advance AI Assistant"})]}),i.jsxs("div",{className:"flex space-x-1",children:[i.jsx(me,{variant:"ghost",size:"icon",className:"h-8 w-8 text-white hover:bg-white/20",onClick:a,title:"Reset conversation",children:i.jsx(Zg,{className:"h-4 w-4"})}),i.jsx(me,{variant:"ghost",size:"icon",className:"h-8 w-8 text-white hover:bg-white/20",onClick:r,title:"Close chat",children:i.jsx(Un,{className:"h-4 w-4"})})]})]}),i.jsx(bm,{className:"flex-grow p-4",children:i.jsxs("div",{className:"space-y-4",children:[t.map(f=>i.jsx("div",{className:`flex ${f.role==="user"?"justify-end":"justify-start"}`,children:i.jsxs("div",{className:`
                  max-w-[80%] p-3 rounded-lg 
                  ${f.role==="user"?"bg-primary text-white rounded-tr-none":"bg-slate-100 dark:bg-slate-800 rounded-tl-none"}
                `,children:[i.jsx("div",{className:"flex items-center space-x-2 mb-1",children:f.role==="user"?i.jsxs(i.Fragment,{children:[i.jsx("span",{className:"text-xs opacity-70",children:"You"}),i.jsx(Jg,{className:"h-3 w-3 opacity-70"})]}):i.jsxs(i.Fragment,{children:[i.jsx(Mi,{className:"h-3 w-3 opacity-70"}),i.jsx("span",{className:"text-xs opacity-70",children:"MP Advance AI"})]})}),i.jsx("div",{className:"text-sm",children:kj(f.content)}),i.jsx("div",{className:"text-xs opacity-50 mt-1 text-right",children:sx(f.timestamp)})]})},f.id)),e&&i.jsx("div",{className:"flex justify-start",children:i.jsx("div",{className:"bg-slate-100 dark:bg-slate-800 p-3 rounded-lg rounded-tl-none max-w-[80%]",children:i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"h-2 w-2 bg-primary/50 rounded-full animate-pulse"}),i.jsx("div",{className:"h-2 w-2 bg-primary/50 rounded-full animate-pulse delay-150"}),i.jsx("div",{className:"h-2 w-2 bg-primary/50 rounded-full animate-pulse delay-300"}),i.jsx("span",{className:"text-xs opacity-50",children:"AI is typing"})]})})}),i.jsx("div",{ref:c})]})}),i.jsxs("div",{className:"p-3 border-t border-border",children:[i.jsxs("div",{className:"flex space-x-2",children:[i.jsx(Bs,{ref:d,placeholder:"Type your message...",value:o,onChange:f=>l(f.target.value),onKeyDown:h,disabled:e,className:"flex-grow"}),i.jsx(me,{onClick:u,disabled:e||!o.trim(),size:"icon",className:"flex-shrink-0",children:i.jsx(zd,{className:"h-4 w-4"})})]}),i.jsxs("div",{className:"mt-2 text-xs text-center text-muted-foreground",children:[i.jsx("span",{className:"company-name",children:"MP Advance Solutions"})," AI Assistant"]})]})]})}):null},Aj=()=>i.jsxs(uj,{children:[i.jsx(hj,{}),i.jsx(Tj,{})]});function Pj(){return i.jsxs(eg,{children:[i.jsx(_s,{path:"/",component:yw}),i.jsx(_s,{path:"/services",component:vw}),i.jsx(_s,{path:"/ai-solutions",component:Sw}),i.jsx(_s,{path:"/about",component:Tw}),i.jsx(_s,{path:"/contact",component:dj}),i.jsx(_s,{component:Tx})]})}function _j(){return i.jsxs(Sx,{children:[i.jsxs("div",{className:"flex flex-col min-h-screen",children:[i.jsx(Dx,{}),i.jsx("main",{className:"flex-grow",children:i.jsx(Pj,{})}),i.jsx(Lx,{}),i.jsx(Aj,{})," "]}),i.jsx(ax,{})]})}const jm=document.getElementById("root");if(!jm)throw new Error("Could not find root element with id 'root'");const Ej=qc(jm);Ej.render(i.jsx(m.StrictMode,{children:i.jsx(gp,{client:bp,children:i.jsx(jp,{defaultTheme:"system",storageKey:"mp-theme",children:i.jsx(_j,{})})})}));
