import{r as a,R as oe,a as qt,b as hr,c as Zt}from"./vendor-1XCZ5AD1.js";var Qt={exports:{}},je={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vr=a,gr=Symbol.for("react.element"),yr=Symbol.for("react.fragment"),wr=Object.prototype.hasOwnProperty,xr=vr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,br={key:!0,ref:!0,__self:!0,__source:!0};function Jt(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)wr.call(t,r)&&!br.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:gr,type:e,key:i,ref:s,props:o,_owner:xr.current}}je.Fragment=yr;je.jsx=Jt;je.jsxs=Jt;Qt.exports=je;var S=Qt.exports;function W(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Tt(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function en(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Tt(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Tt(e[o],null)}}}}function U(...e){return a.useCallback(en(...e),e)}function Er(e,t){const n=a.createContext(t),r=i=>{const{children:s,...c}=i,f=a.useMemo(()=>c,Object.values(c));return S.jsx(n.Provider,{value:f,children:s})};r.displayName=e+"Provider";function o(i){const s=a.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function $e(e,t=[]){let n=[];function r(i,s){const c=a.createContext(s),f=n.length;n=[...n,s];const l=d=>{var g;const{scope:h,children:p,...y}=d,m=((g=h==null?void 0:h[e])==null?void 0:g[f])||c,v=a.useMemo(()=>y,Object.values(y));return S.jsx(m.Provider,{value:v,children:p})};l.displayName=i+"Provider";function u(d,h){var m;const p=((m=h==null?void 0:h[e])==null?void 0:m[f])||c,y=a.useContext(p);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[l,u]}const o=()=>{const i=n.map(s=>a.createContext(s));return function(c){const f=(c==null?void 0:c[e])||i;return a.useMemo(()=>({[`__scope${e}`]:{...c,[e]:f}}),[c,f])}};return o.scopeName=e,[r,Cr(o,...t)]}function Cr(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((c,{useScope:f,scopeName:l})=>{const d=f(i)[`__scope${l}`];return{...c,...d}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function ge(e){const t=Sr(e),n=a.forwardRef((r,o)=>{const{children:i,...s}=r,c=a.Children.toArray(i),f=c.find(Rr);if(f){const l=f.props.children,u=c.map(d=>d===f?a.Children.count(l)>1?a.Children.only(null):a.isValidElement(l)?l.props.children:null:d);return S.jsx(t,{...s,ref:o,children:a.isValidElement(l)?a.cloneElement(l,void 0,u):null})}return S.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}var ys=ge("Slot");function Sr(e){const t=a.forwardRef((n,r)=>{const{children:o,...i}=n;if(a.isValidElement(o)){const s=Pr(o),c=Ar(i,o.props);return o.type!==a.Fragment&&(c.ref=r?en(r,s):s),a.cloneElement(o,c)}return a.Children.count(o)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var tn=Symbol("radix.slottable");function ws(e){const t=({children:n})=>S.jsx(S.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=tn,t}function Rr(e){return a.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===tn}function Ar(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...c)=>{const f=i(...c);return o(...c),f}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Pr(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Or(e){const t=e+"CollectionProvider",[n,r]=$e(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:v,children:g}=m,x=oe.useRef(null),w=oe.useRef(new Map).current;return S.jsx(o,{scope:v,itemMap:w,collectionRef:x,children:g})};s.displayName=t;const c=e+"CollectionSlot",f=ge(c),l=oe.forwardRef((m,v)=>{const{scope:g,children:x}=m,w=i(c,g),b=U(v,w.collectionRef);return S.jsx(f,{ref:b,children:x})});l.displayName=c;const u=e+"CollectionItemSlot",d="data-radix-collection-item",h=ge(u),p=oe.forwardRef((m,v)=>{const{scope:g,children:x,...w}=m,b=oe.useRef(null),E=U(v,b),R=i(u,g);return oe.useEffect(()=>(R.itemMap.set(b,{ref:b,...w}),()=>void R.itemMap.delete(b))),S.jsx(h,{[d]:"",ref:E,children:x})});p.displayName=u;function y(m){const v=i(e+"CollectionConsumer",m);return oe.useCallback(()=>{const x=v.collectionRef.current;if(!x)return[];const w=Array.from(x.querySelectorAll(`[${d}]`));return Array.from(v.itemMap.values()).sort((R,C)=>w.indexOf(R.ref.current)-w.indexOf(C.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:s,Slot:l,ItemSlot:p},y,r]}var Tr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],j=Tr.reduce((e,t)=>{const n=ge(`Primitive.${t}`),r=a.forwardRef((o,i)=>{const{asChild:s,...c}=o,f=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(f,{...c,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Dr(e,t){e&&qt.flushSync(()=>e.dispatchEvent(t))}function ie(e){const t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Nr(e,t=globalThis==null?void 0:globalThis.document){const n=ie(e);a.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ir="DismissableLayer",rt="dismissableLayer.update",Mr="dismissableLayer.pointerDownOutside",_r="dismissableLayer.focusOutside",Dt,nn=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),lt=a.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:c,...f}=e,l=a.useContext(nn),[u,d]=a.useState(null),h=(u==null?void 0:u.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,p]=a.useState({}),y=U(t,C=>d(C)),m=Array.from(l.layers),[v]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),g=m.indexOf(v),x=u?m.indexOf(u):-1,w=l.layersWithOutsidePointerEventsDisabled.size>0,b=x>=g,E=Lr(C=>{const A=C.target,D=[...l.branches].some(T=>T.contains(A));!b||D||(o==null||o(C),s==null||s(C),C.defaultPrevented||c==null||c())},h),R=kr(C=>{const A=C.target;[...l.branches].some(T=>T.contains(A))||(i==null||i(C),s==null||s(C),C.defaultPrevented||c==null||c())},h);return Nr(C=>{x===l.layers.size-1&&(r==null||r(C),!C.defaultPrevented&&c&&(C.preventDefault(),c()))},h),a.useEffect(()=>{if(u)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(Dt=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(u)),l.layers.add(u),Nt(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Dt)}},[u,h,n,l]),a.useEffect(()=>()=>{u&&(l.layers.delete(u),l.layersWithOutsidePointerEventsDisabled.delete(u),Nt())},[u,l]),a.useEffect(()=>{const C=()=>p({});return document.addEventListener(rt,C),()=>document.removeEventListener(rt,C)},[]),S.jsx(j.div,{...f,ref:y,style:{pointerEvents:w?b?"auto":"none":void 0,...e.style},onFocusCapture:W(e.onFocusCapture,R.onFocusCapture),onBlurCapture:W(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:W(e.onPointerDownCapture,E.onPointerDownCapture)})});lt.displayName=Ir;var Fr="DismissableLayerBranch",rn=a.forwardRef((e,t)=>{const n=a.useContext(nn),r=a.useRef(null),o=U(t,r);return a.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),S.jsx(j.div,{...e,ref:o})});rn.displayName=Fr;function Lr(e,t=globalThis==null?void 0:globalThis.document){const n=ie(e),r=a.useRef(!1),o=a.useRef(()=>{});return a.useEffect(()=>{const i=c=>{if(c.target&&!r.current){let f=function(){on(Mr,n,l,{discrete:!0})};const l={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=f,t.addEventListener("click",o.current,{once:!0})):f()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function kr(e,t=globalThis==null?void 0:globalThis.document){const n=ie(e),r=a.useRef(!1);return a.useEffect(()=>{const o=i=>{i.target&&!r.current&&on(_r,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Nt(){const e=new CustomEvent(rt);document.dispatchEvent(e)}function on(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Dr(o,i):o.dispatchEvent(i)}var xs=lt,bs=rn,te=globalThis!=null&&globalThis.document?a.useLayoutEffect:()=>{},Wr="Portal",sn=a.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,i]=a.useState(!1);te(()=>i(!0),[]);const s=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return s?hr.createPortal(S.jsx(j.div,{...r,ref:t}),s):null});sn.displayName=Wr;function jr(e,t){return a.useReducer((n,r)=>t[n][r]??n,e)}var Be=e=>{const{present:t,children:n}=e,r=$r(t),o=typeof n=="function"?n({present:r.isPresent}):a.Children.only(n),i=U(r.ref,Br(o));return typeof n=="function"||r.isPresent?a.cloneElement(o,{ref:i}):null};Be.displayName="Presence";function $r(e){const[t,n]=a.useState(),r=a.useRef(null),o=a.useRef(e),i=a.useRef("none"),s=e?"mounted":"unmounted",[c,f]=jr(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return a.useEffect(()=>{const l=Se(r.current);i.current=c==="mounted"?l:"none"},[c]),te(()=>{const l=r.current,u=o.current;if(u!==e){const h=i.current,p=Se(l);e?f("MOUNT"):p==="none"||(l==null?void 0:l.display)==="none"?f("UNMOUNT"):f(u&&h!==p?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,f]),te(()=>{if(t){let l;const u=t.ownerDocument.defaultView??window,d=p=>{const m=Se(r.current).includes(p.animationName);if(p.target===t&&m&&(f("ANIMATION_END"),!o.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",l=u.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},h=p=>{p.target===t&&(i.current=Se(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{u.clearTimeout(l),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else f("ANIMATION_END")},[t,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:a.useCallback(l=>{r.current=l?getComputedStyle(l):null,n(l)},[])}}function Se(e){return(e==null?void 0:e.animationName)||"none"}function Br(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ur=Zt[" useInsertionEffect ".trim().toString()]||te;function cn({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=Hr({defaultProp:t,onChange:n}),c=e!==void 0,f=c?e:o;{const u=a.useRef(e!==void 0);a.useEffect(()=>{const d=u.current;d!==c&&console.warn(`${r} is changing from ${d?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),u.current=c},[c,r])}const l=a.useCallback(u=>{var d;if(c){const h=Vr(u)?u(e):u;h!==e&&((d=s.current)==null||d.call(s,h))}else i(u)},[c,e,i,s]);return[f,l]}function Hr({defaultProp:e,onChange:t}){const[n,r]=a.useState(e),o=a.useRef(n),i=a.useRef(t);return Ur(()=>{i.current=t},[t]),a.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function Vr(e){return typeof e=="function"}var zr=Zt[" useId ".trim().toString()]||(()=>{}),Yr=0;function De(e){const[t,n]=a.useState(zr());return te(()=>{n(r=>r??String(Yr++))},[e]),t?`radix-${t}`:""}const Gr=["top","right","bottom","left"],ne=Math.min,$=Math.max,_e=Math.round,Re=Math.floor,X=e=>({x:e,y:e}),Kr={left:"right",right:"left",bottom:"top",top:"bottom"},Xr={start:"end",end:"start"};function ot(e,t,n){return $(e,ne(t,n))}function Q(e,t){return typeof e=="function"?e(t):e}function J(e){return e.split("-")[0]}function pe(e){return e.split("-")[1]}function ut(e){return e==="x"?"y":"x"}function ft(e){return e==="y"?"height":"width"}function K(e){return["top","bottom"].includes(J(e))?"y":"x"}function dt(e){return ut(K(e))}function qr(e,t,n){n===void 0&&(n=!1);const r=pe(e),o=dt(e),i=ft(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Fe(s)),[s,Fe(s)]}function Zr(e){const t=Fe(e);return[it(e),t,it(t)]}function it(e){return e.replace(/start|end/g,t=>Xr[t])}function Qr(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function Jr(e,t,n,r){const o=pe(e);let i=Qr(J(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(it)))),i}function Fe(e){return e.replace(/left|right|bottom|top/g,t=>Kr[t])}function eo(e){return{top:0,right:0,bottom:0,left:0,...e}}function an(e){return typeof e!="number"?eo(e):{top:e,right:e,bottom:e,left:e}}function Le(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function It(e,t,n){let{reference:r,floating:o}=e;const i=K(t),s=dt(t),c=ft(s),f=J(t),l=i==="y",u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,h=r[c]/2-o[c]/2;let p;switch(f){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(pe(t)){case"start":p[s]-=h*(n&&l?-1:1);break;case"end":p[s]+=h*(n&&l?-1:1);break}return p}const to=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,c=i.filter(Boolean),f=await(s.isRTL==null?void 0:s.isRTL(t));let l=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=It(l,r,f),h=r,p={},y=0;for(let m=0;m<c.length;m++){const{name:v,fn:g}=c[m],{x,y:w,data:b,reset:E}=await g({x:u,y:d,initialPlacement:r,placement:h,strategy:o,middlewareData:p,rects:l,platform:s,elements:{reference:e,floating:t}});u=x??u,d=w??d,p={...p,[v]:{...p[v],...b}},E&&y<=50&&(y++,typeof E=="object"&&(E.placement&&(h=E.placement),E.rects&&(l=E.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:u,y:d}=It(l,h,f)),m=-1)}return{x:u,y:d,placement:h,strategy:o,middlewareData:p}};async function ye(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:c,strategy:f}=e,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=Q(t,e),y=an(p),v=c[h?d==="floating"?"reference":"floating":d],g=Le(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(c.floating)),boundary:l,rootBoundary:u,strategy:f})),x=d==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,w=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c.floating)),b=await(i.isElement==null?void 0:i.isElement(w))?await(i.getScale==null?void 0:i.getScale(w))||{x:1,y:1}:{x:1,y:1},E=Le(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:w,strategy:f}):x);return{top:(g.top-E.top+y.top)/b.y,bottom:(E.bottom-g.bottom+y.bottom)/b.y,left:(g.left-E.left+y.left)/b.x,right:(E.right-g.right+y.right)/b.x}}const no=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:c,middlewareData:f}=t,{element:l,padding:u=0}=Q(e,t)||{};if(l==null)return{};const d=an(u),h={x:n,y:r},p=dt(o),y=ft(p),m=await s.getDimensions(l),v=p==="y",g=v?"top":"left",x=v?"bottom":"right",w=v?"clientHeight":"clientWidth",b=i.reference[y]+i.reference[p]-h[p]-i.floating[y],E=h[p]-i.reference[p],R=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let C=R?R[w]:0;(!C||!await(s.isElement==null?void 0:s.isElement(R)))&&(C=c.floating[w]||i.floating[y]);const A=b/2-E/2,D=C/2-m[y]/2-1,T=ne(d[g],D),M=ne(d[x],D),F=T,I=C-m[y]-M,N=C/2-m[y]/2+A,k=ot(F,N,I),O=!f.arrow&&pe(o)!=null&&N!==k&&i.reference[y]/2-(N<F?T:M)-m[y]/2<0,_=O?N<F?N-F:N-I:0;return{[p]:h[p]+_,data:{[p]:k,centerOffset:N-k-_,...O&&{alignmentOffset:_}},reset:O}}}),ro=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:c,platform:f,elements:l}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:h,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:m=!0,...v}=Q(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const g=J(o),x=K(c),w=J(c)===c,b=await(f.isRTL==null?void 0:f.isRTL(l.floating)),E=h||(w||!m?[Fe(c)]:Zr(c)),R=y!=="none";!h&&R&&E.push(...Jr(c,m,y,b));const C=[c,...E],A=await ye(t,v),D=[];let T=((r=i.flip)==null?void 0:r.overflows)||[];if(u&&D.push(A[g]),d){const N=qr(o,s,b);D.push(A[N[0]],A[N[1]])}if(T=[...T,{placement:o,overflows:D}],!D.every(N=>N<=0)){var M,F;const N=(((M=i.flip)==null?void 0:M.index)||0)+1,k=C[N];if(k&&(!(d==="alignment"?x!==K(k):!1)||T.every(P=>P.overflows[0]>0&&K(P.placement)===x)))return{data:{index:N,overflows:T},reset:{placement:k}};let O=(F=T.filter(_=>_.overflows[0]<=0).sort((_,P)=>_.overflows[1]-P.overflows[1])[0])==null?void 0:F.placement;if(!O)switch(p){case"bestFit":{var I;const _=(I=T.filter(P=>{if(R){const L=K(P.placement);return L===x||L==="y"}return!0}).map(P=>[P.placement,P.overflows.filter(L=>L>0).reduce((L,Y)=>L+Y,0)]).sort((P,L)=>P[1]-L[1])[0])==null?void 0:I[0];_&&(O=_);break}case"initialPlacement":O=c;break}if(o!==O)return{reset:{placement:O}}}return{}}}};function Mt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function _t(e){return Gr.some(t=>e[t]>=0)}const oo=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Q(e,t);switch(r){case"referenceHidden":{const i=await ye(t,{...o,elementContext:"reference"}),s=Mt(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:_t(s)}}}case"escaped":{const i=await ye(t,{...o,altBoundary:!0}),s=Mt(i,n.floating);return{data:{escapedOffsets:s,escaped:_t(s)}}}default:return{}}}}};async function io(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=J(n),c=pe(n),f=K(n)==="y",l=["left","top"].includes(s)?-1:1,u=i&&f?-1:1,d=Q(t,e);let{mainAxis:h,crossAxis:p,alignmentAxis:y}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof y=="number"&&(p=c==="end"?y*-1:y),f?{x:p*u,y:h*l}:{x:h*l,y:p*u}}const so=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:c}=t,f=await io(t,e);return s===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+f.x,y:i+f.y,data:{...f,placement:s}}}}},co=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:c={fn:v=>{let{x:g,y:x}=v;return{x:g,y:x}}},...f}=Q(e,t),l={x:n,y:r},u=await ye(t,f),d=K(J(o)),h=ut(d);let p=l[h],y=l[d];if(i){const v=h==="y"?"top":"left",g=h==="y"?"bottom":"right",x=p+u[v],w=p-u[g];p=ot(x,p,w)}if(s){const v=d==="y"?"top":"left",g=d==="y"?"bottom":"right",x=y+u[v],w=y-u[g];y=ot(x,y,w)}const m=c.fn({...t,[h]:p,[d]:y});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[h]:i,[d]:s}}}}}},ao=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:c=0,mainAxis:f=!0,crossAxis:l=!0}=Q(e,t),u={x:n,y:r},d=K(o),h=ut(d);let p=u[h],y=u[d];const m=Q(c,t),v=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(f){const w=h==="y"?"height":"width",b=i.reference[h]-i.floating[w]+v.mainAxis,E=i.reference[h]+i.reference[w]-v.mainAxis;p<b?p=b:p>E&&(p=E)}if(l){var g,x;const w=h==="y"?"width":"height",b=["top","left"].includes(J(o)),E=i.reference[d]-i.floating[w]+(b&&((g=s.offset)==null?void 0:g[d])||0)+(b?0:v.crossAxis),R=i.reference[d]+i.reference[w]+(b?0:((x=s.offset)==null?void 0:x[d])||0)-(b?v.crossAxis:0);y<E?y=E:y>R&&(y=R)}return{[h]:p,[d]:y}}}},lo=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:c}=t,{apply:f=()=>{},...l}=Q(e,t),u=await ye(t,l),d=J(o),h=pe(o),p=K(o)==="y",{width:y,height:m}=i.floating;let v,g;d==="top"||d==="bottom"?(v=d,g=h===(await(s.isRTL==null?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(g=d,v=h==="end"?"top":"bottom");const x=m-u.top-u.bottom,w=y-u.left-u.right,b=ne(m-u[v],x),E=ne(y-u[g],w),R=!t.middlewareData.shift;let C=b,A=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(A=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(C=x),R&&!h){const T=$(u.left,0),M=$(u.right,0),F=$(u.top,0),I=$(u.bottom,0);p?A=y-2*(T!==0||M!==0?T+M:$(u.left,u.right)):C=m-2*(F!==0||I!==0?F+I:$(u.top,u.bottom))}await f({...t,availableWidth:A,availableHeight:C});const D=await s.getDimensions(c.floating);return y!==D.width||m!==D.height?{reset:{rects:!0}}:{}}}};function Ue(){return typeof window<"u"}function he(e){return ln(e)?(e.nodeName||"").toLowerCase():"#document"}function B(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Z(e){var t;return(t=(ln(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function ln(e){return Ue()?e instanceof Node||e instanceof B(e).Node:!1}function H(e){return Ue()?e instanceof Element||e instanceof B(e).Element:!1}function q(e){return Ue()?e instanceof HTMLElement||e instanceof B(e).HTMLElement:!1}function Ft(e){return!Ue()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof B(e).ShadowRoot}function xe(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=V(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function uo(e){return["table","td","th"].includes(he(e))}function He(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function mt(e){const t=pt(),n=H(e)?V(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function fo(e){let t=re(e);for(;q(t)&&!me(t);){if(mt(t))return t;if(He(t))return null;t=re(t)}return null}function pt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function me(e){return["html","body","#document"].includes(he(e))}function V(e){return B(e).getComputedStyle(e)}function Ve(e){return H(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function re(e){if(he(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ft(e)&&e.host||Z(e);return Ft(t)?t.host:t}function un(e){const t=re(e);return me(t)?e.ownerDocument?e.ownerDocument.body:e.body:q(t)&&xe(t)?t:un(t)}function we(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=un(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=B(o);if(i){const c=st(s);return t.concat(s,s.visualViewport||[],xe(o)?o:[],c&&n?we(c):[])}return t.concat(o,we(o,[],n))}function st(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function fn(e){const t=V(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=q(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,c=_e(n)!==i||_e(r)!==s;return c&&(n=i,r=s),{width:n,height:r,$:c}}function ht(e){return H(e)?e:e.contextElement}function fe(e){const t=ht(e);if(!q(t))return X(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=fn(t);let s=(i?_e(n.width):n.width)/r,c=(i?_e(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const mo=X(0);function dn(e){const t=B(e);return!pt()||!t.visualViewport?mo:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function po(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==B(e)?!1:t}function se(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=ht(e);let s=X(1);t&&(r?H(r)&&(s=fe(r)):s=fe(e));const c=po(i,n,r)?dn(i):X(0);let f=(o.left+c.x)/s.x,l=(o.top+c.y)/s.y,u=o.width/s.x,d=o.height/s.y;if(i){const h=B(i),p=r&&H(r)?B(r):r;let y=h,m=st(y);for(;m&&r&&p!==y;){const v=fe(m),g=m.getBoundingClientRect(),x=V(m),w=g.left+(m.clientLeft+parseFloat(x.paddingLeft))*v.x,b=g.top+(m.clientTop+parseFloat(x.paddingTop))*v.y;f*=v.x,l*=v.y,u*=v.x,d*=v.y,f+=w,l+=b,y=B(m),m=st(y)}}return Le({width:u,height:d,x:f,y:l})}function vt(e,t){const n=Ve(e).scrollLeft;return t?t.left+n:se(Z(e)).left+n}function mn(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:vt(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function ho(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Z(r),c=t?He(t.floating):!1;if(r===s||c&&i)return n;let f={scrollLeft:0,scrollTop:0},l=X(1);const u=X(0),d=q(r);if((d||!d&&!i)&&((he(r)!=="body"||xe(s))&&(f=Ve(r)),q(r))){const p=se(r);l=fe(r),u.x=p.x+r.clientLeft,u.y=p.y+r.clientTop}const h=s&&!d&&!i?mn(s,f,!0):X(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-f.scrollLeft*l.x+u.x+h.x,y:n.y*l.y-f.scrollTop*l.y+u.y+h.y}}function vo(e){return Array.from(e.getClientRects())}function go(e){const t=Z(e),n=Ve(e),r=e.ownerDocument.body,o=$(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=$(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+vt(e);const c=-n.scrollTop;return V(r).direction==="rtl"&&(s+=$(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:c}}function yo(e,t){const n=B(e),r=Z(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,c=0,f=0;if(o){i=o.width,s=o.height;const l=pt();(!l||l&&t==="fixed")&&(c=o.offsetLeft,f=o.offsetTop)}return{width:i,height:s,x:c,y:f}}function wo(e,t){const n=se(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=q(e)?fe(e):X(1),s=e.clientWidth*i.x,c=e.clientHeight*i.y,f=o*i.x,l=r*i.y;return{width:s,height:c,x:f,y:l}}function Lt(e,t,n){let r;if(t==="viewport")r=yo(e,n);else if(t==="document")r=go(Z(e));else if(H(t))r=wo(t,n);else{const o=dn(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Le(r)}function pn(e,t){const n=re(e);return n===t||!H(n)||me(n)?!1:V(n).position==="fixed"||pn(n,t)}function xo(e,t){const n=t.get(e);if(n)return n;let r=we(e,[],!1).filter(c=>H(c)&&he(c)!=="body"),o=null;const i=V(e).position==="fixed";let s=i?re(e):e;for(;H(s)&&!me(s);){const c=V(s),f=mt(s);!f&&c.position==="fixed"&&(o=null),(i?!f&&!o:!f&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||xe(s)&&!f&&pn(e,s))?r=r.filter(u=>u!==s):o=c,s=re(s)}return t.set(e,r),r}function bo(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?He(t)?[]:xo(t,this._c):[].concat(n),r],c=s[0],f=s.reduce((l,u)=>{const d=Lt(t,u,o);return l.top=$(d.top,l.top),l.right=ne(d.right,l.right),l.bottom=ne(d.bottom,l.bottom),l.left=$(d.left,l.left),l},Lt(t,c,o));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function Eo(e){const{width:t,height:n}=fn(e);return{width:t,height:n}}function Co(e,t,n){const r=q(t),o=Z(t),i=n==="fixed",s=se(e,!0,i,t);let c={scrollLeft:0,scrollTop:0};const f=X(0);function l(){f.x=vt(o)}if(r||!r&&!i)if((he(t)!=="body"||xe(o))&&(c=Ve(t)),r){const p=se(t,!0,i,t);f.x=p.x+t.clientLeft,f.y=p.y+t.clientTop}else o&&l();i&&!r&&o&&l();const u=o&&!r&&!i?mn(o,c):X(0),d=s.left+c.scrollLeft-f.x-u.x,h=s.top+c.scrollTop-f.y-u.y;return{x:d,y:h,width:s.width,height:s.height}}function Ge(e){return V(e).position==="static"}function kt(e,t){if(!q(e)||V(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Z(e)===n&&(n=n.ownerDocument.body),n}function hn(e,t){const n=B(e);if(He(e))return n;if(!q(e)){let o=re(e);for(;o&&!me(o);){if(H(o)&&!Ge(o))return o;o=re(o)}return n}let r=kt(e,t);for(;r&&uo(r)&&Ge(r);)r=kt(r,t);return r&&me(r)&&Ge(r)&&!mt(r)?n:r||fo(e)||n}const So=async function(e){const t=this.getOffsetParent||hn,n=this.getDimensions,r=await n(e.floating);return{reference:Co(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ro(e){return V(e).direction==="rtl"}const Ao={convertOffsetParentRelativeRectToViewportRelativeRect:ho,getDocumentElement:Z,getClippingRect:bo,getOffsetParent:hn,getElementRects:So,getClientRects:vo,getDimensions:Eo,getScale:fe,isElement:H,isRTL:Ro};function vn(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Po(e,t){let n=null,r;const o=Z(e);function i(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function s(c,f){c===void 0&&(c=!1),f===void 0&&(f=1),i();const l=e.getBoundingClientRect(),{left:u,top:d,width:h,height:p}=l;if(c||t(),!h||!p)return;const y=Re(d),m=Re(o.clientWidth-(u+h)),v=Re(o.clientHeight-(d+p)),g=Re(u),w={rootMargin:-y+"px "+-m+"px "+-v+"px "+-g+"px",threshold:$(0,ne(1,f))||1};let b=!0;function E(R){const C=R[0].intersectionRatio;if(C!==f){if(!b)return s();C?s(!1,C):r=setTimeout(()=>{s(!1,1e-7)},1e3)}C===1&&!vn(l,e.getBoundingClientRect())&&s(),b=!1}try{n=new IntersectionObserver(E,{...w,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,w)}n.observe(e)}return s(!0),i}function Oo(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:f=!1}=r,l=ht(e),u=o||i?[...l?we(l):[],...we(t)]:[];u.forEach(g=>{o&&g.addEventListener("scroll",n,{passive:!0}),i&&g.addEventListener("resize",n)});const d=l&&c?Po(l,n):null;let h=-1,p=null;s&&(p=new ResizeObserver(g=>{let[x]=g;x&&x.target===l&&p&&(p.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var w;(w=p)==null||w.observe(t)})),n()}),l&&!f&&p.observe(l),p.observe(t));let y,m=f?se(e):null;f&&v();function v(){const g=se(e);m&&!vn(m,g)&&n(),m=g,y=requestAnimationFrame(v)}return n(),()=>{var g;u.forEach(x=>{o&&x.removeEventListener("scroll",n),i&&x.removeEventListener("resize",n)}),d==null||d(),(g=p)==null||g.disconnect(),p=null,f&&cancelAnimationFrame(y)}}const To=so,Do=co,No=ro,Io=lo,Mo=oo,Wt=no,_o=ao,Fo=(e,t,n)=>{const r=new Map,o={platform:Ao,...n},i={...o.platform,_c:r};return to(e,t,{...o,platform:i})};var Lo=typeof document<"u",ko=function(){},Ne=Lo?a.useLayoutEffect:ko;function ke(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!ke(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!ke(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function gn(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function jt(e,t){const n=gn(e);return Math.round(t*n)/n}function Ke(e){const t=a.useRef(e);return Ne(()=>{t.current=e}),t}function Wo(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:c=!0,whileElementsMounted:f,open:l}=e,[u,d]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=a.useState(r);ke(h,r)||p(r);const[y,m]=a.useState(null),[v,g]=a.useState(null),x=a.useCallback(P=>{P!==R.current&&(R.current=P,m(P))},[]),w=a.useCallback(P=>{P!==C.current&&(C.current=P,g(P))},[]),b=i||y,E=s||v,R=a.useRef(null),C=a.useRef(null),A=a.useRef(u),D=f!=null,T=Ke(f),M=Ke(o),F=Ke(l),I=a.useCallback(()=>{if(!R.current||!C.current)return;const P={placement:t,strategy:n,middleware:h};M.current&&(P.platform=M.current),Fo(R.current,C.current,P).then(L=>{const Y={...L,isPositioned:F.current!==!1};N.current&&!ke(A.current,Y)&&(A.current=Y,qt.flushSync(()=>{d(Y)}))})},[h,t,n,M,F]);Ne(()=>{l===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,d(P=>({...P,isPositioned:!1})))},[l]);const N=a.useRef(!1);Ne(()=>(N.current=!0,()=>{N.current=!1}),[]),Ne(()=>{if(b&&(R.current=b),E&&(C.current=E),b&&E){if(T.current)return T.current(b,E,I);I()}},[b,E,I,T,D]);const k=a.useMemo(()=>({reference:R,floating:C,setReference:x,setFloating:w}),[x,w]),O=a.useMemo(()=>({reference:b,floating:E}),[b,E]),_=a.useMemo(()=>{const P={position:n,left:0,top:0};if(!O.floating)return P;const L=jt(O.floating,u.x),Y=jt(O.floating,u.y);return c?{...P,transform:"translate("+L+"px, "+Y+"px)",...gn(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:L,top:Y}},[n,c,O.floating,u.x,u.y]);return a.useMemo(()=>({...u,update:I,refs:k,elements:O,floatingStyles:_}),[u,I,k,O,_])}const jo=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Wt({element:r.current,padding:o}).fn(n):{}:r?Wt({element:r,padding:o}).fn(n):{}}}},$o=(e,t)=>({...To(e),options:[e,t]}),Bo=(e,t)=>({...Do(e),options:[e,t]}),Uo=(e,t)=>({..._o(e),options:[e,t]}),Ho=(e,t)=>({...No(e),options:[e,t]}),Vo=(e,t)=>({...Io(e),options:[e,t]}),zo=(e,t)=>({...Mo(e),options:[e,t]}),Yo=(e,t)=>({...jo(e),options:[e,t]});var Go="Arrow",yn=a.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return S.jsx(j.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:S.jsx("polygon",{points:"0,0 30,0 15,10"})})});yn.displayName=Go;var Ko=yn;function Xo(e){const[t,n]=a.useState(void 0);return te(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,c;if("borderBoxSize"in i){const f=i.borderBoxSize,l=Array.isArray(f)?f[0]:f;s=l.inlineSize,c=l.blockSize}else s=e.offsetWidth,c=e.offsetHeight;n({width:s,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var gt="Popper",[wn,Es]=$e(gt),[qo,xn]=wn(gt),bn=e=>{const{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return S.jsx(qo,{scope:t,anchor:r,onAnchorChange:o,children:n})};bn.displayName=gt;var En="PopperAnchor",Cn=a.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=xn(En,n),s=a.useRef(null),c=U(t,s);return a.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:S.jsx(j.div,{...o,ref:c})});Cn.displayName=En;var yt="PopperContent",[Zo,Qo]=wn(yt),Sn=a.forwardRef((e,t)=>{var Et,Ct,St,Rt,At,Pt;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:f=!0,collisionBoundary:l=[],collisionPadding:u=0,sticky:d="partial",hideWhenDetached:h=!1,updatePositionStrategy:p="optimized",onPlaced:y,...m}=e,v=xn(yt,n),[g,x]=a.useState(null),w=U(t,ve=>x(ve)),[b,E]=a.useState(null),R=Xo(b),C=(R==null?void 0:R.width)??0,A=(R==null?void 0:R.height)??0,D=r+(i!=="center"?"-"+i:""),T=typeof u=="number"?u:{top:0,right:0,bottom:0,left:0,...u},M=Array.isArray(l)?l:[l],F=M.length>0,I={padding:T,boundary:M.filter(ei),altBoundary:F},{refs:N,floatingStyles:k,placement:O,isPositioned:_,middlewareData:P}=Wo({strategy:"fixed",placement:D,whileElementsMounted:(...ve)=>Oo(...ve,{animationFrame:p==="always"}),elements:{reference:v.anchor},middleware:[$o({mainAxis:o+A,alignmentAxis:s}),f&&Bo({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?Uo():void 0,...I}),f&&Ho({...I}),Vo({...I,apply:({elements:ve,rects:Ot,availableWidth:fr,availableHeight:dr})=>{const{width:mr,height:pr}=Ot.reference,Ce=ve.floating.style;Ce.setProperty("--radix-popper-available-width",`${fr}px`),Ce.setProperty("--radix-popper-available-height",`${dr}px`),Ce.setProperty("--radix-popper-anchor-width",`${mr}px`),Ce.setProperty("--radix-popper-anchor-height",`${pr}px`)}}),b&&Yo({element:b,padding:c}),ti({arrowWidth:C,arrowHeight:A}),h&&zo({strategy:"referenceHidden",...I})]}),[L,Y]=Pn(O),Ee=ie(y);te(()=>{_&&(Ee==null||Ee())},[_,Ee]);const sr=(Et=P.arrow)==null?void 0:Et.x,cr=(Ct=P.arrow)==null?void 0:Ct.y,ar=((St=P.arrow)==null?void 0:St.centerOffset)!==0,[lr,ur]=a.useState();return te(()=>{g&&ur(window.getComputedStyle(g).zIndex)},[g]),S.jsx("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...k,transform:_?k.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:lr,"--radix-popper-transform-origin":[(Rt=P.transformOrigin)==null?void 0:Rt.x,(At=P.transformOrigin)==null?void 0:At.y].join(" "),...((Pt=P.hide)==null?void 0:Pt.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:S.jsx(Zo,{scope:n,placedSide:L,onArrowChange:E,arrowX:sr,arrowY:cr,shouldHideArrow:ar,children:S.jsx(j.div,{"data-side":L,"data-align":Y,...m,ref:w,style:{...m.style,animation:_?void 0:"none"}})})})});Sn.displayName=yt;var Rn="PopperArrow",Jo={top:"bottom",right:"left",bottom:"top",left:"right"},An=a.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=Qo(Rn,r),s=Jo[i.placedSide];return S.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:S.jsx(Ko,{...o,ref:n,style:{...o.style,display:"block"}})})});An.displayName=Rn;function ei(e){return e!==null}var ti=e=>({name:"transformOrigin",options:e,fn(t){var v,g,x;const{placement:n,rects:r,middlewareData:o}=t,s=((v=o.arrow)==null?void 0:v.centerOffset)!==0,c=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[l,u]=Pn(n),d={start:"0%",center:"50%",end:"100%"}[u],h=(((g=o.arrow)==null?void 0:g.x)??0)+c/2,p=(((x=o.arrow)==null?void 0:x.y)??0)+f/2;let y="",m="";return l==="bottom"?(y=s?d:`${h}px`,m=`${-f}px`):l==="top"?(y=s?d:`${h}px`,m=`${r.floating.height+f}px`):l==="right"?(y=`${-f}px`,m=s?d:`${p}px`):l==="left"&&(y=`${r.floating.width+f}px`,m=s?d:`${p}px`),{data:{x:y,y:m}}}});function Pn(e){const[t,n="center"]=e.split("-");return[t,n]}var Cs=bn,Ss=Cn,Rs=Sn,As=An,Xe="focusScope.autoFocusOnMount",qe="focusScope.autoFocusOnUnmount",$t={bubbles:!1,cancelable:!0},ni="FocusScope",On=a.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[c,f]=a.useState(null),l=ie(o),u=ie(i),d=a.useRef(null),h=U(t,m=>f(m)),p=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let m=function(w){if(p.paused||!c)return;const b=w.target;c.contains(b)?d.current=b:ee(d.current,{select:!0})},v=function(w){if(p.paused||!c)return;const b=w.relatedTarget;b!==null&&(c.contains(b)||ee(d.current,{select:!0}))},g=function(w){if(document.activeElement===document.body)for(const E of w)E.removedNodes.length>0&&ee(c)};document.addEventListener("focusin",m),document.addEventListener("focusout",v);const x=new MutationObserver(g);return c&&x.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",v),x.disconnect()}}},[r,c,p.paused]),a.useEffect(()=>{if(c){Ut.add(p);const m=document.activeElement;if(!c.contains(m)){const g=new CustomEvent(Xe,$t);c.addEventListener(Xe,l),c.dispatchEvent(g),g.defaultPrevented||(ri(ai(Tn(c)),{select:!0}),document.activeElement===m&&ee(c))}return()=>{c.removeEventListener(Xe,l),setTimeout(()=>{const g=new CustomEvent(qe,$t);c.addEventListener(qe,u),c.dispatchEvent(g),g.defaultPrevented||ee(m??document.body,{select:!0}),c.removeEventListener(qe,u),Ut.remove(p)},0)}}},[c,l,u,p]);const y=a.useCallback(m=>{if(!n&&!r||p.paused)return;const v=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,g=document.activeElement;if(v&&g){const x=m.currentTarget,[w,b]=oi(x);w&&b?!m.shiftKey&&g===b?(m.preventDefault(),n&&ee(w,{select:!0})):m.shiftKey&&g===w&&(m.preventDefault(),n&&ee(b,{select:!0})):g===x&&m.preventDefault()}},[n,r,p.paused]);return S.jsx(j.div,{tabIndex:-1,...s,ref:h,onKeyDown:y})});On.displayName=ni;function ri(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ee(r,{select:t}),document.activeElement!==n)return}function oi(e){const t=Tn(e),n=Bt(t,e),r=Bt(t.reverse(),e);return[n,r]}function Tn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Bt(e,t){for(const n of e)if(!ii(n,{upTo:t}))return n}function ii(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function si(e){return e instanceof HTMLInputElement&&"select"in e}function ee(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&si(e)&&t&&e.select()}}var Ut=ci();function ci(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Ht(e,t),e.unshift(t)},remove(t){var n;e=Ht(e,t),(n=e[0])==null||n.resume()}}}function Ht(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function ai(e){return e.filter(t=>t.tagName!=="A")}var Ze=0;function li(){a.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Vt()),document.body.insertAdjacentElement("beforeend",e[1]??Vt()),Ze++,()=>{Ze===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Ze--}},[])}function Vt(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var G=function(){return G=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},G.apply(this,arguments)};function Dn(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function ui(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Ie="right-scroll-bar-position",Me="width-before-scroll-bar",fi="with-scroll-bars-hidden",di="--removed-body-scroll-bar-size";function Qe(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function mi(e,t){var n=a.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var pi=typeof window<"u"?a.useLayoutEffect:a.useEffect,zt=new WeakMap;function hi(e,t){var n=mi(null,function(r){return e.forEach(function(o){return Qe(o,r)})});return pi(function(){var r=zt.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(c){i.has(c)||Qe(c,null)}),i.forEach(function(c){o.has(c)||Qe(c,s)})}zt.set(n,e)},[e]),n}function vi(e){return e}function gi(e,t){t===void 0&&(t=vi);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(c){return c!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(c){return i(c)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var c=n;n=[],c.forEach(i),s=n}var f=function(){var u=s;s=[],u.forEach(i)},l=function(){return Promise.resolve().then(f)};l(),n={push:function(u){s.push(u),l()},filter:function(u){return s=s.filter(u),n}}}};return o}function yi(e){e===void 0&&(e={});var t=gi(null);return t.options=G({async:!0,ssr:!1},e),t}var Nn=function(e){var t=e.sideCar,n=Dn(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return a.createElement(r,G({},n))};Nn.isSideCarExport=!0;function wi(e,t){return e.useMedium(t),Nn}var In=yi(),Je=function(){},ze=a.forwardRef(function(e,t){var n=a.useRef(null),r=a.useState({onScrollCapture:Je,onWheelCapture:Je,onTouchMoveCapture:Je}),o=r[0],i=r[1],s=e.forwardProps,c=e.children,f=e.className,l=e.removeScrollBar,u=e.enabled,d=e.shards,h=e.sideCar,p=e.noRelative,y=e.noIsolation,m=e.inert,v=e.allowPinchZoom,g=e.as,x=g===void 0?"div":g,w=e.gapMode,b=Dn(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=h,R=hi([n,t]),C=G(G({},b),o);return a.createElement(a.Fragment,null,u&&a.createElement(E,{sideCar:In,removeScrollBar:l,shards:d,noRelative:p,noIsolation:y,inert:m,setCallbacks:i,allowPinchZoom:!!v,lockRef:n,gapMode:w}),s?a.cloneElement(a.Children.only(c),G(G({},C),{ref:R})):a.createElement(x,G({},C,{className:f,ref:R}),c))});ze.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ze.classNames={fullWidth:Me,zeroRight:Ie};var xi=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function bi(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=xi();return t&&e.setAttribute("nonce",t),e}function Ei(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Ci(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Si=function(){var e=0,t=null;return{add:function(n){e==0&&(t=bi())&&(Ei(t,n),Ci(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Ri=function(){var e=Si();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Mn=function(){var e=Ri(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Ai={left:0,top:0,right:0,gap:0},et=function(e){return parseInt(e||"",10)||0},Pi=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[et(n),et(r),et(o)]},Oi=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ai;var t=Pi(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Ti=Mn(),de="data-scroll-locked",Di=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(fi,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(de,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ie,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Me,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(Ie," .").concat(Ie,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Me," .").concat(Me,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(de,`] {
    `).concat(di,": ").concat(c,`px;
  }
`)},Yt=function(){var e=parseInt(document.body.getAttribute(de)||"0",10);return isFinite(e)?e:0},Ni=function(){a.useEffect(function(){return document.body.setAttribute(de,(Yt()+1).toString()),function(){var e=Yt()-1;e<=0?document.body.removeAttribute(de):document.body.setAttribute(de,e.toString())}},[])},Ii=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Ni();var i=a.useMemo(function(){return Oi(o)},[o]);return a.createElement(Ti,{styles:Di(i,!t,o,n?"":"!important")})},ct=!1;if(typeof window<"u")try{var Ae=Object.defineProperty({},"passive",{get:function(){return ct=!0,!0}});window.addEventListener("test",Ae,Ae),window.removeEventListener("test",Ae,Ae)}catch{ct=!1}var ae=ct?{passive:!1}:!1,Mi=function(e){return e.tagName==="TEXTAREA"},_n=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Mi(e)&&n[t]==="visible")},_i=function(e){return _n(e,"overflowY")},Fi=function(e){return _n(e,"overflowX")},Gt=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Fn(e,r);if(o){var i=Ln(e,r),s=i[1],c=i[2];if(s>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Li=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},ki=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Fn=function(e,t){return e==="v"?_i(t):Fi(t)},Ln=function(e,t){return e==="v"?Li(t):ki(t)},Wi=function(e,t){return e==="h"&&t==="rtl"?-1:1},ji=function(e,t,n,r,o){var i=Wi(e,window.getComputedStyle(t).direction),s=i*r,c=n.target,f=t.contains(c),l=!1,u=s>0,d=0,h=0;do{if(!c)break;var p=Ln(e,c),y=p[0],m=p[1],v=p[2],g=m-v-i*y;(y||g)&&Fn(e,c)&&(d+=g,h+=y);var x=c.parentNode;c=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!f&&c!==document.body||f&&(t.contains(c)||t===c));return(u&&Math.abs(d)<1||!u&&Math.abs(h)<1)&&(l=!0),l},Pe=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Kt=function(e){return[e.deltaX,e.deltaY]},Xt=function(e){return e&&"current"in e?e.current:e},$i=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Bi=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Ui=0,le=[];function Hi(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(Ui++)[0],i=a.useState(Mn)[0],s=a.useRef(e);a.useEffect(function(){s.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=ui([e.lockRef.current],(e.shards||[]).map(Xt),!0).filter(Boolean);return m.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(m,v){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!s.current.allowPinchZoom;var g=Pe(m),x=n.current,w="deltaX"in m?m.deltaX:x[0]-g[0],b="deltaY"in m?m.deltaY:x[1]-g[1],E,R=m.target,C=Math.abs(w)>Math.abs(b)?"h":"v";if("touches"in m&&C==="h"&&R.type==="range")return!1;var A=Gt(C,R);if(!A)return!0;if(A?E=C:(E=C==="v"?"h":"v",A=Gt(C,R)),!A)return!1;if(!r.current&&"changedTouches"in m&&(w||b)&&(r.current=E),!E)return!0;var D=r.current||E;return ji(D,v,m,D==="h"?w:b)},[]),f=a.useCallback(function(m){var v=m;if(!(!le.length||le[le.length-1]!==i)){var g="deltaY"in v?Kt(v):Pe(v),x=t.current.filter(function(E){return E.name===v.type&&(E.target===v.target||v.target===E.shadowParent)&&$i(E.delta,g)})[0];if(x&&x.should){v.cancelable&&v.preventDefault();return}if(!x){var w=(s.current.shards||[]).map(Xt).filter(Boolean).filter(function(E){return E.contains(v.target)}),b=w.length>0?c(v,w[0]):!s.current.noIsolation;b&&v.cancelable&&v.preventDefault()}}},[]),l=a.useCallback(function(m,v,g,x){var w={name:m,delta:v,target:g,should:x,shadowParent:Vi(g)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(b){return b!==w})},1)},[]),u=a.useCallback(function(m){n.current=Pe(m),r.current=void 0},[]),d=a.useCallback(function(m){l(m.type,Kt(m),m.target,c(m,e.lockRef.current))},[]),h=a.useCallback(function(m){l(m.type,Pe(m),m.target,c(m,e.lockRef.current))},[]);a.useEffect(function(){return le.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:h}),document.addEventListener("wheel",f,ae),document.addEventListener("touchmove",f,ae),document.addEventListener("touchstart",u,ae),function(){le=le.filter(function(m){return m!==i}),document.removeEventListener("wheel",f,ae),document.removeEventListener("touchmove",f,ae),document.removeEventListener("touchstart",u,ae)}},[]);var p=e.removeScrollBar,y=e.inert;return a.createElement(a.Fragment,null,y?a.createElement(i,{styles:Bi(o)}):null,p?a.createElement(Ii,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Vi(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const zi=wi(In,Hi);var kn=a.forwardRef(function(e,t){return a.createElement(ze,G({},e,{ref:t,sideCar:zi}))});kn.classNames=ze.classNames;var Yi=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ue=new WeakMap,Oe=new WeakMap,Te={},tt=0,Wn=function(e){return e&&(e.host||Wn(e.parentNode))},Gi=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Wn(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Ki=function(e,t,n,r){var o=Gi(t,Array.isArray(e)?e:[e]);Te[n]||(Te[n]=new WeakMap);var i=Te[n],s=[],c=new Set,f=new Set(o),l=function(d){!d||c.has(d)||(c.add(d),l(d.parentNode))};o.forEach(l);var u=function(d){!d||f.has(d)||Array.prototype.forEach.call(d.children,function(h){if(c.has(h))u(h);else try{var p=h.getAttribute(r),y=p!==null&&p!=="false",m=(ue.get(h)||0)+1,v=(i.get(h)||0)+1;ue.set(h,m),i.set(h,v),s.push(h),m===1&&y&&Oe.set(h,!0),v===1&&h.setAttribute(n,"true"),y||h.setAttribute(r,"true")}catch(g){console.error("aria-hidden: cannot operate on ",h,g)}})};return u(t),c.clear(),tt++,function(){s.forEach(function(d){var h=ue.get(d)-1,p=i.get(d)-1;ue.set(d,h),i.set(d,p),h||(Oe.has(d)||d.removeAttribute(r),Oe.delete(d)),p||d.removeAttribute(n)}),tt--,tt||(ue=new WeakMap,ue=new WeakMap,Oe=new WeakMap,Te={})}},Xi=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Yi(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Ki(r,o,n,"aria-hidden")):function(){return null}},Ye="Dialog",[jn,Ps]=$e(Ye),[qi,z]=jn(Ye),$n=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,c=a.useRef(null),f=a.useRef(null),[l,u]=cn({prop:r,defaultProp:o??!1,onChange:i,caller:Ye});return S.jsx(qi,{scope:t,triggerRef:c,contentRef:f,contentId:De(),titleId:De(),descriptionId:De(),open:l,onOpenChange:u,onOpenToggle:a.useCallback(()=>u(d=>!d),[u]),modal:s,children:n})};$n.displayName=Ye;var Bn="DialogTrigger",Un=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=z(Bn,n),i=U(t,o.triggerRef);return S.jsx(j.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":bt(o.open),...r,ref:i,onClick:W(e.onClick,o.onOpenToggle)})});Un.displayName=Bn;var wt="DialogPortal",[Zi,Hn]=jn(wt,{forceMount:void 0}),Vn=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=z(wt,t);return S.jsx(Zi,{scope:t,forceMount:n,children:a.Children.map(r,s=>S.jsx(Be,{present:n||i.open,children:S.jsx(sn,{asChild:!0,container:o,children:s})}))})};Vn.displayName=wt;var We="DialogOverlay",zn=a.forwardRef((e,t)=>{const n=Hn(We,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=z(We,e.__scopeDialog);return i.modal?S.jsx(Be,{present:r||i.open,children:S.jsx(Ji,{...o,ref:t})}):null});zn.displayName=We;var Qi=ge("DialogOverlay.RemoveScroll"),Ji=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=z(We,n);return S.jsx(kn,{as:Qi,allowPinchZoom:!0,shards:[o.contentRef],children:S.jsx(j.div,{"data-state":bt(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ce="DialogContent",Yn=a.forwardRef((e,t)=>{const n=Hn(ce,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=z(ce,e.__scopeDialog);return S.jsx(Be,{present:r||i.open,children:i.modal?S.jsx(es,{...o,ref:t}):S.jsx(ts,{...o,ref:t})})});Yn.displayName=ce;var es=a.forwardRef((e,t)=>{const n=z(ce,e.__scopeDialog),r=a.useRef(null),o=U(t,n.contentRef,r);return a.useEffect(()=>{const i=r.current;if(i)return Xi(i)},[]),S.jsx(Gn,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:W(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:W(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,c=s.button===0&&s.ctrlKey===!0;(s.button===2||c)&&i.preventDefault()}),onFocusOutside:W(e.onFocusOutside,i=>i.preventDefault())})}),ts=a.forwardRef((e,t)=>{const n=z(ce,e.__scopeDialog),r=a.useRef(!1),o=a.useRef(!1);return S.jsx(Gn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,c;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var f,l;(f=e.onInteractOutside)==null||f.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const s=i.target;((l=n.triggerRef.current)==null?void 0:l.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}})}),Gn=a.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,c=z(ce,n),f=a.useRef(null),l=U(t,f);return li(),S.jsxs(S.Fragment,{children:[S.jsx(On,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:S.jsx(lt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":bt(c.open),...s,ref:l,onDismiss:()=>c.onOpenChange(!1)})}),S.jsxs(S.Fragment,{children:[S.jsx(ns,{titleId:c.titleId}),S.jsx(os,{contentRef:f,descriptionId:c.descriptionId})]})]})}),xt="DialogTitle",Kn=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=z(xt,n);return S.jsx(j.h2,{id:o.titleId,...r,ref:t})});Kn.displayName=xt;var Xn="DialogDescription",qn=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=z(Xn,n);return S.jsx(j.p,{id:o.descriptionId,...r,ref:t})});qn.displayName=Xn;var Zn="DialogClose",Qn=a.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=z(Zn,n);return S.jsx(j.button,{type:"button",...r,ref:t,onClick:W(e.onClick,()=>o.onOpenChange(!1))})});Qn.displayName=Zn;function bt(e){return e?"open":"closed"}var Jn="DialogTitleWarning",[Os,er]=Er(Jn,{contentName:ce,titleName:xt,docsSlug:"dialog"}),ns=({titleId:e})=>{const t=er(Jn),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},rs="DialogDescriptionWarning",os=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${er(rs).contentName}}.`;return a.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Ts=$n,Ds=Un,Ns=Vn,Is=zn,Ms=Yn,_s=Kn,Fs=qn,Ls=Qn,is=a.createContext(void 0);function ss(e){const t=a.useContext(is);return e||t||"ltr"}var nt="rovingFocusGroup.onEntryFocus",cs={bubbles:!1,cancelable:!0},be="RovingFocusGroup",[at,tr,as]=Or(be),[ls,ks]=$e(be,[as]),[us,fs]=ls(be),nr=a.forwardRef((e,t)=>S.jsx(at.Provider,{scope:e.__scopeRovingFocusGroup,children:S.jsx(at.Slot,{scope:e.__scopeRovingFocusGroup,children:S.jsx(ds,{...e,ref:t})})}));nr.displayName=be;var ds=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:f,onEntryFocus:l,preventScrollOnEntryFocus:u=!1,...d}=e,h=a.useRef(null),p=U(t,h),y=ss(i),[m,v]=cn({prop:s,defaultProp:c??null,onChange:f,caller:be}),[g,x]=a.useState(!1),w=ie(l),b=tr(n),E=a.useRef(!1),[R,C]=a.useState(0);return a.useEffect(()=>{const A=h.current;if(A)return A.addEventListener(nt,w),()=>A.removeEventListener(nt,w)},[w]),S.jsx(us,{scope:n,orientation:r,dir:y,loop:o,currentTabStopId:m,onItemFocus:a.useCallback(A=>v(A),[v]),onItemShiftTab:a.useCallback(()=>x(!0),[]),onFocusableItemAdd:a.useCallback(()=>C(A=>A+1),[]),onFocusableItemRemove:a.useCallback(()=>C(A=>A-1),[]),children:S.jsx(j.div,{tabIndex:g||R===0?-1:0,"data-orientation":r,...d,ref:p,style:{outline:"none",...e.style},onMouseDown:W(e.onMouseDown,()=>{E.current=!0}),onFocus:W(e.onFocus,A=>{const D=!E.current;if(A.target===A.currentTarget&&D&&!g){const T=new CustomEvent(nt,cs);if(A.currentTarget.dispatchEvent(T),!T.defaultPrevented){const M=b().filter(O=>O.focusable),F=M.find(O=>O.active),I=M.find(O=>O.id===m),k=[F,I,...M].filter(Boolean).map(O=>O.ref.current);ir(k,u)}}E.current=!1}),onBlur:W(e.onBlur,()=>x(!1))})})}),rr="RovingFocusGroupItem",or=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:s,...c}=e,f=De(),l=i||f,u=fs(rr,n),d=u.currentTabStopId===l,h=tr(n),{onFocusableItemAdd:p,onFocusableItemRemove:y,currentTabStopId:m}=u;return a.useEffect(()=>{if(r)return p(),()=>y()},[r,p,y]),S.jsx(at.ItemSlot,{scope:n,id:l,focusable:r,active:o,children:S.jsx(j.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...c,ref:t,onMouseDown:W(e.onMouseDown,v=>{r?u.onItemFocus(l):v.preventDefault()}),onFocus:W(e.onFocus,()=>u.onItemFocus(l)),onKeyDown:W(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){u.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const g=hs(v,u.orientation,u.dir);if(g!==void 0){if(v.metaKey||v.ctrlKey||v.altKey||v.shiftKey)return;v.preventDefault();let w=h().filter(b=>b.focusable).map(b=>b.ref.current);if(g==="last")w.reverse();else if(g==="prev"||g==="next"){g==="prev"&&w.reverse();const b=w.indexOf(v.currentTarget);w=u.loop?vs(w,b+1):w.slice(b+1)}setTimeout(()=>ir(w))}}),children:typeof s=="function"?s({isCurrentTabStop:d,hasTabStop:m!=null}):s})})});or.displayName=rr;var ms={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ps(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function hs(e,t,n){const r=ps(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return ms[r]}function ir(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function vs(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Ws=nr,js=or;export{Ss as A,bs as B,Rs as C,lt as D,kn as E,ge as F,On as G,Cs as H,js as I,Is as O,j as P,xs as R,ys as S,_s as T,Or as a,cn as b,$e as c,Be as d,W as e,ie as f,sn as g,te as h,Dr as i,S as j,Es as k,ws as l,As as m,Ns as n,Ms as o,Ls as p,Fs as q,Ts as r,Ds as s,ks as t,U as u,Ws as v,ss as w,De as x,Xi as y,li as z};
