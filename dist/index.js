// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// server/openai.ts
async function generateChatResponse(userMessage, chatHistory = []) {
  await new Promise((resolve) => setTimeout(resolve, 1e3));
  const lowerMessage = userMessage.toLowerCase();
  if (lowerMessage.includes("ai") || lowerMessage.includes("artificial intelligence") || lowerMessage.includes("machine learning")) {
    return "Our AI Solutions leverage advanced machine learning and predictive analytics to drive business innovation. We help companies optimize operations, enhance customer experiences, and unlock new growth opportunities through intelligent automation.";
  }
  if (lowerMessage.includes("ecommerce") || lowerMessage.includes("online store") || lowerMessage.includes("marketplace")) {
    return "We specialize in building powerful ecommerce solutions tailored to your business needs. Our services include custom online stores, marketplace integrations, payment processing, inventory management, and mobile-responsive designs for both retail and wholesale businesses.";
  }
  if (lowerMessage.includes("marketing") || lowerMessage.includes("seo") || lowerMessage.includes("social media")) {
    return "Our Digital Marketing services help you attract, engage, and convert customers through strategic campaigns. We offer SEO optimization, social media management, paid advertising, content marketing, and comprehensive analytics to maximize your ROI.";
  }
  if (lowerMessage.includes("leads") || lowerMessage.includes("lead generation") || lowerMessage.includes("prospects")) {
    return "Our Lead Generation services create qualified prospects through intelligent, automated funnels. We help you identify potential customers, nurture relationships, and convert leads into sales using data-driven strategies and marketing automation.";
  }
  if (lowerMessage.includes("automation") || lowerMessage.includes("workflow") || lowerMessage.includes("process")) {
    return "We offer comprehensive Business Automation and Process Management solutions to streamline your operations. Our services include workflow optimization, automated reporting, task management systems, and integration solutions to boost productivity and operational excellence.";
  }
  if (lowerMessage.includes("fulfillment") || lowerMessage.includes("shipping") || lowerMessage.includes("supply chain")) {
    return "Our Order Fulfillment services provide end-to-end supply chain solutions for efficient product delivery. We handle inventory management, order processing, shipping coordination, and tracking systems to ensure your customers receive their orders promptly.";
  }
  if (lowerMessage.includes("digital signage") || lowerMessage.includes("displays") || lowerMessage.includes("retail")) {
    return "Our Digital Signage solutions create engaging interactive displays for retail environments. We design and implement dynamic content management systems, customer engagement platforms, and analytics dashboards to enhance your in-store experience.";
  }
  if (lowerMessage.includes("pricing") || lowerMessage.includes("cost") || lowerMessage.includes("price")) {
    return "Our pricing is customized based on your specific needs and project scope. We offer competitive rates and flexible packages for all our services. I'd be happy to connect you with our sales team for a detailed quote tailored to your requirements.";
  }
  if (lowerMessage.includes("contact") || lowerMessage.includes("speak") || lowerMessage.includes("call")) {
    return "I'd be happy to connect you with our team! You can reach us through our contact page. What particular service or solution are you interested in learning more about?";
  }
  return "Thank you for your inquiry! MP Advance Solutions offers a comprehensive range of services including AI Solutions, Ecommerce Development, Digital Marketing, Lead Generation, Business Automation, Process Management, Order Fulfillment, and Digital Signage. How can I help you with your specific business needs today?";
}
async function categorizeInquiry(userMessage) {
  const lowerMessage = userMessage.toLowerCase();
  if (lowerMessage.includes("ai") || lowerMessage.includes("artificial intelligence") || lowerMessage.includes("machine learning")) {
    return "AI_Solutions";
  }
  if (lowerMessage.includes("ecommerce") || lowerMessage.includes("online store") || lowerMessage.includes("marketplace")) {
    return "Ecommerce";
  }
  if (lowerMessage.includes("marketing") || lowerMessage.includes("seo") || lowerMessage.includes("social media")) {
    return "Digital_Marketing";
  }
  if (lowerMessage.includes("leads") || lowerMessage.includes("lead generation")) {
    return "Leads_Generation";
  }
  if (lowerMessage.includes("automation") || lowerMessage.includes("workflow")) {
    return "Business_Automation";
  }
  if (lowerMessage.includes("process") || lowerMessage.includes("management")) {
    return "Business_Process_Management";
  }
  if (lowerMessage.includes("fulfillment") || lowerMessage.includes("shipping")) {
    return "Order_Fulfillment";
  }
  if (lowerMessage.includes("digital signage") || lowerMessage.includes("displays")) {
    return "Digital_Signage";
  }
  if (lowerMessage.includes("pricing") || lowerMessage.includes("cost") || lowerMessage.includes("price")) {
    return "Pricing";
  }
  if (lowerMessage.includes("support") || lowerMessage.includes("help") || lowerMessage.includes("problem")) {
    return "Support_Request";
  }
  return "General_Inquiry";
}

// server/routes.ts
async function registerRoutes(app2) {
  app2.post("/api/chatbot", async (req, res) => {
    try {
      const { message, chatHistory } = req.body;
      if (!message) {
        return res.status(400).json({ error: "Message is required" });
      }
      const response = await generateChatResponse(message, chatHistory);
      const category = await categorizeInquiry(message);
      res.json({
        response,
        category
      });
    } catch (error) {
      console.error("Error in chatbot API:", error);
      res.status(500).json({
        error: "Failed to process request",
        message: "Our AI assistant is temporarily unavailable. Please try again later."
      });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  assetsInclude: ["**/*.svg", "**/*.otf"],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "client", "src", "assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom"],
          ui: ["@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu"],
          utils: ["clsx", "tailwind-merge"]
        }
      }
    },
    chunkSizeWarningLimit: 1e3
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
