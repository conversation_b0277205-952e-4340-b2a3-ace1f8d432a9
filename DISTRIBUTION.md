# MP Advance Solutions - Distribution Package

This document provides comprehensive instructions for building and deploying the MP Advance Solutions application.

## 📦 Distribution Options

The application supports multiple distribution methods:

1. **NPM Package** - For Node.js environments
2. **Docker Container** - For containerized deployments
3. **Standalone Package** - Self-contained distribution
4. **Docker Compose** - Complete stack deployment

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Docker (for containerized deployments)
- Docker Compose (for stack deployments)

### Build Commands

```bash
# Build for production
npm run build:prod

# Create NPM package
npm run dist:package

# Build Docker image
npm run dist:docker

# Deploy with Docker Compose
npm run dist:docker-compose

# Create all distribution packages
npm run dist:all
```

## 🛠️ Deployment Methods

### 1. NPM Package Deployment

```bash
# Build and package
npm run dist:package

# Install the package
npm install ./mp-advance-solutions-1.0.0.tgz

# Start the application
npm start
```

### 2. Docker Container Deployment

```bash
# Build Docker image
docker build -t mp-advance-solutions .

# Run container
docker run -p 3000:3000 mp-advance-solutions
```

### 3. Docker Compose Deployment

```bash
# Deploy complete stack
docker-compose up -d

# View logs
docker-compose logs -f

# Stop deployment
docker-compose down
```

### 4. Standalone Deployment

```bash
# Create standalone package
./scripts/deploy.sh standalone

# Extract and deploy
tar -xzf mp-advance-solutions-*.tar.gz
cd dist-package
npm install --production
./start.sh
```

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file based on `.env.production`:

```bash
cp .env.production .env.local
# Edit .env.local with your configuration
```

Key variables:
- `NODE_ENV`: Environment (production/development)
- `PORT`: Application port (default: 3000)
- `DATABASE_URL`: Database connection string
- `SESSION_SECRET`: Session encryption key

### Production Optimizations

The production build includes:
- Code minification and optimization
- Asset compression
- Bundle splitting for better caching
- Source map generation disabled
- Environment-specific configurations

## 📁 File Structure

```
dist/
├── public/          # Frontend assets
│   ├── assets/      # Compiled CSS/JS
│   └── index.html   # Main HTML file
├── index.js         # Server entry point
└── shared/          # Shared utilities

dist-package/        # Standalone distribution
├── dist/           # Built application
├── package.json    # Dependencies
├── start.sh        # Startup script
└── README.md       # Distribution guide
```

## 🔍 Health Checks

The application includes health check endpoints:

- `GET /health` - Basic health check
- `GET /api/health` - API health check

Docker containers include automatic health checks.

## 🚨 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Change port in .env.local
   PORT=3001
   ```

2. **Database connection issues**
   ```bash
   # Check DATABASE_URL in .env.local
   # Ensure database is accessible
   ```

3. **Docker build fails**
   ```bash
   # Clean Docker cache
   docker system prune -a
   ```

### Logs

```bash
# Docker logs
docker logs <container_name>

# Docker Compose logs
docker-compose logs -f app

# Application logs
tail -f logs/app.log
```

## 🔒 Security Considerations

- Use strong `SESSION_SECRET` in production
- Enable HTTPS in production environments
- Configure firewall rules appropriately
- Regular security updates for dependencies
- Use environment variables for sensitive data

## 📊 Performance

### Production Optimizations

- Gzip compression enabled
- Static asset caching
- Bundle splitting for optimal loading
- Minified JavaScript and CSS
- Optimized images and fonts

### Monitoring

Consider implementing:
- Application performance monitoring (APM)
- Log aggregation
- Health check monitoring
- Resource usage tracking

## 🔄 Updates

### Rolling Updates

For zero-downtime deployments:

1. **Docker Compose**
   ```bash
   docker-compose up -d --no-deps app
   ```

2. **Kubernetes**
   ```bash
   kubectl set image deployment/mp-advance-solutions app=mp-advance-solutions:new-tag
   ```

### Backup Strategy

- Database backups before updates
- Configuration file backups
- Container image versioning

## 📞 Support

For deployment support:
- Check logs for error details
- Verify environment configuration
- Ensure all prerequisites are met
- Contact MP Advance Solutions support team

## 📝 License

This distribution package is proprietary to MP Advance Solutions.