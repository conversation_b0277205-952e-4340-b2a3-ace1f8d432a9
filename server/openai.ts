// OpenAI access disabled - using mock responses instead

// Function to generate a chat response (mock implementation)
export async function generateChatResponse(userMessage: string, chatHistory: Array<{ role: 'user' | 'assistant', content: string }> = []): Promise<string> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const lowerMessage = userMessage.toLowerCase();
  
  // Simple keyword-based responses for MP Advance Solutions services
  if (lowerMessage.includes('ai') || lowerMessage.includes('artificial intelligence') || lowerMessage.includes('machine learning')) {
    return "Our AI Solutions leverage advanced machine learning and predictive analytics to drive business innovation. We help companies optimize operations, enhance customer experiences, and unlock new growth opportunities through intelligent automation.";
  }
  
  if (lowerMessage.includes('ecommerce') || lowerMessage.includes('online store') || lowerMessage.includes('marketplace')) {
    return "We specialize in building powerful ecommerce solutions tailored to your business needs. Our services include custom online stores, marketplace integrations, payment processing, inventory management, and mobile-responsive designs for both retail and wholesale businesses.";
  }
  
  if (lowerMessage.includes('marketing') || lowerMessage.includes('seo') || lowerMessage.includes('social media')) {
    return "Our Digital Marketing services help you attract, engage, and convert customers through strategic campaigns. We offer SEO optimization, social media management, paid advertising, content marketing, and comprehensive analytics to maximize your ROI.";
  }
  
  if (lowerMessage.includes('leads') || lowerMessage.includes('lead generation') || lowerMessage.includes('prospects')) {
    return "Our Lead Generation services create qualified prospects through intelligent, automated funnels. We help you identify potential customers, nurture relationships, and convert leads into sales using data-driven strategies and marketing automation.";
  }
  
  if (lowerMessage.includes('automation') || lowerMessage.includes('workflow') || lowerMessage.includes('process')) {
    return "We offer comprehensive Business Automation and Process Management solutions to streamline your operations. Our services include workflow optimization, automated reporting, task management systems, and integration solutions to boost productivity and operational excellence.";
  }
  
  if (lowerMessage.includes('fulfillment') || lowerMessage.includes('shipping') || lowerMessage.includes('supply chain')) {
    return "Our Order Fulfillment services provide end-to-end supply chain solutions for efficient product delivery. We handle inventory management, order processing, shipping coordination, and tracking systems to ensure your customers receive their orders promptly.";
  }
  
  if (lowerMessage.includes('digital signage') || lowerMessage.includes('displays') || lowerMessage.includes('retail')) {
    return "Our Digital Signage solutions create engaging interactive displays for retail environments. We design and implement dynamic content management systems, customer engagement platforms, and analytics dashboards to enhance your in-store experience.";
  }
  
  if (lowerMessage.includes('pricing') || lowerMessage.includes('cost') || lowerMessage.includes('price')) {
    return "Our pricing is customized based on your specific needs and project scope. We offer competitive rates and flexible packages for all our services. I'd be happy to connect you with our sales team for a detailed quote tailored to your requirements.";
  }
 
  if (lowerMessage.includes('contact') || lowerMessage.includes('speak') || lowerMessage.includes('call')) {
    return "I'd be happy to connect you with our team! You can reach us through our contact page. What particular service or solution are you interested in learning more about?";
  }
  
  // Default response
  return "Thank you for your inquiry! MP Advance Solutions offers a comprehensive range of services including AI Solutions, Ecommerce Development, Digital Marketing, Lead Generation, Business Automation, Process Management, Order Fulfillment, and Digital Signage. How can I help you with your specific business needs today?";
}

// Function to determine the inquiry category (mock implementation)
export async function categorizeInquiry(userMessage: string): Promise<string> {
  const lowerMessage = userMessage.toLowerCase();
  
  if (lowerMessage.includes('ai') || lowerMessage.includes('artificial intelligence') || lowerMessage.includes('machine learning')) {
    return "AI_Solutions";
  }
  
  if (lowerMessage.includes('ecommerce') || lowerMessage.includes('online store') || lowerMessage.includes('marketplace')) {
    return "Ecommerce";
  }
  
  if (lowerMessage.includes('marketing') || lowerMessage.includes('seo') || lowerMessage.includes('social media')) {
    return "Digital_Marketing";
  }
  
  if (lowerMessage.includes('leads') || lowerMessage.includes('lead generation')) {
    return "Leads_Generation";
  }
  
  if (lowerMessage.includes('automation') || lowerMessage.includes('workflow')) {
    return "Business_Automation";
  }
  
  if (lowerMessage.includes('process') || lowerMessage.includes('management')) {
    return "Business_Process_Management";
  }
  
  if (lowerMessage.includes('fulfillment') || lowerMessage.includes('shipping')) {
    return "Order_Fulfillment";
  }
  
  if (lowerMessage.includes('digital signage') || lowerMessage.includes('displays')) {
    return "Digital_Signage";
  }
  
  if (lowerMessage.includes('pricing') || lowerMessage.includes('cost') || lowerMessage.includes('price')) {
    return "Pricing";
  }
  
  if (lowerMessage.includes('support') || lowerMessage.includes('help') || lowerMessage.includes('problem')) {
    return "Support_Request";
  }
  
  return "General_Inquiry";
}