import { Client } from '@microsoft/microsoft-graph-client';
import { ConfidentialClientApplication } from '@azure/msal-node';
import dotenv from 'dotenv';

dotenv.config();

interface EmailData {
    subject: string;
    body: string;
    toRecipients: string[];
    ccRecipients?: string[];
    importance?: 'low' | 'normal' | 'high';
}

export class GraphEmailSender {
    private msalConfig: any;
    private cca: ConfidentialClientApplication;
    private graphClient: any;

    constructor() {
        // Initialize MSAL instance
        this.msalConfig = {
            auth: {
                clientId: process.env.CLIENT_ID,
                clientSecret: process.env.CLIENT_SECRET,
                authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`
            }
        };
        
        this.cca = new ConfidentialClientApplication(this.msalConfig);
        this.graphClient = null;
    }

    async authenticate(): Promise<boolean> {
        try {
            // Get access token using client credentials flow
            const clientCredentialRequest = {
                scopes: ['https://graph.microsoft.com/.default'],
            };

            const response = await this.cca.acquireTokenByClientCredential(clientCredentialRequest);
            if (!response) {
                throw new Error('Failed to acquire access token');
            }
            const accessToken = response.accessToken;

            // Initialize Graph client with access token
            this.graphClient = Client.init({
                authProvider: (done) => {
                    done(null, accessToken);
                }
            });

            console.log('✅ Successfully authenticated with Microsoft Graph');
            return true;
        } catch (error) {
            console.error('❌ Authentication failed:', error instanceof Error ? error.message : String(error));
            return false;
        }
    }

    async sendEmail(emailData: EmailData): Promise<boolean> {
        if (!this.graphClient) {
            throw new Error('Not authenticated. Call authenticate() first.');
        }

        const email = {
            message: {
                subject: emailData.subject,
                body: {
                    contentType: 'HTML',
                    content: emailData.body
                },
                toRecipients: emailData.toRecipients.map(email => ({
                    emailAddress: {
                        address: email
                    }
                })),
                ccRecipients: emailData.ccRecipients ? emailData.ccRecipients.map(email => ({
                    emailAddress: {
                        address: email
                    }
                })) : [],
                importance: emailData.importance || 'normal'
            },
            saveToSentItems: true
        };

        try {
            await this.graphClient
                .api(`/users/${process.env.FROM_EMAIL}/sendMail`)
                .post(email);
            
            console.log('✅ Email sent successfully!');
            return true;
        } catch (error) {
            console.error('❌ Failed to send email:', error instanceof Error ? error.message : String(error));
            return false;
        }
    }
}
