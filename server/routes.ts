import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";

import { generateChatResponse, categorizeInquiry } from './openai';
import { GraphEmailSender } from './msgraph';

export async function registerRoutes(app: Express): Promise<Server> {
  // AI Chatbot API endpoint
  app.post('/api/chatbot', async (req, res) => {
    try {
      const { message, chatHistory } = req.body;
      
      if (!message) {
        return res.status(400).json({ error: 'Message is required' });
      }

      // Generate response using OpenAI
      const response = await generateChatResponse(message, chatHistory);
      
      // Categorize the inquiry (for analytics purposes)
      const category = await categorizeInquiry(message);
      
      res.json({ 
        response,
        category
      });
    } catch (error) {
      console.error('Error in chatbot API:', error);
      res.status(500).json({ 
        error: 'Failed to process request',
        message: 'Our AI assistant is temporarily unavailable. Please try again later.'
      });
    }
  });

  // Contact form API endpoint
  app.post('/api/contact', async (req, res) => {
    try {
      const { name, email, phone, company, subject, message } = req.body;

      // Validate required fields
      if (!name || !email || !subject || !message) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Name, email, subject, and message are required.'
        });
      }

      // Initialize Graph Email Sender
      const emailSender = new GraphEmailSender();

      // Authenticate with Microsoft Graph
      const authSuccess = await emailSender.authenticate();
      if (!authSuccess) {
        console.error('Failed to authenticate with Microsoft Graph');
        return res.status(500).json({
          error: 'Email service unavailable',
          message: 'Unable to send email at this time. Please try again later.'
        });
      }

      // Prepare email content
      const emailSubject = `Contact Form: ${subject}`;
      const emailBody = `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
        ${company ? `<p><strong>Company:</strong> ${company}</p>` : ''}
        <p><strong>Subject:</strong> ${subject}</p>
        <p><strong>Message:</strong></p>
        <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0;">
          ${message.replace(/\n/g, '<br>')}
        </div>
        <hr>
        <p><em>This message was sent from the MP Advance Solutions contact form at ${new Date().toLocaleString()}</em></p>
      `;

      // Prepare email data for Microsoft Graph
      const emailData = {
        subject: emailSubject,
        body: emailBody,
        toRecipients: [process.env.TO_EMAIL || '<EMAIL>'],
        ccRecipients: [],
        importance: 'normal' as const
      };

      // Send email using Microsoft Graph
      const emailSuccess = await emailSender.sendEmail(emailData);

      if (emailSuccess) {
        console.log(`✅ Contact form email sent from ${email} with subject: ${subject}`);
        res.json({
          success: true,
          message: 'Your message has been sent successfully!'
        });
      } else {
        console.error('Failed to send contact form email');
        res.status(500).json({
          error: 'Email sending failed',
          message: 'Unable to send your message. Please try again later.'
        });
      }
    } catch (error) {
      console.error('Error in contact API:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'An unexpected error occurred. Please try again later.'
      });
    }
  });

  // put application routes here
  // prefix all routes with /api

  // use storage to perform CRUD operations on the storage interface
  // e.g. storage.insertUser(user) or storage.getUserByUsername(username)

  const httpServer = createServer(app);

  return httpServer;
}
