import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";

import { generateChatResponse, categorizeInquiry } from './openai';

export async function registerRoutes(app: Express): Promise<Server> {
  // AI Chatbot API endpoint
  app.post('/api/chatbot', async (req, res) => {
    try {
      const { message, chatHistory } = req.body;
      
      if (!message) {
        return res.status(400).json({ error: 'Message is required' });
      }

      // Generate response using OpenAI
      const response = await generateChatResponse(message, chatHistory);
      
      // Categorize the inquiry (for analytics purposes)
      const category = await categorizeInquiry(message);
      
      res.json({ 
        response,
        category
      });
    } catch (error) {
      console.error('Error in chatbot API:', error);
      res.status(500).json({ 
        error: 'Failed to process request',
        message: 'Our AI assistant is temporarily unavailable. Please try again later.'
      });
    }
  });

  // put application routes here
  // prefix all routes with /api

  // use storage to perform CRUD operations on the storage interface
  // e.g. storage.insertUser(user) or storage.getUserByUsername(username)

  const httpServer = createServer(app);

  return httpServer;
}
