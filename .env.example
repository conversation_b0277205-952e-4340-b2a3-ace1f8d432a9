# Microsoft Graph API Configuration
# Required for contact form email functionality

# Azure App Registration Details
CLIENT_ID=your-azure-app-client-id
CLIENT_SECRET=your-azure-app-client-secret
TENANT_ID=your-azure-tenant-id

# Email Configuration
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual Azure app registration details
# 3. Make sure the FROM_EMAIL has permission to send emails via Microsoft Graph
# 4. The TO_EMAIL is where contact form submissions will be sent
